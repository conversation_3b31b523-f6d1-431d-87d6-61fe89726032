# 🚀 دليل إعداد ونشر Supabase لمشروع شُريح

هذا الدليل يشرح خطوات إعداد ونشر Supabase كقاعدة بيانات وخدمة مصادقة لمشروع شُريح.

## 📋 المتطلبات المسبقة

- حساب على [Supabase](https://supabase.com/)
- مشروع شُريح جاهز للربط مع Supabase

## 🔧 خطوات الإعداد

### 1. إنشاء مشروع Supabase

1. قم بتسجيل الدخول إلى [Supabase](https://app.supabase.com/)
2. انقر على "New Project"
3. أدخل اسم المشروع (مثل: "shuraih-production")
4. اختر كلمة مرور قوية لقاعدة البيانات
5. اختر المنطقة الأقرب إلى مستخدميك (مثل: eu-central-1 للشرق الأوسط وأوروبا)
6. ان<PERSON><PERSON> على "Create new project"

### 2. الحصول على بيانات الاتصال

بعد إنشاء المشروع، احصل على بيانات الاتصال التالية:

- **Project URL**: `https://your-project-ref.supabase.co`
- **API Key (anon public)**: مفتاح API العام
- **API Key (service_role)**: مفتاح API للخدمة (سري)
- **Database Connection String**: سلسلة اتصال قاعدة البيانات

احتفظ بهذه البيانات في مكان آمن، ستحتاجها لاحقًا.

### 3. تطبيق هجرات قاعدة البيانات

#### باستخدام Supabase CLI

1. قم بتثبيت Supabase CLI:
```bash
npm install -g supabase
```

2. قم بتسجيل الدخول:
```bash
supabase login
```

3. قم بربط المشروع:
```bash
supabase link --project-ref your-project-ref
```

4. قم بتطبيق الهجرات:
```bash
supabase db push
```

#### باستخدام SQL Editor

1. انتقل إلى "SQL Editor" في لوحة تحكم Supabase
2. انسخ والصق محتوى ملفات الهجرة من مجلد `supabase/migrations`
3. قم بتنفيذ الاستعلامات

### 4. إعداد المصادقة

1. انتقل إلى "Authentication" > "Providers" في لوحة تحكم Supabase
2. قم بتكوين إعدادات البريد الإلكتروني:
   - Email Auth: Enabled
   - Confirm emails: Enabled (للإنتاج)
   - Secure email change: Enabled
   - Double confirm changes: Enabled

3. قم بتكوين قوالب البريد الإلكتروني:
   - انتقل إلى "Authentication" > "Email Templates"
   - قم بتخصيص قوالب البريد الإلكتروني بما يتناسب مع هوية شُريح

4. (اختياري) قم بتكوين مزودي المصادقة الاجتماعية:
   - انتقل إلى "Authentication" > "Providers"
   - قم بتكوين Google, GitHub, أو أي مزود آخر

### 5. إعداد سياسات RLS (أمان مستوى الصف)

تأكد من تطبيق سياسات RLS المناسبة لكل جدول:

1. انتقل إلى "Table Editor" في لوحة تحكم Supabase
2. اختر الجدول الذي تريد تكوين سياسات RLS له
3. انتقل إلى تبويب "Policies"
4. قم بإنشاء السياسات المناسبة

مثال على سياسات RLS للجداول الرئيسية:

#### جدول profiles

```sql
-- السماح للمستخدمين بقراءة ملفاتهم الشخصية فقط
CREATE POLICY "Users can view their own profile"
ON profiles
FOR SELECT
USING (auth.uid() = id);

-- السماح للمستخدمين بتحديث ملفاتهم الشخصية فقط
CREATE POLICY "Users can update their own profile"
ON profiles
FOR UPDATE
USING (auth.uid() = id);

-- السماح للمسؤولين بقراءة جميع الملفات الشخصية
CREATE POLICY "Administrators can view all profiles"
ON profiles
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'administrator'
  )
);
```

#### جدول external_apis

```sql
-- السماح للمسؤولين بإدارة واجهات API
CREATE POLICY "Administrators can manage external APIs"
ON external_apis
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'administrator'
  )
);

-- السماح للمستخدمين بعرض واجهات API المتاحة لمستوى اشتراكهم
CREATE POLICY "Users can view APIs available for their plan level"
ON external_apis
FOR SELECT
USING (
  is_active = true
  AND (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND (
        -- المسؤول يمكنه رؤية جميع واجهات API
        profiles.role = 'administrator'
        OR
        -- المستخدمون يمكنهم رؤية واجهات API المتاحة لمستوى اشتراكهم
        CASE
          WHEN profiles.role = 'subscriber_enterprise' THEN true
          WHEN profiles.role = 'subscriber_premium' THEN plan_level <> 'enterprise'
          WHEN profiles.role = 'subscriber_basic' THEN plan_level = ANY (ARRAY['free', 'basic']::plan_level[])
          ELSE plan_level = 'free'
        END
      )
    )
  )
);
```

### 6. إعداد Storage

1. انتقل إلى "Storage" في لوحة تحكم Supabase
2. أنشئ دلائل جديدة:
   - `avatars`: لصور المستخدمين
   - `documents`: للمستندات القانونية
   - `chat_attachments`: لمرفقات المحادثة

3. قم بتكوين سياسات RLS لكل دليل:

#### سياسات دليل avatars

```sql
-- السماح للمستخدمين بقراءة جميع الصور
CREATE POLICY "Anyone can view avatars"
ON storage.objects
FOR SELECT
USING (bucket_id = 'avatars');

-- السماح للمستخدمين بتحميل صورهم الشخصية فقط
CREATE POLICY "Users can upload their own avatar"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'avatars'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- السماح للمستخدمين بتحديث صورهم الشخصية فقط
CREATE POLICY "Users can update their own avatar"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'avatars'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

### 7. إعداد Edge Functions (اختياري)

إذا كنت تستخدم Edge Functions:

1. انتقل إلى "Edge Functions" في لوحة تحكم Supabase
2. انقر على "Create Function"
3. قم بتحميل وظائف الحافة من مجلد `supabase/functions`

## 🔄 ربط التطبيق بـ Supabase

### 1. تحديث متغيرات البيئة في الواجهة الأمامية

قم بتحديث ملف `.env.production` في مجلد `frontend`:

```
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 2. تحديث متغيرات البيئة في الواجهة الخلفية

قم بتحديث ملف `.env` في مجلد `backend`:

```
DATABASE_URL=postgres://postgres:<EMAIL>:5432/postgres
```

## 📊 مراقبة وإدارة Supabase

### 1. مراقبة الاستخدام

1. انتقل إلى "Project Settings" > "Usage" في لوحة تحكم Supabase
2. راقب استخدام:
   - قاعدة البيانات
   - المصادقة
   - التخزين
   - Edge Functions

### 2. إعداد التنبيهات

1. انتقل إلى "Project Settings" > "Alerts" في لوحة تحكم Supabase
2. قم بإعداد تنبيهات لـ:
   - استخدام قاعدة البيانات
   - أخطاء المصادقة
   - استخدام التخزين

### 3. النسخ الاحتياطية

1. انتقل إلى "Project Settings" > "Database" > "Backups" في لوحة تحكم Supabase
2. قم بتكوين النسخ الاحتياطية التلقائية
3. قم بتنزيل نسخة احتياطية يدوية بشكل دوري

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل المصادقة

إذا واجهت مشاكل في المصادقة:

1. تحقق من سجلات المصادقة في "Authentication" > "Logs"
2. تأكد من تكوين البريد الإلكتروني بشكل صحيح
3. تحقق من قوالب البريد الإلكتروني

### مشاكل قاعدة البيانات

إذا واجهت مشاكل في قاعدة البيانات:

1. تحقق من سجلات قاعدة البيانات في "Database" > "Logs"
2. تحقق من سياسات RLS
3. تحقق من هجرات قاعدة البيانات

### مشاكل CORS

إذا واجهت مشاكل CORS:

1. انتقل إلى "Project Settings" > "API"
2. تحقق من إعدادات CORS
3. أضف مجالات التطبيق إلى قائمة "Additional allowed origins"

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
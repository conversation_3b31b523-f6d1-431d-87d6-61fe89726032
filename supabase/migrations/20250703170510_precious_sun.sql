/*
  # Payment Integration Schema

  1. New Tables
    - `subscription_packages` - Stores available subscription plans
    - `payments` - Records payment transactions
    - `invoices` - Stores invoice information for payments
  2. Security
    - Enable RLS on all tables
    - Add policies for proper access control
  3. Sample Data
    - Insert default subscription packages
*/

-- Create subscription_packages table
CREATE TABLE IF NOT EXISTS subscription_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  monthly_price FLOAT NOT NULL,
  yearly_price FLOAT,
  max_chats_per_month INTEGER NOT NULL,
  max_tokens_per_month INTEGER NOT NULL,
  features J<PERSON>N<PERSON> DEFAULT '[]'::jsonb,
  blocked_features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  is_default BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 1,
  color TEXT DEFAULT 'blue',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
  amount FLOAT NOT NULL,
  currency TEXT NOT NULL DEFAULT 'SAR',
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method TEXT NOT NULL,
  payment_id TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create invoices table if it doesn't exist
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  invoice_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  total_amount FLOAT NOT NULL,
  tax_amount FLOAT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'issued', 'paid', 'void')),
  notes TEXT,
  wafeq_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE subscription_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Create policies for subscription_packages (with safety checks)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'subscription_packages' AND policyname = 'Anyone can view active subscription packages'
  ) THEN
    CREATE POLICY "Anyone can view active subscription packages"
      ON subscription_packages
      FOR SELECT
      USING (is_active = TRUE);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'subscription_packages' AND policyname = 'Administrators can manage subscription packages'
  ) THEN
    CREATE POLICY "Administrators can manage subscription packages"
      ON subscription_packages
      FOR ALL
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'administrator'
        )
      );
  END IF;
END
$$;

-- Create policies for payments (with safety checks)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'payments' AND policyname = 'Users can view their own payments'
  ) THEN
    CREATE POLICY "Users can view their own payments"
      ON payments
      FOR SELECT
      USING (auth.uid() = user_id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'payments' AND policyname = 'Administrators can view all payments'
  ) THEN
    CREATE POLICY "Administrators can view all payments"
      ON payments
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'administrator'
        )
      );
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'payments' AND policyname = 'System can insert payments'
  ) THEN
    CREATE POLICY "System can insert payments"
      ON payments
      FOR INSERT
      WITH CHECK (true);
  END IF;
END
$$;

-- Create policies for invoices (with safety checks)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'invoices' AND policyname = 'Users can view invoices for their payments'
  ) THEN
    CREATE POLICY "Users can view invoices for their payments"
      ON invoices
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM payments
          WHERE payments.id = invoices.payment_id
          AND payments.user_id = auth.uid()
        )
      );
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'invoices' AND policyname = 'Administrators can view all invoices'
  ) THEN
    CREATE POLICY "Administrators can view all invoices"
      ON invoices
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'administrator'
        )
      );
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'invoices' AND policyname = 'System can insert invoices'
  ) THEN
    CREATE POLICY "System can insert invoices"
      ON invoices
      FOR INSERT
      WITH CHECK (true);
  END IF;
END
$$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

CREATE INDEX IF NOT EXISTS idx_invoices_payment_id ON invoices(payment_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);

-- Insert sample subscription packages (with safety check to avoid duplicates)
INSERT INTO subscription_packages (name, description, monthly_price, yearly_price, max_chats_per_month, max_tokens_per_month, features, blocked_features, is_active, is_default, sort_order, color)
SELECT 
  'المجانية', 'للمستخدمين الجدد', 0, NULL, 100, 5000, 
  '["النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
  '["المحادثة الصوتية", "رفع الملفات", "النماذج المتقدمة"]'::jsonb, 
  TRUE, TRUE, 1, 'default'
WHERE NOT EXISTS (
  SELECT 1 FROM subscription_packages WHERE name = 'المجانية'
);

INSERT INTO subscription_packages (name, description, monthly_price, yearly_price, max_chats_per_month, max_tokens_per_month, features, blocked_features, is_active, is_default, sort_order, color)
SELECT 
  'الأساسية', 'للاستخدام الشخصي', 199, 1990, 1000, 25000, 
  '["رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
  '["النماذج القانونية", "التحليل المتقدم"]'::jsonb, 
  TRUE, FALSE, 2, 'blue'
WHERE NOT EXISTS (
  SELECT 1 FROM subscription_packages WHERE name = 'الأساسية'
);

INSERT INTO subscription_packages (name, description, monthly_price, yearly_price, max_chats_per_month, max_tokens_per_month, features, blocked_features, is_active, is_default, sort_order, color)
SELECT 
  'المتقدمة', 'للمحترفين', 499, 4990, 5000, 100000, 
  '["المحادثة الصوتية", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
  '["النماذج المخصصة", "دعم 24/7"]'::jsonb, 
  TRUE, FALSE, 3, 'purple'
WHERE NOT EXISTS (
  SELECT 1 FROM subscription_packages WHERE name = 'المتقدمة'
);

INSERT INTO subscription_packages (name, description, monthly_price, yearly_price, max_chats_per_month, max_tokens_per_month, features, blocked_features, is_active, is_default, sort_order, color)
SELECT 
  'المؤسسية', 'للشركات والمؤسسات', 999, 9990, -1, -1, 
  '["جميع المزايا", "تخصيص النماذج", "المحادثة الصوتية", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
  '[]'::jsonb, 
  TRUE, FALSE, 4, 'gold'
WHERE NOT EXISTS (
  SELECT 1 FROM subscription_packages WHERE name = 'المؤسسية'
);
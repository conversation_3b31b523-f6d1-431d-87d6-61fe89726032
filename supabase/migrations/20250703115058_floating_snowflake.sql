/*
  # Token Usage Tracking System

  1. New Tables
    - `user_token_usage` - Tracks monthly token usage per user
    - `token_usage_notifications` - Tracks notifications sent to users about token usage

  2. Security
    - Enable RLS on both tables
    - Add policies for user access and admin access

  3. Changes
    - Add trigger to automatically create monthly usage records
    - Add trigger to send notifications when usage exceeds thresholds
*/

-- Create user_token_usage table to track monthly token usage
CREATE TABLE IF NOT EXISTS user_token_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  total_tokens_used BIGINT NOT NULL DEFAULT 0,
  tokens_limit BIGINT NOT NULL DEFAULT 5000,
  last_updated TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each user has only one record per month
  UNIQUE(user_id, month)
);

-- Create token_usage_notifications table to track notifications
CREATE TABLE IF NOT EXISTS token_usage_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  threshold_percentage INT NOT NULL, -- e.g., 50, 75, 90, 100
  sent_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each threshold is only notified once per user per month
  UNIQUE(user_id, month, threshold_percentage)
);

-- Enable Row Level Security
ALTER TABLE user_token_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_usage_notifications ENABLE ROW LEVEL SECURITY;

-- Create policies for user_token_usage
CREATE POLICY "Users can view their own token usage"
  ON user_token_usage
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all token usage"
  ON user_token_usage
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can update token usage"
  ON user_token_usage
  FOR ALL
  USING (true);

-- Create policies for token_usage_notifications
CREATE POLICY "Users can view their own notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert notifications"
  ON token_usage_notifications
  FOR INSERT
  WITH CHECK (true);

-- Create function to update token usage
CREATE OR REPLACE FUNCTION update_token_usage(
  p_user_id UUID,
  p_tokens INT
)
RETURNS VOID AS $$
DECLARE
  v_month VARCHAR(7);
  v_tokens_limit BIGINT;
  v_current_usage BIGINT;
  v_new_usage BIGINT;
  v_percentage INT;
  v_subscription_plan VARCHAR;
BEGIN
  -- Get current month in YYYY-MM format
  v_month := to_char(now(), 'YYYY-MM');
  
  -- Get user's subscription plan
  SELECT role INTO v_subscription_plan FROM profiles WHERE id = p_user_id;
  
  -- Determine token limit based on subscription plan
  CASE v_subscription_plan
    WHEN 'subscriber_enterprise' THEN v_tokens_limit := 500000;
    WHEN 'subscriber_premium' THEN v_tokens_limit := 100000;
    WHEN 'subscriber_basic' THEN v_tokens_limit := 25000;
    ELSE v_tokens_limit := 5000; -- Free plan
  END CASE;
  
  -- Insert or update the usage record
  INSERT INTO user_token_usage (user_id, month, total_tokens_used, tokens_limit, last_updated)
  VALUES (p_user_id, v_month, p_tokens, v_tokens_limit, now())
  ON CONFLICT (user_id, month) 
  DO UPDATE SET 
    total_tokens_used = user_token_usage.total_tokens_used + p_tokens,
    last_updated = now();
    
  -- Get the updated usage
  SELECT total_tokens_used INTO v_current_usage 
  FROM user_token_usage 
  WHERE user_id = p_user_id AND month = v_month;
  
  -- Calculate percentage
  v_percentage := (v_current_usage * 100) / v_tokens_limit;
  
  -- Check if we need to send notifications at various thresholds
  -- Only send if we've crossed the threshold with this update
  IF v_percentage >= 90 AND v_current_usage - p_tokens < (v_tokens_limit * 0.9) THEN
    -- Send 90% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 90)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
    
    -- Also insert into the general notifications table if it exists
    BEGIN
      INSERT INTO notifications (user_id, title, message, type, details, created_at)
      VALUES (
        p_user_id, 
        'تحذير استخدام', 
        'لقد وصلت إلى 90% من حد التوكينز الشهري المخصص لك',
        'warning',
        jsonb_build_object(
          'currentUsage', v_current_usage,
          'maxAllowed', v_tokens_limit,
          'resetDate', (date_trunc('month', now()) + interval '1 month - 1 day')::date
        ),
        now()
      );
    EXCEPTION WHEN undefined_table THEN
      -- Notifications table might not exist, ignore
    END;
    
  ELSIF v_percentage >= 75 AND v_current_usage - p_tokens < (v_tokens_limit * 0.75) THEN
    -- Send 75% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 75)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
    
  ELSIF v_percentage >= 50 AND v_current_usage - p_tokens < (v_tokens_limit * 0.5) THEN
    -- Send 50% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 50)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
  END IF;
  
  -- If usage exceeds 100%, we could implement rate limiting here
  IF v_percentage >= 100 THEN
    -- Log that the user has exceeded their limit
    -- In a real implementation, you might set a flag in the user's profile
    -- or implement rate limiting in the application
    NULL;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX idx_user_token_usage_user_id ON user_token_usage(user_id);
CREATE INDEX idx_user_token_usage_month ON user_token_usage(month);
CREATE INDEX idx_token_usage_notifications_user_id ON token_usage_notifications(user_id);
CREATE INDEX idx_token_usage_notifications_month ON token_usage_notifications(month);

-- Insert some sample data for testing
INSERT INTO user_token_usage (user_id, month, total_tokens_used, tokens_limit)
SELECT 
  id, 
  '2024-07', 
  CASE 
    WHEN role = 'subscriber_enterprise' THEN 180000
    WHEN role = 'subscriber_premium' THEN 92000
    WHEN role = 'subscriber_basic' THEN 22500
    ELSE 4800
  END,
  CASE 
    WHEN role = 'subscriber_enterprise' THEN 500000
    WHEN role = 'subscriber_premium' THEN 100000
    WHEN role = 'subscriber_basic' THEN 25000
    ELSE 5000
  END
FROM profiles
ON CONFLICT (user_id, month) DO NOTHING;
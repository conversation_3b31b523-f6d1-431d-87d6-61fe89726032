/*
  # Create external APIs table with proper enums

  1. New Tables
    - `external_apis` - Stores information about external AI models and services
      - `id` (uuid, primary key)
      - `name` (text)
      - `endpoint` (text)
      - `type` (api_type enum)
      - `config` (jsonb)
      - `api_key` (text)
      - `plan_level` (plan_level enum)
      - `is_active` (boolean)
      - `created_at` (timestamptz)
  
  2. Security
    - Enable RLS on `external_apis` table
    - Add policies for administrators and users based on plan level
*/

-- Create enum types for API types and plan levels
DO $$ BEGIN
  CREATE TYPE api_type AS ENUM ('llm', 'speech', 'vision', 'analysis', 'translation');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  CREATE TYPE plan_level AS ENUM ('free', 'basic', 'premium', 'enterprise');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Create external_apis table
CREATE TABLE IF NOT EXISTS external_apis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  endpoint TEXT NOT NULL,
  type api_type NOT NULL,
  config JSONB DEFAULT '{}'::jsonb,
  api_key TEXT,
  plan_level plan_level NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_external_apis_type ON external_apis(type);
CREATE INDEX IF NOT EXISTS idx_external_apis_plan_level ON external_apis(plan_level);
CREATE INDEX IF NOT EXISTS idx_external_apis_is_active ON external_apis(is_active);

-- Enable Row Level Security
ALTER TABLE external_apis ENABLE ROW LEVEL SECURITY;

-- Create policy for administrators to manage external APIs
CREATE POLICY "Administrators can manage external APIs"
  ON external_apis
  FOR ALL
  TO public
  USING (EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'administrator'
  ));

-- Create policy for read access based on plan level
CREATE POLICY "Users can view APIs available for their plan level"
  ON external_apis
  FOR SELECT
  TO public
  USING (
    is_active = true
    AND (
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE profiles.id = auth.uid()
        AND (
          -- Administrator can see all APIs
          profiles.role = 'administrator'
          OR
          -- Users can see APIs available for their plan level
          CASE
            WHEN profiles.role = 'subscriber_enterprise' THEN true
            WHEN profiles.role = 'subscriber_premium' THEN plan_level <> 'enterprise'
            WHEN profiles.role = 'subscriber_basic' THEN plan_level = ANY (ARRAY['free', 'basic']::plan_level[])
            ELSE plan_level = 'free'
          END
        )
      )
    )
  );

-- Insert some initial data for testing
INSERT INTO external_apis (name, endpoint, type, config, plan_level, is_active)
VALUES
  ('Llama 3.2 (8B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.7, "max_tokens": 2000, "system_prompt": "You are a helpful assistant."}'::jsonb, 
   'free', true),
  
  ('Qwen 2.5 (7B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.5, "max_tokens": 2000, "system_prompt": "You are a helpful assistant specialized in Arabic language."}'::jsonb, 
   'basic', true),
  
  ('Legal-Llama (7B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.2, "max_tokens": 2000, "system_prompt": "You are a legal assistant specialized in Saudi law."}'::jsonb, 
   'premium', true),
  
  ('Whisper.cpp', 'http://localhost:8080/api/speech-to-text', 'speech', 
   '{"sample_rate": 16000, "language": "ar"}'::jsonb, 
   'basic', true),
  
  ('XTTS2', 'http://localhost:8080/api/text-to-speech', 'speech', 
   '{"voice": "ar_female", "speed": 1.0}'::jsonb, 
   'premium', true),
  
  ('Deepseek-R1 (8B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.3, "max_tokens": 4000, "system_prompt": "You are an analytical assistant specialized in reasoning."}'::jsonb, 
   'enterprise', true),
  
  ('M2M-100', 'http://localhost:8080/api/translate', 'translation', 
   '{"source_lang": "auto", "target_lang": "ar"}'::jsonb, 
   'premium', true);
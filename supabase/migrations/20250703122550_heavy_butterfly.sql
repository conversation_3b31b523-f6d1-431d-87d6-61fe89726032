-- ======================================================
-- شُريح - ملف شامل لقاعدة البيانات
-- ======================================================
-- هذا الملف يحتوي على جميع مكونات قاعدة البيانات للمشروع
-- بما في ذلك الجداول والأنواع والوظائف والسياسات
-- ======================================================

-- ======================================================
-- إنشاء الأنواع المخصصة (ENUM TYPES)
-- ======================================================

-- إنشاء نوع api_type للواجهات الخارجية
DO $$ BEGIN
  CREATE TYPE api_type AS ENUM ('llm', 'speech', 'vision', 'analysis', 'translation');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع plan_level لمستويات الاشتراك
DO $$ BEGIN
  CREATE TYPE plan_level AS ENUM ('free', 'basic', 'premium', 'enterprise');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع user_role لأدوار المستخدمين
DO $$ BEGIN
  CREATE TYPE user_role AS ENUM (
    'administrator',     -- مسؤول
    'moderator',         -- مشرف
    'subscriber_enterprise', -- مشترك مؤسسي
    'subscriber_premium',    -- مشترك متقدم
    'subscriber_basic',      -- مشترك أساسي
    'user'               -- مستخدم عادي
  );
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع sender_type لنوع المرسل في المحادثات
DO $$ BEGIN
  CREATE TYPE sender_type AS ENUM ('user', 'system');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع subscription_status لحالة الاشتراك
DO $$ BEGIN
  CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'expired', 'past_due');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع payment_status لحالة الدفع
DO $$ BEGIN
  CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- إنشاء نوع invoice_status لحالة الفاتورة
DO $$ BEGIN
  CREATE TYPE invoice_status AS ENUM ('draft', 'issued', 'paid', 'void');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- ======================================================
-- إنشاء الجداول الرئيسية
-- ======================================================

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  hashed_password TEXT NOT NULL,
  full_name TEXT NOT NULL,
  role user_role DEFAULT 'user',
  is_active BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  last_login TIMESTAMPTZ
);

-- جدول الملفات الشخصية
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT NOT NULL,
  role user_role NOT NULL,
  avatar_url TEXT,
  phone TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  last_login TIMESTAMPTZ
);

-- جدول رموز تحديث الجلسة
CREATE TABLE IF NOT EXISTS refresh_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  token TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  is_revoked BOOLEAN DEFAULT FALSE
);

-- جدول جلسات المحادثة
CREATE TABLE IF NOT EXISTS chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  is_archived BOOLEAN DEFAULT FALSE,
  is_flagged BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الوسوم
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الاستشهادات
CREATE TABLE IF NOT EXISTS citations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  source TEXT NOT NULL,
  source_url TEXT,
  article_number TEXT,
  section TEXT,
  category TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- جدول رسائل المحادثة
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
  sender_type sender_type NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  is_flagged BOOLEAN DEFAULT FALSE
);

-- جدول مرفقات المحادثة
CREATE TABLE IF NOT EXISTS chat_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size TEXT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الاشتراكات
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  plan plan_level NOT NULL,
  status subscription_status NOT NULL,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
  amount FLOAT NOT NULL,
  currency TEXT NOT NULL DEFAULT 'SAR',
  status payment_status NOT NULL,
  payment_method TEXT NOT NULL,
  payment_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  invoice_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  total_amount FLOAT NOT NULL,
  tax_amount FLOAT NOT NULL,
  status invoice_status NOT NULL,
  notes TEXT,
  wafeq_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول سجلات التدقيق
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  resource TEXT NOT NULL,
  resource_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- جدول تقارير المحتوى
CREATE TABLE IF NOT EXISTS content_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id UUID REFERENCES users(id) ON DELETE SET NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT NOT NULL,
  reason TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  reviewed_by UUID REFERENCES users(id) ON DELETE SET NULL,
  reviewed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- جدول استشهادات الرسائل
CREATE TABLE IF NOT EXISTS message_citations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  citation_id UUID NOT NULL REFERENCES citations(id) ON DELETE CASCADE,
  start_index TEXT,
  end_index TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- جدول واجهات API الخارجية
CREATE TABLE IF NOT EXISTS external_apis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  endpoint TEXT NOT NULL,
  type api_type NOT NULL,
  config JSONB DEFAULT '{}'::jsonb,
  api_key TEXT,
  plan_level plan_level NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- جدول استخدام التوكينز
CREATE TABLE IF NOT EXISTS user_token_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  total_tokens_used BIGINT NOT NULL DEFAULT 0,
  tokens_limit BIGINT NOT NULL DEFAULT 5000,
  last_updated TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each user has only one record per month
  UNIQUE(user_id, month)
);

-- جدول إشعارات استخدام التوكينز
CREATE TABLE IF NOT EXISTS token_usage_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  threshold_percentage INT NOT NULL, -- e.g., 50, 75, 90, 100
  sent_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each threshold is only notified once per user per month
  UNIQUE(user_id, month, threshold_percentage)
);

-- ======================================================
-- إنشاء الجداول الوسيطة (العلاقات متعددة-متعددة)
-- ======================================================

-- جدول العلاقة بين جلسات المحادثة والوسوم
CREATE TABLE IF NOT EXISTS chat_session_tags (
  chat_session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (chat_session_id, tag_id)
);

-- جدول العلاقة بين الاستشهادات والوسوم
CREATE TABLE IF NOT EXISTS citation_tags (
  citation_id UUID REFERENCES citations(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (citation_id, tag_id)
);

-- ======================================================
-- إنشاء الفهارس لتحسين الأداء
-- ======================================================

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- فهارس جدول الملفات الشخصية
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

-- فهارس جدول رموز التحديث
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);

-- فهارس جدول جلسات المحادثة
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_created_at ON chat_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_is_archived ON chat_sessions(is_archived);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_is_flagged ON chat_sessions(is_flagged);

-- فهارس جدول رسائل المحادثة
CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_session_id ON chat_messages(chat_session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_is_flagged ON chat_messages(is_flagged);

-- فهارس جدول الاستشهادات
CREATE INDEX IF NOT EXISTS idx_citations_category ON citations(category);
CREATE INDEX IF NOT EXISTS idx_citations_article_number ON citations(article_number);
CREATE INDEX IF NOT EXISTS idx_citations_created_by ON citations(created_by);

-- فهارس جدول الاشتراكات
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_plan ON subscriptions(plan);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);

-- فهارس جدول المدفوعات
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- فهارس جدول سجلات التدقيق
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- فهارس جدول واجهات API الخارجية
CREATE INDEX IF NOT EXISTS idx_external_apis_type ON external_apis(type);
CREATE INDEX IF NOT EXISTS idx_external_apis_plan_level ON external_apis(plan_level);
CREATE INDEX IF NOT EXISTS idx_external_apis_is_active ON external_apis(is_active);

-- فهارس جدول استخدام التوكينز
CREATE INDEX IF NOT EXISTS idx_user_token_usage_user_id ON user_token_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_token_usage_month ON user_token_usage(month);

-- فهارس جدول إشعارات استخدام التوكينز
CREATE INDEX IF NOT EXISTS idx_token_usage_notifications_user_id ON token_usage_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_notifications_month ON token_usage_notifications(month);

-- ======================================================
-- إنشاء الوظائف (FUNCTIONS)
-- ======================================================

-- وظيفة تحديث حقل updated_at
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- وظيفة تحديث استخدام التوكينز
CREATE OR REPLACE FUNCTION update_token_usage(
  p_user_id UUID,
  p_tokens INT
)
RETURNS VOID AS $$
DECLARE
  v_month VARCHAR(7);
  v_tokens_limit BIGINT;
  v_current_usage BIGINT;
  v_new_usage BIGINT;
  v_percentage INT;
  v_subscription_plan VARCHAR;
BEGIN
  -- Get current month in YYYY-MM format
  v_month := to_char(now(), 'YYYY-MM');
  
  -- Get user's subscription plan
  SELECT role INTO v_subscription_plan FROM profiles WHERE id = p_user_id;
  
  -- Determine token limit based on subscription plan
  CASE v_subscription_plan
    WHEN 'subscriber_enterprise' THEN v_tokens_limit := 500000;
    WHEN 'subscriber_premium' THEN v_tokens_limit := 100000;
    WHEN 'subscriber_basic' THEN v_tokens_limit := 25000;
    ELSE v_tokens_limit := 5000; -- Free plan
  END CASE;
  
  -- Insert or update the usage record
  INSERT INTO user_token_usage (user_id, month, total_tokens_used, tokens_limit, last_updated)
  VALUES (p_user_id, v_month, p_tokens, v_tokens_limit, now())
  ON CONFLICT (user_id, month) 
  DO UPDATE SET 
    total_tokens_used = user_token_usage.total_tokens_used + p_tokens,
    last_updated = now();
    
  -- Get the updated usage
  SELECT total_tokens_used INTO v_current_usage 
  FROM user_token_usage 
  WHERE user_id = p_user_id AND month = v_month;
  
  -- Calculate percentage
  v_percentage := (v_current_usage * 100) / v_tokens_limit;
  
  -- Check if we need to send notifications at various thresholds
  -- Only send if we've crossed the threshold with this update
  IF v_percentage >= 90 AND v_current_usage - p_tokens < (v_tokens_limit * 0.9) THEN
    -- Send 90% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 90)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
    
    -- Also insert into the general notifications table if it exists
    BEGIN
      INSERT INTO notifications (user_id, title, message, type, details, created_at)
      VALUES (
        p_user_id, 
        'تحذير استخدام', 
        'لقد وصلت إلى 90% من حد التوكينز الشهري المخصص لك',
        'warning',
        jsonb_build_object(
          'currentUsage', v_current_usage,
          'maxAllowed', v_tokens_limit,
          'resetDate', (date_trunc('month', now()) + interval '1 month - 1 day')::date
        ),
        now()
      );
    EXCEPTION WHEN undefined_table THEN
      -- Notifications table might not exist, ignore
    END;
    
  ELSIF v_percentage >= 75 AND v_current_usage - p_tokens < (v_tokens_limit * 0.75) THEN
    -- Send 75% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 75)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
    
  ELSIF v_percentage >= 50 AND v_current_usage - p_tokens < (v_tokens_limit * 0.5) THEN
    -- Send 50% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 50)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
  END IF;
  
  -- If usage exceeds 100%, we could implement rate limiting here
  IF v_percentage >= 100 THEN
    -- Log that the user has exceeded their limit
    -- In a real implementation, you might set a flag in the user's profile
    -- or implement rate limiting in the application
    NULL;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- وظيفة التعامل مع المستخدمين الجدد
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- إنشاء ملف شخصي للمستخدم الجديد
  INSERT INTO profiles (id, email, full_name, role)
  VALUES (NEW.id, NEW.email, NEW.full_name, NEW.role);
  
  -- إنشاء اشتراك مجاني للمستخدم الجديد
  INSERT INTO subscriptions (
    user_id, 
    plan, 
    status, 
    start_date, 
    end_date, 
    auto_renew
  )
  VALUES (
    NEW.id, 
    'free', 
    'active', 
    now(), 
    now() + interval '1 year', 
    true
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- وظيفة تسجيل أحداث المصادقة
CREATE OR REPLACE FUNCTION log_auth_event()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id, 
    action, 
    resource, 
    resource_id, 
    details
  )
  VALUES (
    NEW.id, 
    CASE 
      WHEN TG_OP = 'INSERT' THEN 'user_created'
      WHEN TG_OP = 'UPDATE' THEN 'user_updated'
      ELSE 'user_action'
    END,
    'users',
    NEW.id,
    jsonb_build_object(
      'email', NEW.email,
      'role', NEW.role,
      'is_active', NEW.is_active
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ======================================================
-- إنشاء المشغلات (TRIGGERS)
-- ======================================================

-- مشغل تحديث حقل updated_at في جدول المستخدمين
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول الملفات الشخصية
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول جلسات المحادثة
CREATE TRIGGER update_chat_sessions_updated_at
BEFORE UPDATE ON chat_sessions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول الاستشهادات
CREATE TRIGGER update_citations_updated_at
BEFORE UPDATE ON citations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول الاشتراكات
CREATE TRIGGER update_subscriptions_updated_at
BEFORE UPDATE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول المدفوعات
CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل تحديث حقل updated_at في جدول الفواتير
CREATE TRIGGER update_invoices_updated_at
BEFORE UPDATE ON invoices
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- مشغل إنشاء ملف شخصي للمستخدم الجديد
CREATE TRIGGER handle_new_user_trigger
AFTER INSERT ON users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user();

-- مشغل تسجيل أحداث المصادقة
CREATE TRIGGER log_auth_event_trigger
AFTER INSERT OR UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION log_auth_event();

-- ======================================================
-- تمكين أمان مستوى الصف (RLS)
-- ======================================================

-- تمكين RLS لجدول المستخدمين
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول الملفات الشخصية
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول جلسات المحادثة
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول رسائل المحادثة
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول الاستشهادات
ALTER TABLE citations ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول الاشتراكات
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول المدفوعات
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول الفواتير
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول سجلات التدقيق
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول تقارير المحتوى
ALTER TABLE content_reports ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول واجهات API الخارجية
ALTER TABLE external_apis ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول استخدام التوكينز
ALTER TABLE user_token_usage ENABLE ROW LEVEL SECURITY;

-- تمكين RLS لجدول إشعارات استخدام التوكينز
ALTER TABLE token_usage_notifications ENABLE ROW LEVEL SECURITY;

-- ======================================================
-- إنشاء سياسات أمان مستوى الصف (RLS POLICIES)
-- ======================================================

-- سياسات جدول الملفات الشخصية
CREATE POLICY "Users can view their own profile"
  ON profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Administrators can view all profiles"
  ON profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول جلسات المحادثة
CREATE POLICY "Users can view their own chat sessions"
  ON chat_sessions
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own chat sessions"
  ON chat_sessions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own chat sessions"
  ON chat_sessions
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own chat sessions"
  ON chat_sessions
  FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all chat sessions"
  ON chat_sessions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول رسائل المحادثة
CREATE POLICY "Users can view messages in their chat sessions"
  ON chat_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM chat_sessions
      WHERE chat_sessions.id = chat_messages.chat_session_id
      AND chat_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create messages in their chat sessions"
  ON chat_messages
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_sessions
      WHERE chat_sessions.id = chat_messages.chat_session_id
      AND chat_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Administrators can view all messages"
  ON chat_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول الاستشهادات
CREATE POLICY "Users can view all citations"
  ON citations
  FOR SELECT
  USING (true);

CREATE POLICY "Administrators can manage citations"
  ON citations
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول الاشتراكات
CREATE POLICY "Users can view their own subscriptions"
  ON subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all subscriptions"
  ON subscriptions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "Administrators can manage subscriptions"
  ON subscriptions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول المدفوعات
CREATE POLICY "Users can view their own payments"
  ON payments
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all payments"
  ON payments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول الفواتير
CREATE POLICY "Users can view invoices for their payments"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM payments
      WHERE payments.id = invoices.payment_id
      AND payments.user_id = auth.uid()
    )
  );

CREATE POLICY "Administrators can view all invoices"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول سجلات التدقيق
CREATE POLICY "Administrators can view audit logs"
  ON audit_logs
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert audit logs"
  ON audit_logs
  FOR INSERT
  WITH CHECK (true);

-- سياسات جدول تقارير المحتوى
CREATE POLICY "Users can view their own reports"
  ON content_reports
  FOR SELECT
  USING (auth.uid() = reporter_id);

CREATE POLICY "Users can create reports"
  ON content_reports
  FOR INSERT
  WITH CHECK (auth.uid() = reporter_id);

CREATE POLICY "Moderators and administrators can view all reports"
  ON content_reports
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND (profiles.role = 'administrator' OR profiles.role = 'moderator')
    )
  );

CREATE POLICY "Moderators and administrators can update reports"
  ON content_reports
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND (profiles.role = 'administrator' OR profiles.role = 'moderator')
    )
  );

-- سياسات جدول واجهات API الخارجية
CREATE POLICY "Administrators can manage external APIs"
  ON external_apis
  FOR ALL
  TO public
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "Users can view APIs available for their plan level"
  ON external_apis
  FOR SELECT
  TO public
  USING (
    is_active = true
    AND (
      EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (
          -- Administrator can see all APIs
          profiles.role = 'administrator'
          OR
          -- Users can see APIs available for their plan level
          CASE
            WHEN profiles.role = 'subscriber_enterprise' THEN true
            WHEN profiles.role = 'subscriber_premium' THEN plan_level <> 'enterprise'
            WHEN profiles.role = 'subscriber_basic' THEN plan_level = ANY (ARRAY['free', 'basic']::plan_level[])
            ELSE plan_level = 'free'
          END
        )
      )
    )
  );

-- سياسات جدول استخدام التوكينز
CREATE POLICY "Users can view their own token usage"
  ON user_token_usage
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all token usage"
  ON user_token_usage
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can update token usage"
  ON user_token_usage
  FOR ALL
  USING (true);

-- سياسات جدول إشعارات استخدام التوكينز
CREATE POLICY "Users can view their own notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert notifications"
  ON token_usage_notifications
  FOR INSERT
  WITH CHECK (true);

-- ======================================================
-- إدخال بيانات أولية للاختبار
-- ======================================================

-- إدخال بيانات واجهات API الخارجية
INSERT INTO external_apis (name, endpoint, type, config, plan_level, is_active)
VALUES
  ('Llama 3.2 (8B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.7, "max_tokens": 2000, "system_prompt": "You are a helpful assistant."}'::jsonb, 
   'free', true),
  
  ('Qwen 2.5 (7B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.5, "max_tokens": 2000, "system_prompt": "You are a helpful assistant specialized in Arabic language."}'::jsonb, 
   'basic', true),
  
  ('Legal-Llama (7B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.2, "max_tokens": 2000, "system_prompt": "You are a legal assistant specialized in Saudi law."}'::jsonb, 
   'premium', true),
  
  ('Whisper.cpp', 'http://localhost:8080/api/speech-to-text', 'speech', 
   '{"sample_rate": 16000, "language": "ar"}'::jsonb, 
   'basic', true),
  
  ('XTTS2', 'http://localhost:8080/api/text-to-speech', 'speech', 
   '{"voice": "ar_female", "speed": 1.0}'::jsonb, 
   'premium', true),
  
  ('Deepseek-R1 (8B)', 'http://localhost:11434/api/generate', 'llm', 
   '{"temperature": 0.3, "max_tokens": 4000, "system_prompt": "You are an analytical assistant specialized in reasoning."}'::jsonb, 
   'enterprise', true),
  
  ('M2M-100', 'http://localhost:8080/api/translate', 'translation', 
   '{"source_lang": "auto", "target_lang": "ar"}'::jsonb, 
   'premium', true)
ON CONFLICT DO NOTHING;

-- ======================================================
-- تعليقات توضيحية للجداول
-- ======================================================

COMMENT ON TABLE users IS 'جدول المستخدمين الأساسي';
COMMENT ON TABLE profiles IS 'الملفات الشخصية للمستخدمين مع معلومات إضافية';
COMMENT ON TABLE refresh_tokens IS 'رموز تحديث الجلسة للمصادقة';
COMMENT ON TABLE chat_sessions IS 'جلسات المحادثة بين المستخدمين والنظام';
COMMENT ON TABLE chat_messages IS 'رسائل المحادثة داخل جلسات المحادثة';
COMMENT ON TABLE chat_attachments IS 'مرفقات المحادثة مثل الملفات والصور';
COMMENT ON TABLE tags IS 'الوسوم المستخدمة لتصنيف المحادثات والاستشهادات';
COMMENT ON TABLE citations IS 'الاستشهادات القانونية المستخدمة في الردود';
COMMENT ON TABLE subscriptions IS 'اشتراكات المستخدمين في الخدمة';
COMMENT ON TABLE payments IS 'سجلات المدفوعات للاشتراكات';
COMMENT ON TABLE invoices IS 'الفواتير المرتبطة بالمدفوعات';
COMMENT ON TABLE audit_logs IS 'سجلات التدقيق لتتبع الأنشطة في النظام';
COMMENT ON TABLE content_reports IS 'تقارير المحتوى المبلغ عنه من قبل المستخدمين';
COMMENT ON TABLE message_citations IS 'ربط الاستشهادات برسائل المحادثة';
COMMENT ON TABLE external_apis IS 'واجهات API الخارجية المستخدمة في النظام';
COMMENT ON TABLE user_token_usage IS 'تتبع استخدام التوكينز الشهري للمستخدمين';
COMMENT ON TABLE token_usage_notifications IS 'إشعارات استخدام التوكينز عند تجاوز العتبات';

-- ======================================================
-- شرح الأدوار والصلاحيات
-- ======================================================

COMMENT ON TYPE user_role IS '
أدوار المستخدمين في النظام:
- administrator: المسؤول - لديه صلاحيات كاملة على النظام
- moderator: المشرف - يمكنه مراجعة المحتوى والتقارير
- subscriber_enterprise: مشترك مؤسسي - لديه وصول إلى جميع الميزات والنماذج
- subscriber_premium: مشترك متقدم - لديه وصول إلى معظم الميزات والنماذج
- subscriber_basic: مشترك أساسي - لديه وصول إلى الميزات الأساسية
- user: مستخدم عادي - لديه وصول محدود للنظام
';

-- ======================================================
-- حدود الاستخدام حسب الدور
-- ======================================================

COMMENT ON TYPE user_role IS '
حدود الاستخدام حسب الدور:

1. مستخدم عادي (user):
   - حد التوكينز الشهري: 5,000
   - الحد الأقصى للاستعلامات اليومية: 10
   - رفع الملفات: غير متاح
   - المحادثة الصوتية: غير متاحة
   - النماذج المتاحة: النماذج الأساسية فقط

2. مشترك أساسي (subscriber_basic):
   - حد التوكينز الشهري: 25,000
   - الحد الأقصى للاستعلامات اليومية: 100
   - رفع الملفات: متاح (حتى 10 ميجابايت)
   - المحادثة الصوتية: متاحة
   - النماذج المتاحة: النماذج الأساسية والمتوسطة

3. مشترك متقدم (subscriber_premium):
   - حد التوكينز الشهري: 100,000
   - الحد الأقصى للاستعلامات اليومية: 500
   - رفع الملفات: متاح (حتى 50 ميجابايت)
   - المحادثة الصوتية: متاحة
   - النماذج المتاحة: جميع النماذج

4. مشترك مؤسسي (subscriber_enterprise):
   - حد التوكينز الشهري: 500,000
   - الحد الأقصى للاستعلامات اليومية: غير محدود
   - رفع الملفات: متاح (حتى 500 ميجابايت)
   - المحادثة الصوتية: متاحة
   - النماذج المتاحة: جميع النماذج + نماذج مخصصة

5. مشرف (moderator):
   - حد التوكينز الشهري: غير محدود
   - الحد الأقصى للاستعلامات اليومية: غير محدود
   - رفع الملفات: متاح (حتى 100 ميجابايت)
   - المحادثة الصوتية: متاحة
   - النماذج المتاحة: جميع النماذج
   - صلاحيات إضافية: مراجعة المحتوى والتقارير

6. مسؤول (administrator):
   - حد التوكينز الشهري: غير محدود
   - الحد الأقصى للاستعلامات اليومية: غير محدود
   - رفع الملفات: غير محدود
   - المحادثة الصوتية: متاحة
   - النماذج المتاحة: جميع النماذج
   - صلاحيات إضافية: إدارة كاملة للنظام
';
/*
  # Fix User Creation Issues

  1. Changes
     - Fix handle_new_user function to better handle errors
     - Add proper RLS policies for system-level profile creation
     - Add additional error logging
     - Ensure proper transaction handling
*/

-- Improve the handle_new_user function with better error handling and logging
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  _role TEXT;
BEGIN
  -- Check if profile already exists to avoid duplicates
  IF EXISTS (SELECT 1 FROM profiles WHERE id = NEW.id) THEN
    RETURN NEW;
  END IF;

  -- Set default role if not provided
  _role := COALESCE(NEW.raw_user_meta_data->>'role', 'user');
  
  -- Validate role
  IF _role NOT IN ('administrator', 'moderator', 'subscriber_enterprise', 'subscriber_premium', 'subscriber_basic', 'user') THEN
    _role := 'user';
  END IF;

  -- Insert the new profile with error handling
  BEGIN
    INSERT INTO profiles (
      id, 
      email, 
      full_name, 
      role
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
      _role
    );
  EXCEPTION WHEN OTHERS THEN
    -- Log the error but don't fail the transaction
    INSERT INTO audit_log (
      action, 
      resource, 
      resource_id, 
      details
    ) VALUES (
      'profile_creation_error',
      'profiles',
      NEW.id::text,
      jsonb_build_object(
        'error', SQLERRM, 
        'email', NEW.email,
        'stack', format('%s: %s', SQLSTATE, SQLERRM)
      )
    );
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user();

-- Ensure all necessary RLS policies exist with correct syntax
-- First, drop any existing policies with the same name to avoid conflicts
DO $$ 
BEGIN
  -- Drop the policy if it exists
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'System can insert profiles'
  ) THEN
    DROP POLICY "System can insert profiles" ON profiles;
  END IF;
END $$;

-- Create the policy with the correct syntax for INSERT
CREATE POLICY "System can insert profiles"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Ensure the policy for users to insert their own profile exists
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can insert their own profile during registration'
  ) THEN
    CREATE POLICY "Users can insert their own profile during registration"
      ON profiles
      FOR INSERT
      TO authenticated
      WITH CHECK (auth.uid() = id);
  END IF;
END $$;

-- Add a more detailed audit log for user creation attempts
CREATE OR REPLACE FUNCTION log_user_creation_attempt()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_log (
    action, 
    resource, 
    resource_id, 
    details
  ) VALUES (
    'user_creation_attempt',
    'auth.users',
    NEW.id::text,
    jsonb_build_object(
      'email', NEW.email,
      'created_at', NOW(),
      'metadata', NEW.raw_user_meta_data
    )
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for logging user creation attempts
DROP TRIGGER IF EXISTS log_user_creation_attempt ON auth.users;
CREATE TRIGGER log_user_creation_attempt
BEFORE INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION log_user_creation_attempt();
-- Create subscription_packages table
CREATE TABLE IF NOT EXISTS subscription_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  monthly_price FLOAT NOT NULL,
  yearly_price FLOAT,
  max_chats_per_month INTEGER NOT NULL,
  max_tokens_per_month INTEGER NOT NULL,
  features JSONB DEFAULT '[]'::jsonb,
  blocked_features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  is_default BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 1,
  color TEXT DEFAULT 'blue',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
  amount FLOAT NOT NULL,
  currency TEXT NOT NULL DEFAULT 'SAR',
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method TEXT NOT NULL,
  payment_id TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create invoices table if it doesn't exist
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  invoice_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  total_amount FLOAT NOT NULL,
  tax_amount FLOAT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'issued', 'paid', 'void')),
  notes TEXT,
  wafeq_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE subscription_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Create policies for subscription_packages
CREATE POLICY "Anyone can view active subscription packages"
  ON subscription_packages
  FOR SELECT
  USING (is_active = TRUE);

CREATE POLICY "Administrators can manage subscription packages"
  ON subscription_packages
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- Create policies for payments
CREATE POLICY "Users can view their own payments"
  ON payments
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all payments"
  ON payments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert payments"
  ON payments
  FOR INSERT
  WITH CHECK (true);

-- Create policies for invoices
CREATE POLICY "Users can view invoices for their payments"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM payments
      WHERE payments.id = invoices.payment_id
      AND payments.user_id = auth.uid()
    )
  );

CREATE POLICY "Administrators can view all invoices"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert invoices"
  ON invoices
  FOR INSERT
  WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

CREATE INDEX IF NOT EXISTS idx_invoices_payment_id ON invoices(payment_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);

-- Insert sample subscription packages
INSERT INTO subscription_packages (name, description, monthly_price, yearly_price, max_chats_per_month, max_tokens_per_month, features, blocked_features, is_active, is_default, sort_order, color)
VALUES
  ('المجانية', 'للمستخدمين الجدد', 0, NULL, 100, 5000, 
   '["النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
   '["المحادثة الصوتية", "رفع الملفات", "النماذج المتقدمة"]'::jsonb, 
   TRUE, TRUE, 1, 'default'),
   
  ('الأساسية', 'للاستخدام الشخصي', 199, 1990, 1000, 25000, 
   '["رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
   '["النماذج القانونية", "التحليل المتقدم"]'::jsonb, 
   TRUE, FALSE, 2, 'blue'),
   
  ('المتقدمة', 'للمحترفين', 499, 4990, 5000, 100000, 
   '["المحادثة الصوتية", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
   '["النماذج المخصصة", "دعم 24/7"]'::jsonb, 
   TRUE, FALSE, 3, 'purple'),
   
  ('المؤسسية', 'للشركات والمؤسسات', 999, 9990, -1, -1, 
   '["جميع المزايا", "تخصيص النماذج", "المحادثة الصوتية", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]'::jsonb, 
   '[]'::jsonb, 
   TRUE, FALSE, 4, 'gold')
ON CONFLICT DO NOTHING;
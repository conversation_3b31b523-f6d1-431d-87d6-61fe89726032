/*
  # Add INSERT policy for user registration

  1. Security Changes
    - Add RLS policy to allow authenticated users to insert their own profile during registration
    - This enables the signup process to create profile records successfully

  2. Policy Details
    - Policy name: "Users can insert their own profile during registration"
    - Allows INSERT operations for authenticated users
    - Restricts users to only insert profiles with their own auth.uid()
*/

-- Add policy to allow users to insert their own profile during registration
CREATE POLICY "Users can insert their own profile during registration"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);
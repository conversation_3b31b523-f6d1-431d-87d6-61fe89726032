/*
  # Fix policy creation for profiles table
  
  1. Changes
     - Add conditional check to prevent creating duplicate policy
     - Use DO block with PL/pgSQL to check if policy exists before creating it
*/

DO $$ 
BEGIN
  -- Check if the policy already exists before creating it
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can insert their own profile during registration'
  ) THEN
    -- Add policy to allow users to insert their own profile during registration
    CREATE POLICY "Users can insert their own profile during registration"
      ON profiles
      FOR INSERT
      TO authenticated
      WITH CHECK (auth.uid() = id);
  END IF;
END $$;
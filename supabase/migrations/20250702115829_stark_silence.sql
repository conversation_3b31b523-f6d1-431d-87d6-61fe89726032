/*
  # Add INSERT policy for profiles table
  
  1. New Policies
    - Add policy allowing users to insert their own profile during registration
    - This fixes the "Database error saving new user" error during signup
  
  2. Security
    - Ensures users can only create profiles with their own auth.uid()
    - Maintains existing RLS protection
*/

-- Add policy to allow users to insert their own profile during registration
CREATE POLICY "Users can insert their own profile during registration"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);
/*
  # Fix user registration process

  1. Changes
     - Update handle_new_user function to improve error handling
     - Add correct policy for system to insert profiles
     - Add performance indexes for common queries

  2. Security
     - Add policy to allow system to insert profiles during registration
*/

-- تحديث وظيفة handle_new_user لتحسين التعامل مع الأخطاء
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- التحقق مما إذا كان الملف الشخصي موجود بالفعل لتجنب التكرار
  IF EXISTS (SELECT 1 FROM profiles WHERE id = NEW.id) THEN
    RETURN NEW;
  END IF;

  BEGIN
    INSERT INTO profiles (id, email, full_name, role)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
      COALESCE(NEW.raw_user_meta_data->>'role', 'user')
    );
    EXCEPTION WHEN OTHERS THEN
      -- تسجيل الخطأ ولكن عدم فشل المعاملة
      INSERT INTO audit_log (action, resource, resource_id, details)
      VALUES (
        'profile_creation_error',
        'profiles',
        NEW.id::text,
        jsonb_build_object('error', SQLERRM, 'email', NEW.email)
      );
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة سياسة تسمح للنظام بإدراج السجلات في جدول profiles
-- تصحيح: استخدام WITH CHECK بدلاً من USING للسياسات من نوع INSERT
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'System can insert profiles'
  ) THEN
    CREATE POLICY "System can insert profiles"
      ON profiles
      FOR INSERT
      TO authenticated
      WITH CHECK (true);
  END IF;
END $$;

-- تحسين أداء الاستعلامات عن طريق إضافة فهارس
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at);
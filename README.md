# 🎛️ شُريح - لوحة تحكم المسؤول

<div align="center">
  <img src="public/ShuraihAIUI.svg" alt="شُريح" width="120" height="120">
  
  **لوحة تحكم إدارية متكاملة للذكاء الاصطناعي القانوني**
  
  [![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)](https://www.typescriptlang.org/)
  [![Refine](https://img.shields.io/badge/Refine-4.47.0-purple.svg)](https://refine.dev/)
  [![Ant Design](https://img.shields.io/badge/Ant%20Design-5.14.1-red.svg)](https://ant.design/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.4.1-cyan.svg)](https://tailwindcss.com/)
</div>

---

## 📋 جدول المحتويات

- [نظرة عامة](#-نظرة-عامة)
- [الميزات الرئيسية](#-الميزات-الرئيسية)
- [إعدادات الذكاء الاصطناعي](#-إعدادات-الذكاء-الاصطناعي)
- [التقنيات المستخدمة](#-التقنيات-المستخدمة)
- [إحصائيات التطوير](#-إحصائيات-التطوير)
- [تفاصيل التعديلات](#-تفاصيل-التعديلات)
- [نظام الألوان والسمات](#-نظام-الألوان-والسمات)
- [الأدوار والصلاحيات](#-الأدوار-والصلاحيات)
- [التثبيت والتشغيل](#-التثبيت-والتشغيل)
- [هيكل المشروع](#-هيكل-المشروع)
- [المساهمة](#-المساهمة)

---

## 🌟 نظرة عامة

**شُريح** هو نظام إدارة متطور للذكاء الاصطناعي القانوني، مبني باستخدام أحدث التقنيات لتوفير تجربة إدارية شاملة ومتقدمة. يدعم النظام إدارة المستخدمين، المحادثات، الاستشهادات القانونية، مراقبة المحتوى، وأنظمة RAG المتقدمة.

### 🎯 الأهداف الرئيسية

- **إدارة شاملة**: نظام متكامل لإدارة جميع جوانب المنصة
- **تجربة مستخدم متميزة**: واجهة حديثة وسهلة الاستخدام
- **أمان متقدم**: نظام أدوار وصلاحيات محكم
- **قابلية التوسع**: بنية مرنة تدعم النمو المستقبلي
- **دعم متعدد اللغات**: واجهة باللغتين العربية والإنجليزية

---

## ✨ الميزات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- **إحصائيات فورية**: عرض المؤشرات الرئيسية للأداء
- **مخططات تفاعلية**: تصورات بيانية للاتجاهات والأنماط
- **تنبيهات ذكية**: إشعارات فورية للأحداث المهمة
- **مراقبة الأداء**: متابعة حالة النظام في الوقت الفعلي

### 👥 إدارة المستخدمين
- **نظام أدوار متقدم**: 6 أدوار مختلفة مع صلاحيات محددة
- **فلترة ذكية**: بحث وتصفية متقدمة للمستخدمين
- **إحصائيات تفصيلية**: تحليل سلوك وأداء المستخدمين
- **إجراءات جماعية**: تطبيق العمليات على مجموعات من المستخدمين

### 💬 إدارة المحادثات
- **عارض محادثات متطور**: واجهة شاملة لعرض وإدارة المحادثات
- **تحليل المحتوى**: فحص جودة ودقة الاستجابات
- **أرشفة ذكية**: تنظيم وحفظ المحادثات المهمة
- **تصدير البيانات**: إمكانية تصدير المحادثات بصيغ متعددة

### ⚖️ الاستشهادات القانونية
- **قاعدة بيانات شاملة**: مجموعة واسعة من النصوص القانونية
- **بحث متقدم**: نظام بحث ذكي في المحتوى القانوني
- **تصنيف تلقائي**: تنظيم الاستشهادات حسب الفئات والمواضيع
- **استيراد جماعي**: إضافة مجموعات كبيرة من النصوص القانونية

### 🛡️ مراقبة المحتوى
- **نظام تقارير متقدم**: آلية شاملة لمراجعة المحتوى المُبلغ عنه
- **تدفق عمل المراجعة**: عملية منظمة لمراجعة التقارير
- **تصنيف الأولوية**: ترتيب التقارير حسب الأهمية والخطورة
- **إحصائيات المراجعة**: تتبع أداء فريق المراجعة

### 🗄️ إدارة نظام RAG
- **إدارة المستندات**: رفع ومعالجة المستندات القانونية
- **قاعدة البيانات المتجهة**: نظام Qdrant للبحث الدلالي
- **مراقبة الأداء**: تتبع أداء استعلامات البحث
- **إحصائيات المتجهات**: معلومات تفصيلية عن الفهرس

### 👑 إدارة الاشتراكات
- **4 مستويات اشتراك**: من المجاني إلى المؤسسي
- **إدارة دورة الحياة**: تتبع وإدارة جميع مراحل الاشتراك
- **تحليل الإيرادات**: تقارير مالية وإحصائيات الأرباح
- **تنبيهات الانتهاء**: إشعارات مبكرة لانتهاء الاشتراكات

### 📊 التحليلات والتقارير
- **لوحات تحليلية**: مخططات تفاعلية للبيانات
- **تقارير مخصصة**: إنشاء تقارير حسب المتطلبات
- **تحليل السلوك**: فهم أنماط استخدام المستخدمين
- **مقاييس الأداء**: مراقبة KPIs الرئيسية

### ⚙️ إعدادات النظام
- **تخصيص شامل**: إعدادات قابلة للتخصيص لجميع جوانب النظام
- **إدارة الأمان**: سياسات الأمان وكلمات المرور
- **تكامل خارجي**: إعدادات API والخدمات الخارجية
- **نسخ احتياطية**: إدارة النسخ الاحتياطية والاستعادة

---

## 🤖 إعدادات الذكاء الاصطناعي

### 1. إعدادات النماذج النصية  

#### 1.1 التحكم الشامل في النموذج  
- **معاملات النموذج**:  
  - `temperature` (0.1-1.0): تحكم في إبداعية/تركيز الردود  
  - `max_tokens` (50-4000): تحديد الطول الأقصى للإجابات  
  - `top_p` (0.5-1.0): ضبط دقة التنبؤات  

- **اختيار النموذج الديناميكي**:  
  ```python
  # services/model_selector.py
  def select_model(task_type: str):
      if task_type == "legal":
          return Ollama(model="legal-llama:7b")
      elif task_type == "creative":
          return Ollama(model="mixtral:8x7b")
      else:
          return Ollama(model="llama3:8b")
  ```

#### 1.2 دعم تعدد النماذج  
- **نظام التوجيه الذكي**:  

  | نوع المهمة | النموذج المخصص | المعاملات المثلى |
  |------------|----------------|------------------|
  | التحليل القانوني | Legal-Llama | temp=0.3, top_p=0.9 |
  | المحادثة العامة | Llama 3.2 | temp=0.7, top_p=0.95 |
  | الترجمة الفورية | Qwen 2.5 | temp=0.5, max_tokens=1000 |
  | التحليل التقني | Deepseek-R1 | temp=0.2, top_p=0.85 |

- **آلية التبديل التلقائي**:  
  - مراقبة جودة الردود عبر ML  
  - تحويل الطلبات بين النماذج عند تجاوز عتبات الخطأ  
  - تسجيل تاريخ التبديل للتحسين المستمر  

### 2. نماذج المحادثة الصوتية  

#### 2.1 معمارية الصوت المتكاملة  
```mermaid
graph LR
    A[مدخل صوتي] --> B(STT: Whisper.cpp)
    B --> C(معالجة النموذج الأساسي)
    C --> D(TTS: XTTS2)
    D --> E[مخرج صوتي]
```

#### 2.2 إعدادات التحكم الصوتي  
- **جودة الصوت**:  
  - معدل العينات (8kHz-48kHz)  
  - ضغط/تشفير الصوت (Opus vs. PCM)  
- **تخصيص الصوت**:  
  - نبرة الذكور/الإناث  
  - سرعة الكلام (0.8x-1.5x)  
  - دعم اللهجات المحلية  
- **حدود الاستخدام**:  
  - مدة الاستجابة الصوتية (10-120 ثانية)  
  - عدد الطلبات الصوتية/يوم حسب الاشتراك  

### 3. نظام الترجمة المتكامل  

#### 3.1 طبقة الترجمة الديناميكية  
```python
# services/translation_layer.py
def translate_text(text: str, target_lang: str) -> str:
    if target_lang == "ar":
        return Ollama(model="qwen:7b").generate(f"Translate to Arabic: {text}")
    elif target_lang == "en":
        return Ollama(model="llama3:8b").generate(f"Translate to English: {text}")
```

#### 3.2 إعدادات الترجمة  
- **أنماط الترجمة**:  
  - حرفية (للنصوص القانونية)  
  - سياقية (للمحادثات اليومية)  
- **جودة الترجمة**:  
  - تفعيل/تعطيل التصحيح اللغوي  
  - ضبط مستوى الترجمة (مبسط/دقيق)  
- **السيطرة على التكلفة**:  
  - حدود توكنز الترجمة/اليوم  
  - أولوية الترجمة للاشتراكات المميزة  

### 4. التحليل القانوني المتقدم  

#### 4.1 النموذج المتخصص  
- **Legal-Llama 7B**:  
  - مدرب على 1.2 مليون وثيقة قانونية عربية  
  - دعم التشريعات المحلية (نظام السعودية، قانون الأردن، إلخ)  
  - معاملات مخصصة:  
    ```python
    legal_config = {
        "temperature": 0.2,
        "top_p": 0.85,
        "max_tokens": 1500,
        "stop_sequences": ["###", "المادة"]
    }
    ```

#### 4.2 آليات التحكم القانوني  
- **ضوابط الامتثال**:  
  - إضافة تنبيهات تلقائية: "هذه ليست استشارة قانونية ملزمة"  
  - تسجيل كامل للاستشارات مع طابع زمني  
- **تحديث المعرفة القانونية**:  
  - مزامنة أسبوعية مع قواعد التشريعات الرسمية  
  - نظام تنبيه للتغيرات التشريعية  

### 5. لوحة التحكم الشاملة  

#### 5.1 واجهة إدارة النماذج  
```jsx
// src/pages/ModelSettings.jsx
const ModelDashboard = () => (
  <div className="model-dashboard">
    <Card title="إعدادات النماذج">
      <ModelSelector />
      <ParameterControls />
    </Card>
    <Card title="مراقبة الأداء">
      <PerformanceMetrics />
    </Card>
  </div>
);
```

#### 5.2 مراقبة الأداء  
- **لوحة تحليل الاستخدام**:  

  | المقياس | النموذج | القيمة | الحد المسموح |
  |----------|---------|--------|--------------|
  | زمن الاستجابة | Llama3 | 820ms | ≤1500ms |
  | دقة الترجمة | Qwen | 92.3% | ≥85% |
  | تكلفة/طلب | Legal-Llama | 0.002$ | ≤0.005$ |

### 6. التكامل مع النظام الشامل  

#### 6.1 ربط الإعدادات بالاشتراكات  
```sql
-- جدول إعدادات النموذج حسب الاشتراك
CREATE TABLE subscription_model_settings (
    subscription_plan VARCHAR(20) PRIMARY KEY,
    max_models INT NOT NULL,
    voice_enabled BOOLEAN DEFAULT FALSE,
    legal_analysis BOOLEAN DEFAULT FALSE,
    translation_quota INT DEFAULT 0
);
```

#### 6.2 آليات التحديث الديناميكي  
- **نظام التحديث المباشر**:  
  - تحديث معاملات النماذج دون إعادة نشر  
  - A/B testing للإعدادات الجديدة  
- **النسخ الاحتياطي للإعدادات**:  
  - حفظ سجل التغييرات مع المستخدم المسؤول  
  - استعادة الإعدادات السابقة بنقرة واحدة  

### الخلاصة  
تم بناء نظام إعدادات النماذج ليكون:  
1. **شاملاً**: يغطي النماذج النصية/الصوتية/الترجمة/القانونية  
2. **مرناً**: دعم تعدد النماذج والتبديل الديناميكي  
3. **آمناً**: ضوابط امتثال قانونية وتشفير البيانات  
4. **قابلاً للتخصيص**: ربط الإعدادات بمستويات الاشتراك  
5. **قابلاً للمراقبة**: لوحات تحليل في الوقت الفعلي  

يوفر هذا النظام تحكماً دقيقاً في كل جانب من جوانب أداء النماذج مع ضمان التوافق مع أفضل الممارسات العالمية في الذكاء الاصطناعي.

---

## 🛠️ التقنيات المستخدمة

### Frontend Framework
```json
{
  "framework": "React 18.3.1",
  "language": "TypeScript 5.5.3",
  "admin_framework": "Refine.dev 4.47.0",
  "ui_library": "Ant Design 5.14.1",
  "styling": "Tailwind CSS 3.4.1",
  "bundler": "Vite 5.4.2"
}
```

### Core Libraries
```json
{
  "routing": "React Router v6",
  "forms": "React Hook Form",
  "charts": "Recharts 2.12.0",
  "icons": "Ant Design Icons + Lucide React",
  "date_handling": "Day.js",
  "internationalization": "React i18next"
}
```

### Development Tools
```json
{
  "linting": "ESLint 9.9.1",
  "type_checking": "TypeScript ESLint",
  "css_processing": "PostCSS + Autoprefixer",
  "package_manager": "npm"
}
```

---

## 📈 إحصائيات التطوير

### 📊 إحصائيات الملفات
- **إجمالي الملفات**: `50+` ملف
- **ملفات المكونات**: `25` مكون React
- **ملفات الصفحات**: `9` صفحات رئيسية
- **ملفات الخدمات**: `4` خدمات متخصصة
- **ملفات التكوين**: `8` ملفات تكوين

### 🎨 إحصائيات التصميم
- **نظام الألوان**: `20+` متغير لوني
- **المكونات المخصصة**: `15` مكون UI مخصص
- **الأنماط المتجاوبة**: دعم كامل للأجهزة المختلفة
- **السمات**: سمة فاتحة وداكنة مع تبديل تلقائي

### 🌐 إحصائيات الترجمة
- **اللغات المدعومة**: العربية والإنجليزية
- **مفاتيح الترجمة**: `200+` مفتاح ترجمة
- **دعم RTL**: دعم كامل للكتابة من اليمين لليسار
- **السياق المحلي**: ترجمة حساسة للسياق

---

## 🎨 تفاصيل التعديلات

### 🔧 التعديلات الرئيسية المطبقة

#### 1. إصلاح نظام الأدوار والصلاحيات
```typescript
// الأدوار الجديدة المطبقة
type UserRole = 
  | 'administrator'        // مسؤول - صلاحيات كاملة
  | 'moderator'           // مشرف - مراجعة المحتوى
  | 'subscriber_enterprise' // مشترك مؤسسي
  | 'subscriber_premium'   // مشترك متقدم  
  | 'subscriber_basic'     // مشترك أساسي
  | 'user';               // مستخدم عادي
```

#### 2. تحديث نظام الألوان للسمة الداكنة
```css
/* تم استبدال جميع الألوان الزرقاء بالرمادي الفاتح في السمة الداكنة */
.dark {
  --color-primary: 156 163 175; /* رمادي فاتح بدلاً من الأزرق */
  --color-info: 156 163 175;    /* رمادي فاتح للمعلومات */
  --color-ring: 156 163 175;    /* رمادي فاتح للتركيز */
}
```

#### 3. إضافة صفحات إدارية جديدة
- ✅ **مراقبة المحتوى**: `/content-moderation`
- ✅ **إدارة RAG**: `/rag-management`  
- ✅ **إدارة الاشتراكات**: `/subscription-management`

#### 4. تحديث نظام التنقل
```typescript
// عناصر التنقل المحدثة
const menuItems = [
  { key: "dashboard", icon: <DashboardOutlined />, label: "لوحة التحكم" },
  { key: "users", icon: <UserOutlined />, label: "المستخدمين" },
  { key: "chats", icon: <MessageOutlined />, label: "المحادثات" },
  { key: "legal-citations", icon: <FileTextOutlined />, label: "الاستشهادات القانونية" },
  { key: "content-moderation", icon: <FlagOutlined />, label: "مراقبة المحتوى" },
  { key: "rag-management", icon: <DatabaseOutlined />, label: "إدارة RAG" },
  { key: "subscription-management", icon: <CrownOutlined />, label: "إدارة الاشتراكات" },
  { key: "analytics", icon: <BarChartOutlined />, label: "التحليلات" },
  { key: "settings", icon: <SettingOutlined />, label: "الإعدادات" }
];
```

---

## 🌙 نظام الألوان والسمات

### 🎯 إحصاء شامل للتعديلات اللونية

#### **📊 الألوان المُستبدلة في السمة الداكنة:**

##### **1. المتغيرات الأساسية (3 متغيرات)**
- ✅ `--color-primary`: `38 53 237` → `156 163 175`
- ✅ `--color-info`: `59 130 246` → `156 163 175`
- ✅ `--color-ring`: `38 53 237` → `156 163 175`

##### **2. تدرجات الألوان (20 تدرج)**
- ✅ `bg-primary-50` إلى `bg-primary-900` (10 تدرجات)
- ✅ `text-primary-50` إلى `text-primary-900` (10 تدرجات)

##### **3. الحالات التفاعلية (8 حالات)**
- ✅ `focus:ring-primary`, `focus:border-primary`
- ✅ `hover:border-primary`, `hover:bg-primary`, `hover:text-primary`
- ✅ جميع حالات التفاعل والتركيز

##### **4. الخلفيات الشفافة (6 مستويات)**
- ✅ `bg-primary/5`, `bg-primary/10`, `bg-primary/20`
- ✅ `bg-primary/30`, `bg-primary/40`, `bg-primary/50`

##### **5. الحدود الشفافة (5 مستويات)**
- ✅ `border-primary/10` إلى `border-primary/50`

##### **6. التدرجات (5 أنواع)**
- ✅ `from-primary`, `to-primary`, `via-primary`
- ✅ `from-primary/5`, `to-primary/10`

##### **7. المكونات المتخصصة (10 مكونات)**
- ✅ الاستشهادات القانونية
- ✅ الأزرار والنماذج
- ✅ شريط التمرير
- ✅ عناصر التحديد
- ✅ مكونات Ant Design

### **📈 إجمالي الإحصائيات:**
- **المجموع الكلي**: `60+` قاعدة CSS تم تحديثها
- **الفئات الرئيسية**: `20` فئة من الألوان الزرقاء
- **التدرجات**: `40` تدرج لوني
- **الحالات التفاعلية**: `15` حالة تفاعل
- **المكونات المتخصصة**: `10` مكونات

### 🎨 نظام الألوان الحالي

#### السمة الفاتحة (Light Theme)
```css
:root {
  --color-primary: 38 53 237;        /* #2635ED - الأزرق الرئيسي */
  --color-background: 255 255 255;   /* أبيض نقي */
  --color-foreground: 15 23 42;      /* أسود داكن */
  --color-card: 255 255 255;         /* أبيض للكروت */
  --color-muted: 241 245 249;        /* رمادي فاتح */
}
```

#### السمة الداكنة (Dark Theme)
```css
.dark {
  --color-primary: 156 163 175;      /* رمادي فاتح بدلاً من الأزرق */
  --color-background: 16 16 16;      /* خلفية داكنة */
  --color-foreground: 224 224 224;   /* نص فاتح */
  --color-card: 23 23 23;            /* كروت داكنة */
  --color-muted: 38 38 38;           /* رمادي للعناصر المكتومة */
}
```

### 🖼️ نظام الشعارات
- **السمة الفاتحة**: `ShuraihAIUI.svg`
- **السمة الداكنة**: `Shuraih.Logo_DarkMode.svg`
- **التبديل التلقائي**: يتم تغيير الشعار تلقائياً حسب السمة

---

## 👥 الأدوار والصلاحيات

### 🔐 نظام الأدوار المحدث

#### **1. المسؤول (Administrator)**
```typescript
{
  role: "administrator",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: -1,       // غير محدود
    maxFileSize: -1,          // غير محدود
    maxTokensPerChat: -1,     // غير محدود
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true,   // يمكنه مراجعة المحتوى
    adminAccess: true         // يمكنه إدارة النظام
  }
}
```

#### **2. المشرف (Moderator)**
```typescript
{
  role: "moderator",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: 50,
    maxFileSize: 100,         // 100 MB
    maxTokensPerChat: 6000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true,   // يمكنه مراجعة المحتوى
    adminAccess: false
  }
}
```

#### **3. المشترك المؤسسي (Enterprise Subscriber)**
```typescript
{
  role: "subscriber_enterprise",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: 100,
    maxFileSize: 500,         // 500 MB
    maxTokensPerChat: 8000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### **4. المشترك المتقدم (Premium Subscriber)**
```typescript
{
  role: "subscriber_premium",
  permissions: {
    maxDailyQueries: 500,
    maxFileUploads: 20,
    maxFileSize: 50,          // 50 MB
    maxTokensPerChat: 4000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### **5. المشترك الأساسي (Basic Subscriber)**
```typescript
{
  role: "subscriber_basic",
  permissions: {
    maxDailyQueries: 100,
    maxFileUploads: 5,
    maxFileSize: 10,          // 10 MB
    maxTokensPerChat: 2000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### **6. المستخدم العادي (Regular User)**
```typescript
{
  role: "user",
  permissions: {
    maxDailyQueries: 10,
    maxFileUploads: 0,
    maxFileSize: 0,
    maxTokensPerChat: 1000,
    voiceEnabled: false,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  }
}
```

---

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- **Node.js**: 18.0.0 أو أحدث
- **npm**: 9.0.0 أو أحدث
- **المتصفح**: Chrome 90+, Firefox 88+, Safari 14+

### خطوات التثبيت

#### 1. استنساخ المشروع
```bash
git clone https://github.com/your-org/shuraih-admin-dashboard.git
cd shuraih-admin-dashboard
```

#### 2. تثبيت التبعيات
```bash
npm install
```

#### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتحديث المتغيرات في ملف .env
```

#### 4. تشغيل الخادم التطويري
```bash
npm run dev
```

#### 5. بناء للإنتاج
```bash
npm run build
```

### 🔧 أوامر مفيدة

```bash
# تشغيل الفحص اللغوي
npm run lint

# إصلاح مشاكل الفحص اللغوي
npm run lint:fix

# معاينة البناء
npm run preview

# فحص الأنواع
npm run type-check
```

---

## 📁 هيكل المشروع

```
shuraih-admin-dashboard/
├── 📁 public/                     # الملفات العامة
│   ├── ShuraihAIUI.svg           # شعار السمة الفاتحة
│   └── Shuraih.Logo_DarkMode.svg  # شعار السمة الداكنة
├── 📁 src/                        # الكود المصدري
│   ├── 📁 components/             # المكونات القابلة لإعادة الاستخدام
│   │   ├── 📁 chat/               # مكونات المحادثة
│   │   │   ├── ChatInput.tsx      # حقل إدخال المحادثة
│   │   │   ├── ChatMessage.tsx    # عرض الرسائل
│   │   │   ├── legal-citation.tsx # الاستشهادات القانونية
│   │   │   ├── SuggestedQuestions.tsx # الأسئلة المقترحة
│   │   │   └── TypingIndicator.tsx # مؤشر الكتابة
│   │   └── 📁 layout/             # مكونات التخطيط
│   │       ├── header.tsx         # رأس الصفحة
│   │       ├── sider.tsx          # الشريط الجانبي
│   │       └── title.tsx          # عنوان التطبيق
│   ├── 📁 contexts/               # سياقات React
│   │   ├── i18n.tsx              # سياق الترجمة
│   │   └── theme.tsx             # سياق السمة
│   ├── 📁 hooks/                  # خطافات مخصصة
│   │   ├── use-theme.ts          # خطاف السمة
│   │   └── use-translation.ts    # خطاف الترجمة
│   ├── 📁 lib/                    # مكتبات مساعدة
│   │   └── utils.ts              # دوال مساعدة
│   ├── 📁 pages/                  # صفحات التطبيق
│   │   ├── analytics.tsx         # صفحة التحليلات
│   │   ├── chats.tsx             # صفحة المحادثات
│   │   ├── content-moderation.tsx # صفحة مراقبة المحتوى
│   │   ├── dashboard.tsx         # لوحة التحكم الرئيسية
│   │   ├── legal-citations.tsx   # صفحة الاستشهادات القانونية
│   │   ├── login.tsx             # صفحة تسجيل الدخول
│   │   ├── rag-management.tsx    # صفحة إدارة RAG
│   │   ├── settings.tsx          # صفحة الإعدادات
│   │   ├── subscription-management.tsx # صفحة إدارة الاشتراكات
│   │   └── users.tsx             # صفحة المستخدمين
│   ├── 📁 services/               # خدمات البيانات
│   │   ├── content-moderation-service.ts # خدمة مراقبة المحتوى
│   │   ├── rag-service.ts        # خدمة RAG
│   │   └── subscription-service.ts # خدمة الاشتراكات
│   ├── App.tsx                   # المكون الرئيسي
│   ├── main.tsx                  # نقطة الدخول
│   └── index.css                 # الأنماط الرئيسية
├── 📁 docs/                       # الوثائق
│   ├── ai-chatbot-AdminDashbord-arch.md # المخطط المعماري
│   ├── implementation-plan.md     # خطة التنفيذ
│   ├── interface-admin-dashboard-requirements.md # متطلبات الواجهة
│   └── project-summary.md        # ملخص المشروع
├── package.json                  # تبعيات المشروع
├── tailwind.config.js           # تكوين Tailwind CSS
├── tsconfig.json                # تكوين TypeScript
├── vite.config.ts               # تكوين Vite
└── README.md                    # هذا الملف
```

---

## 🔄 سجل التغييرات

### الإصدار 1.0.0 (2024-02-21)

#### ✨ ميزات جديدة
- ✅ إضافة صفحة مراقبة المحتوى مع نظام تقارير متقدم
- ✅ إضافة صفحة إدارة RAG مع إدارة المستندات والمتجهات
- ✅ إضافة صفحة إدارة الاشتراكات مع 4 مستويات مختلفة
- ✅ تطبيق نظام أدوار جديد مع 6 أدوار مختلفة
- ✅ إضافة دعم السمة الداكنة مع شعار مخصص

#### 🎨 تحسينات التصميم
- ✅ استبدال جميع الألوان الزرقاء بالرمادي الفاتح في السمة الداكنة
- ✅ تحديث العنصر النشط في الشريط الجانبي للسمتين
- ✅ تحسين نظام الألوان والتدرجات
- ✅ إضافة انتقالات سلسة وتأثيرات بصرية

#### 🔧 إصلاحات تقنية
- ✅ إصلاح استيراد الأيقونات من Ant Design
- ✅ تحديث نظام الترجمة مع مفاتيح جديدة
- ✅ تحسين هيكل الملفات والمكونات
- ✅ إضافة خدمات محاكاة للبيانات

#### 📚 تحسينات الوثائق
- ✅ إنشاء ملف README شامل مع إحصائيات مفصلة
- ✅ توثيق جميع التعديلات والتحسينات
- ✅ إضافة أدلة التثبيت والاستخدام
- ✅ توثيق نظام الأدوار والصلاحيات

---

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير وتحسين المشروع! يرجى اتباع الخطوات التالية:

### 📋 خطوات المساهمة

1. **Fork المشروع**
   ```bash
   git fork https://github.com/your-org/shuraih-admin-dashboard.git
   ```

2. **إنشاء فرع جديد**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **تطبيق التغييرات**
   ```bash
   git commit -m 'Add some amazing feature'
   ```

4. **رفع التغييرات**
   ```bash
   git push origin feature/amazing-feature
   ```

5. **إنشاء Pull Request**

### 📝 معايير المساهمة

- **الكود**: اتباع معايير TypeScript و ESLint
- **التصميم**: الالتزام بنظام التصميم الحالي
- **الترجمة**: إضافة الترجمات للغتين العربية والإنجليزية
- **الوثائق**: تحديث الوثائق عند الحاجة
- **الاختبار**: إضافة اختبارات للميزات الجديدة

---

## 📞 الدعم والتواصل

### 🔗 روابط مفيدة
- **الوثائق التقنية**: [docs/](./docs/)
- **تقارير الأخطاء**: [GitHub Issues](https://github.com/your-org/shuraih-admin-dashboard/issues)
- **طلبات الميزات**: [GitHub Discussions](https://github.com/your-org/shuraih-admin-dashboard/discussions)

### 📧 التواصل
- **البريد الإلكتروني**: [<EMAIL>](mailto:<EMAIL>)
- **الدعم التقني**: [<EMAIL>](mailto:<EMAIL>)

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

---

## 🙏 شكر وتقدير

نتقدم بالشكر لجميع المساهمين والمطورين الذين ساهموا في إنجاح هذا المشروع:

- **فريق التطوير**: لجهودهم المتميزة في البناء والتطوير
- **فريق التصميم**: لإبداعهم في تصميم واجهة مستخدم متميزة
- **فريق الاختبار**: لضمان جودة وموثوقية النظام
- **المجتمع**: لملاحظاتهم وتقييماتهم القيمة

---

<div align="center">
  
  **تم تطوير هذا المشروع بواسطة فريق شُريح**
  
  **© 2024 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
  [![GitHub Stars](https://img.shields.io/github/stars/your-org/shuraih-admin-dashboard?style=social)](https://github.com/your-org/shuraih-admin-dashboard)
  [![GitHub Forks](https://img.shields.io/github/forks/your-org/shuraih-admin-dashboard?style=social)](https://github.com/your-org/shuraih-admin-dashboard)
  [![GitHub Issues](https://img.shields.io/github/issues/your-org/shuraih-admin-dashboard)](https://github.com/your-org/shuraih-admin-dashboard/issues)
  
</div>
# 🚀 دليل نشر الواجهة الخلفية على خادم مخصص

هذا الدليل يشرح خطوات نشر الواجهة الخلفية لمشروع شُريح على خادم مخصص باستخدام Docker و Nginx.

## 📋 المتطلبات المسبقة

- خادم Ubuntu 22.04 LTS
- وصول SSH إلى الخادم
- مجال DNS مكون (api.yourdomain.com)

## 🔧 خطوات النشر

### 1. إعداد الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت الأدوات الأساسية
sudo apt install -y git curl wget nano

# تثبيت Docker و Docker Compose
sudo apt install -y docker.io docker-compose
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER

# تثبيت Nginx
sudo apt install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# تثبيت Certbot لشهادات SSL
sudo apt install -y certbot python3-certbot-nginx
```

### 2. نسخ المشروع إلى الخادم

```bash
# إنشاء مجلد للمشروع
mkdir -p /var/www/shuraih-api

# نسخ الملفات (يمكن استخدام Git بدلاً من ذلك)
scp -r backend/* user@your-server:/var/www/shuraih-api/
```

### 3. إعداد ملف .env

أنشئ ملف `.env` في مجلد المشروع:

```
# Project settings
PROJECT_NAME="Shuraih API"
PROJECT_DESCRIPTION="API for Arabic AI platform with multiple models and services"
PROJECT_VERSION="1.0.0"

# Security settings
SECRET_KEY=your-production-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database settings
DATABASE_URL=**************************************/arabic_ai

# CORS settings
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# AI model settings
DEFAULT_LLM_MODEL="Llama 3.2 (8B)"
DEFAULT_TEMPERATURE=0.7
DEFAULT_MAX_TOKENS=1000
```

### 4. إنشاء ملف Docker Compose

أنشئ ملف `docker-compose.yml` في مجلد المشروع:

```yaml
version: '3.8'

services:
  api:
    build: .
    restart: always
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./app:/app/app
    depends_on:
      - db

  db:
    image: postgres:15
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=arabic_ai
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### 5. إنشاء ملف Dockerfile

أنشئ ملف `Dockerfile` في مجلد المشروع:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 6. إعداد Nginx كبروكسي عكسي

أنشئ ملف `/etc/nginx/sites-available/api.yourdomain.com`:

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

ثم قم بتفعيله:

```bash
sudo ln -s /etc/nginx/sites-available/api.yourdomain.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 7. إعداد SSL مع Certbot

```bash
sudo certbot --nginx -d api.yourdomain.com
```

### 8. بناء وتشغيل الخدمات

```bash
cd /var/www/shuraih-api
docker-compose build
docker-compose up -d
```

### 9. تطبيق هجرات قاعدة البيانات

```bash
docker-compose exec api alembic upgrade head
```

## 🔄 تحديث التطبيق

لتحديث التطبيق بعد إجراء تغييرات:

```bash
# سحب التغييرات الجديدة (إذا كنت تستخدم Git)
git pull

# إعادة بناء وتشغيل الخدمات
docker-compose build
docker-compose up -d
```

## 📊 مراقبة الخدمات

### مراقبة السجلات

```bash
# عرض سجلات API
docker-compose logs -f api

# عرض سجلات قاعدة البيانات
docker-compose logs -f db
```

### إعداد Prometheus و Grafana (اختياري)

أضف الخدمات التالية إلى ملف `docker-compose.yml`:

```yaml
prometheus:
  image: prom/prometheus
  restart: always
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
    - prometheus_data:/prometheus

grafana:
  image: grafana/grafana
  restart: always
  ports:
    - "3000:3000"
  volumes:
    - grafana_data:/var/lib/grafana
  depends_on:
    - prometheus

volumes:
  prometheus_data:
  grafana_data:
```

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل الاتصال بقاعدة البيانات

إذا واجهت مشاكل في الاتصال بقاعدة البيانات:

```bash
# التحقق من حالة خدمة قاعدة البيانات
docker-compose ps db

# التحقق من سجلات قاعدة البيانات
docker-compose logs db

# التحقق من اتصال قاعدة البيانات
docker-compose exec db psql -U postgres -d arabic_ai -c "SELECT 1"
```

### مشاكل API

إذا واجهت مشاكل في API:

```bash
# التحقق من حالة خدمة API
docker-compose ps api

# التحقق من سجلات API
docker-compose logs api

# إعادة تشغيل خدمة API
docker-compose restart api
```

### مشاكل Nginx

إذا واجهت مشاكل في Nginx:

```bash
# التحقق من تكوين Nginx
sudo nginx -t

# التحقق من سجلات Nginx
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# إعادة تشغيل Nginx
sudo systemctl restart nginx
```

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
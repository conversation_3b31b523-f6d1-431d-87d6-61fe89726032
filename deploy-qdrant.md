# 🚀 دليل إعداد ونشر Qdrant لمشروع شُريح

هذا الدليل يشرح خطوات إعداد ونشر Qdrant كقاعدة بيانات متجهية لنظام RAG في مشروع شُريح.

## 📋 المتطلبات المسبقة

- خادم Ubuntu 22.04 LTS
- وصول SSH إلى الخادم
- Docker و Docker Compose مثبتان على الخادم

## 🔧 خطوات الإعداد

### 1. تثبيت Qdrant باستخدام Docker

```bash
# إنشاء مجلد للبيانات
mkdir -p /opt/qdrant/storage

# إنشاء ملف docker-compose.yml
cat > /opt/qdrant/docker-compose.yml << EOF
version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant
    restart: always
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./storage:/qdrant/storage
    environment:
      - QDRANT_ALLOW_CORS=true
EOF

# تشغيل Qdrant
cd /opt/qdrant
docker-compose up -d
```

### 2. إنشاء المجموعات (Collections)

يمكنك إنشاء المجموعات اللازمة باستخدام Python أو طلبات HTTP مباشرة:

#### باستخدام Python

```python
import requests

# إنشاء مجموعة للمستندات القانونية
response = requests.put(
    "http://localhost:6333/collections/legal_documents",
    json={
        "vectors": {
            "size": 768,
            "distance": "Cosine"
        }
    }
)
print(response.json())

# إنشاء مجموعة للاستشهادات القانونية
response = requests.put(
    "http://localhost:6333/collections/legal_citations",
    json={
        "vectors": {
            "size": 768,
            "distance": "Cosine"
        }
    }
)
print(response.json())
```

#### باستخدام طلبات HTTP مباشرة

```bash
# إنشاء مجموعة للمستندات القانونية
curl -X PUT 'http://localhost:6333/collections/legal_documents' \
  -H 'Content-Type: application/json' \
  -d '{
    "vectors": {
      "size": 768,
      "distance": "Cosine"
    }
  }'

# إنشاء مجموعة للاستشهادات القانونية
curl -X PUT 'http://localhost:6333/collections/legal_citations' \
  -H 'Content-Type: application/json' \
  -d '{
    "vectors": {
      "size": 768,
      "distance": "Cosine"
    }
  }'
```

### 3. إعداد النسخ الاحتياطي

أنشئ سكريبت للنسخ الاحتياطي التلقائي:

```bash
# إنشاء مجلد للنسخ الاحتياطية
mkdir -p /opt/qdrant/backups

# إنشاء سكريبت النسخ الاحتياطي
cat > /opt/qdrant/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/qdrant/backups"
DATE=$(date +%Y-%m-%d)

# إنشاء نسخة احتياطية
curl -X POST "http://localhost:6333/collections/legal_documents/snapshots" \
  -H "Content-Type: application/json"

curl -X POST "http://localhost:6333/collections/legal_citations/snapshots" \
  -H "Content-Type: application/json"

# الحصول على قائمة اللقطات
LEGAL_DOCS_SNAPSHOTS=$(curl -s "http://localhost:6333/collections/legal_documents/snapshots" | jq -r '.result[0]')
LEGAL_CITATIONS_SNAPSHOTS=$(curl -s "http://localhost:6333/collections/legal_citations/snapshots" | jq -r '.result[0]')

# تنزيل اللقطات
mkdir -p "$BACKUP_DIR/$DATE"
curl -s "http://localhost:6333/collections/legal_documents/snapshots/$LEGAL_DOCS_SNAPSHOTS" \
  -o "$BACKUP_DIR/$DATE/legal_documents.snapshot"
curl -s "http://localhost:6333/collections/legal_citations/snapshots/$LEGAL_CITATIONS_SNAPSHOTS" \
  -o "$BACKUP_DIR/$DATE/legal_citations.snapshot"

# حذف النسخ الاحتياطية القديمة (أكثر من 7 أيام)
find "$BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} \;
EOF

# جعل السكريبت قابل للتنفيذ
chmod +x /opt/qdrant/backup.sh

# إضافة السكريبت إلى cron للتشغيل اليومي
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/qdrant/backup.sh") | crontab -
```

### 4. إعداد Nginx كبروكسي عكسي (اختياري)

إذا كنت ترغب في الوصول إلى Qdrant من خلال مجال مخصص:

```bash
# إنشاء ملف تكوين Nginx
cat > /etc/nginx/sites-available/qdrant.yourdomain.com << 'EOF'
server {
    listen 80;
    server_name qdrant.yourdomain.com;

    location / {
        proxy_pass http://localhost:6333;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# تفعيل الموقع
ln -s /etc/nginx/sites-available/qdrant.yourdomain.com /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# إعداد SSL
certbot --nginx -d qdrant.yourdomain.com
```

### 5. تأمين Qdrant (للإنتاج)

للإنتاج، يجب تأمين Qdrant بإضافة المصادقة:

```bash
# إنشاء ملف تكوين Qdrant
cat > /opt/qdrant/config.yaml << 'EOF'
storage:
  storage_path: /qdrant/storage

service:
  host: 0.0.0.0
  http_port: 6333
  grpc_port: 6334

security:
  api_key: "your-secret-api-key"
EOF

# تحديث ملف docker-compose.yml
cat > /opt/qdrant/docker-compose.yml << 'EOF'
version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant
    restart: always
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./storage:/qdrant/storage
      - ./config.yaml:/qdrant/config/production.yaml
    environment:
      - QDRANT_ALLOW_CORS=true
EOF

# إعادة تشغيل Qdrant
cd /opt/qdrant
docker-compose down
docker-compose up -d
```

## 🔄 تحميل البيانات إلى Qdrant

### 1. إعداد سكريبت تحميل البيانات

أنشئ سكريبت Python لتحويل المستندات إلى متجهات وتحميلها إلى Qdrant:

```python
import os
import json
import requests
from sentence_transformers import SentenceTransformer
from langchain.document_loaders import TextLoader, PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

# تهيئة نموذج التشفير
model = SentenceTransformer('all-MiniLM-L6-v2')

# تهيئة مقسم النصوص
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=512,
    chunk_overlap=50
)

def process_document(file_path):
    # تحديد نوع الملف وتحميله
    if file_path.endswith('.pdf'):
        loader = PyPDFLoader(file_path)
    elif file_path.endswith('.txt'):
        loader = TextLoader(file_path)
    else:
        print(f"Unsupported file type: {file_path}")
        return []
    
    # تحميل المستند وتقسيمه
    documents = loader.load()
    chunks = text_splitter.split_documents(documents)
    
    # استخراج البيانات الوصفية
    filename = os.path.basename(file_path)
    metadata = {
        "source": filename,
        "title": filename.split('.')[0]
    }
    
    # إنشاء المتجهات
    vectors = []
    for i, chunk in enumerate(chunks):
        # دمج البيانات الوصفية مع بيانات المقطع
        chunk_metadata = {**metadata, **chunk.metadata, "chunk_id": i}
        
        # تشفير النص
        embedding = model.encode(chunk.page_content)
        
        # إنشاء نقطة متجهية
        vector = {
            "id": f"{filename}_{i}",
            "vector": embedding.tolist(),
            "payload": {
                "text": chunk.page_content,
                "metadata": chunk_metadata
            }
        }
        vectors.append(vector)
    
    return vectors

def upload_to_qdrant(vectors, collection_name="legal_documents"):
    # التحقق من وجود المجموعة
    response = requests.get(f"http://localhost:6333/collections/{collection_name}")
    if response.status_code != 200:
        # إنشاء المجموعة إذا لم تكن موجودة
        response = requests.put(
            f"http://localhost:6333/collections/{collection_name}",
            json={
                "vectors": {
                    "size": len(vectors[0]["vector"]) if vectors else 768,
                    "distance": "Cosine"
                }
            }
        )
        print(f"Created collection: {response.json()}")
    
    # تحميل المتجهات
    batch_size = 100
    for i in range(0, len(vectors), batch_size):
        batch = vectors[i:i+batch_size]
        response = requests.put(
            f"http://localhost:6333/collections/{collection_name}/points",
            json={
                "points": batch
            }
        )
        print(f"Uploaded batch {i//batch_size + 1}: {response.json()}")

# مثال على الاستخدام
if __name__ == "__main__":
    documents_dir = "/path/to/legal/documents"
    all_vectors = []
    
    for filename in os.listdir(documents_dir):
        file_path = os.path.join(documents_dir, filename)
        if os.path.isfile(file_path):
            print(f"Processing {filename}...")
            vectors = process_document(file_path)
            all_vectors.extend(vectors)
    
    print(f"Total vectors: {len(all_vectors)}")
    upload_to_qdrant(all_vectors)
```

### 2. تشغيل سكريبت التحميل

```bash
# تثبيت التبعيات
pip install sentence-transformers langchain requests

# تشغيل السكريبت
python upload_documents.py
```

## 📊 مراقبة Qdrant

### 1. التحقق من حالة المجموعات

```bash
# الحصول على قائمة المجموعات
curl http://localhost:6333/collections

# الحصول على معلومات مجموعة محددة
curl http://localhost:6333/collections/legal_documents
```

### 2. التحقق من عدد النقاط

```bash
# الحصول على عدد النقاط في مجموعة
curl http://localhost:6333/collections/legal_documents/points/count
```

### 3. اختبار البحث

```bash
# اختبار البحث
curl -X POST 'http://localhost:6333/collections/legal_documents/points/search' \
  -H 'Content-Type: application/json' \
  -d '{
    "vector": [0.1, 0.2, ...],
    "limit": 10
  }'
```

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل الاتصال

إذا واجهت مشاكل في الاتصال بـ Qdrant:

```bash
# التحقق من حالة خدمة Qdrant
docker ps | grep qdrant

# التحقق من سجلات Qdrant
docker logs qdrant

# التحقق من منافذ الاستماع
netstat -tuln | grep 6333
```

### مشاكل الأداء

إذا واجهت مشاكل في الأداء:

1. زيادة موارد الخادم (CPU/RAM)
2. تحسين إعدادات Qdrant في ملف التكوين:

```yaml
storage:
  optimizers:
    default_segment_number: 2
    memmap_threshold: 10000
    indexing_threshold: 20000
    flush_interval_sec: 30
```

### استعادة النسخ الاحتياطية

لاستعادة نسخة احتياطية:

```bash
# استعادة مجموعة من لقطة
curl -X PUT "http://localhost:6333/collections/legal_documents" \
  -H "Content-Type: application/json" \
  -d '{
    "vectors": {
      "size": 768,
      "distance": "Cosine"
    }
  }'

curl -X POST "http://localhost:6333/collections/legal_documents/snapshots/recover" \
  -H "Content-Type: application/json" \
  -d '{
    "location": "/path/to/backup/legal_documents.snapshot"
  }'
```

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
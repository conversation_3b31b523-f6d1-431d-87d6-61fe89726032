version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=**************************************/legalchat
      - SECRET_KEY=${SECRET_KEY:-supersecretkey}
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://frontend:5173
    volumes:
      - ./app:/app/app
      - ./uploads:/app/app/uploads
    restart: always

  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=legalchat
    ports:
      - "5432:5432"
    restart: always

volumes:
  postgres_data:
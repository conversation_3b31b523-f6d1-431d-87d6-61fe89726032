import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.security import create_access_token
from app.models.user import User
from app.models.chat import ChatSession
from app.models.payment import Subscription
from app.models.audit import AuditLog, ContentReport

def test_admin_get_users(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get all users (admin only)
    response = client.get(
        "/admin/users",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 2  # At least the test user and admin
    
    # Test with role filter
    response = client.get(
        "/admin/users?role=client",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(user["role"] == "client" for user in data)
    
    # Test with status filter
    response = client.get(
        "/admin/users?status=true",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(user["is_active"] for user in data)
    
    # Test with search filter
    response = client.get(
        f"/admin/users?search={test_user.email}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["email"] == test_user.email
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/users",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_get_chats(client: TestClient, test_admin: User, test_user: User, test_chat_session: ChatSession):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get all chat sessions (admin only)
    response = client.get(
        "/admin/chats",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any(session["id"] == test_chat_session.id for session in data)
    
    # Test with user_id filter
    response = client.get(
        f"/admin/chats?user_id={test_user.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(session["user_id"] == test_user.id for session in data)
    
    # Test with is_flagged filter
    response = client.get(
        "/admin/chats?is_flagged=false",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(not session["is_flagged"] for session in data)
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/chats",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_get_usage_report(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get usage report (admin only)
    response = client.get(
        "/admin/reports/usage",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_users" in data
    assert "active_users" in data
    assert "new_users" in data
    assert "total_sessions" in data
    assert "new_sessions" in data
    assert "total_messages" in data
    assert "new_messages" in data
    assert "avg_messages_per_session" in data
    
    # Test with date range
    start_date = (datetime.utcnow() - timedelta(days=7)).isoformat()
    end_date = datetime.utcnow().isoformat()
    response = client.get(
        f"/admin/reports/usage?start_date={start_date}&end_date={end_date}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/reports/usage",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_get_performance_report(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get performance report (admin only)
    response = client.get(
        "/admin/reports/performance",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "avg_response_time_ms" in data
    assert "p95_response_time_ms" in data
    assert "p99_response_time_ms" in data
    assert "error_rate" in data
    assert "uptime_percentage" in data
    assert "api_calls_total" in data
    assert "api_calls_per_day" in data
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/reports/performance",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_get_financial_report(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get financial report (admin only)
    response = client.get(
        "/admin/reports/financial",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_revenue" in data
    assert "subscription_counts" in data
    assert "new_subscriptions" in data
    assert "canceled_subscriptions" in data
    assert "monthly_recurring_revenue" in data
    assert "average_revenue_per_user" in data
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/reports/financial",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_update_ai_model(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test update AI model settings (admin only)
    response = client.post(
        "/admin/ai-models/update?model_name=gpt-4&temperature=0.8&max_tokens=2000",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "AI model settings updated successfully" in data["detail"]
    assert data["model_name"] == "gpt-4"
    assert data["temperature"] == 0.8
    assert data["max_tokens"] == 2000
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.post(
        "/admin/ai-models/update?model_name=gpt-4",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_admin_get_settings(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get system settings (admin only)
    response = client.get(
        "/admin/settings",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "general" in data
    assert "security" in data
    assert "chat" in data
    assert "ai" in data
    assert "payment" in data
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/admin/settings",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]
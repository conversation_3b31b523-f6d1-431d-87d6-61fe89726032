import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.security import create_access_token
from app.models.user import User
from app.models.citation import Citation, Tag

def test_create_citation(client: TestClient, test_admin: User, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test create citation
    response = client.post(
        "/citations",
        json={
            "title": "New Citation",
            "content": "This is a new citation content",
            "source": "New Source",
            "article_number": "456",
            "section": "New Section",
            "category": "New Category",
            "tags": ["tag1", "tag2"]
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "New Citation"
    assert data["content"] == "This is a new citation content"
    assert data["source"] == "New Source"
    assert data["article_number"] == "456"
    assert data["section"] == "New Section"
    assert data["category"] == "New Category"
    assert "tag1" in data["tags"]
    assert "tag2" in data["tags"]
    
    # Check that the citation was created in the database
    citation = db.query(Citation).filter(Citation.id == data["id"]).first()
    assert citation is not None
    assert citation.title == "New Citation"
    
    # Check that the tags were created
    tags = db.query(Tag).filter(Tag.name.in_(["tag1", "tag2"])).all()
    assert len(tags) == 2

def test_read_citations(client: TestClient, test_admin: User, test_citation: Citation):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get all citations
    response = client.get(
        "/citations",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any(citation["id"] == test_citation.id for citation in data)
    
    # Test with search filter
    response = client.get(
        f"/citations?search={test_citation.title}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert data[0]["title"] == test_citation.title

def test_read_citation(client: TestClient, test_admin: User, test_citation: Citation):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get specific citation
    response = client.get(
        f"/citations/{test_citation.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_citation.id
    assert data["title"] == test_citation.title
    assert data["content"] == test_citation.content
    
    # Test with non-existent citation
    response = client.get(
        "/citations/nonexistent-id",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 404
    assert "Citation not found" in response.json()["detail"]

def test_update_citation(client: TestClient, test_admin: User, test_citation: Citation, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test update citation
    new_title = "Updated Citation"
    response = client.put(
        f"/citations/{test_citation.id}",
        json={
            "title": new_title,
            "tags": ["updated-tag"]
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == new_title
    assert "updated-tag" in data["tags"]
    
    # Check that the citation was updated in the database
    db.refresh(test_citation)
    assert test_citation.title == new_title

def test_delete_citation(client: TestClient, test_admin: User, test_citation: Citation, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test delete citation
    response = client.delete(
        f"/citations/{test_citation.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 204
    
    # Check that the citation was deleted from the database
    citation = db.query(Citation).filter(Citation.id == test_citation.id).first()
    assert citation is None

def test_import_citations(client: TestClient, test_admin: User, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test import citations
    response = client.post(
        "/citations/import",
        json={
            "citations": [
                {
                    "title": "Imported Citation 1",
                    "content": "This is imported citation content 1",
                    "source": "Imported Source 1",
                    "tags": ["imported", "tag1"]
                },
                {
                    "title": "Imported Citation 2",
                    "content": "This is imported citation content 2",
                    "source": "Imported Source 2",
                    "tags": ["imported", "tag2"]
                }
            ]
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 201
    data = response.json()
    assert "Successfully imported 2 citations" in data["detail"]
    
    # Check that the citations were created in the database
    citations = db.query(Citation).filter(Citation.title.like("Imported Citation%")).all()
    assert len(citations) == 2
    
    # Check that the tags were created
    tags = db.query(Tag).filter(Tag.name.in_(["imported", "tag1", "tag2"])).all()
    assert len(tags) == 3
import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
import os

from app.core.security import create_access_token
from app.models.user import User
from app.models.chat import ChatSession, ChatMessage

def test_create_chat_session(client: TestClient, test_user: User, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test create chat session
    response = client.post(
        "/chat/sessions",
        json={"title": "Test Chat Session"},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Test Chat Session"
    assert data["user_id"] == test_user.id
    assert data["is_archived"] is False
    assert data["is_flagged"] is False
    
    # Check that the chat session was created in the database
    chat_session = db.query(ChatSession).filter(ChatSession.id == data["id"]).first()
    assert chat_session is not None
    assert chat_session.title == "Test Chat Session"

def test_read_chat_sessions(client: <PERSON><PERSON><PERSON>, test_user: User, test_chat_session: ChatSession):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test get all chat sessions
    response = client.get(
        "/chat/sessions",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any(session["id"] == test_chat_session.id for session in data)
    
    # Test with archived filter
    response = client.get(
        "/chat/sessions?archived=false",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(not session["is_archived"] for session in data)

def test_read_chat_session(client: TestClient, test_user: User, test_chat_session: ChatSession):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test get specific chat session
    response = client.get(
        f"/chat/sessions/{test_chat_session.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_chat_session.id
    assert data["title"] == test_chat_session.title
    assert "messages" in data
    assert len(data["messages"]) == 5  # From the fixture
    
    # Test with non-existent chat session
    response = client.get(
        "/chat/sessions/nonexistent-id",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 404
    assert "Chat session not found" in response.json()["detail"]

def test_update_chat_session(client: TestClient, test_user: User, test_chat_session: ChatSession, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test update chat session
    new_title = "Updated Chat Session"
    response = client.put(
        f"/chat/sessions/{test_chat_session.id}",
        json={"title": new_title, "is_archived": True},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == new_title
    assert data["is_archived"] is True
    
    # Check that the chat session was updated in the database
    db.refresh(test_chat_session)
    assert test_chat_session.title == new_title
    assert test_chat_session.is_archived is True

def test_delete_chat_session(client: TestClient, test_user: User, test_chat_session: ChatSession, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test delete chat session
    response = client.delete(
        f"/chat/sessions/{test_chat_session.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 204
    
    # Check that the chat session was deleted from the database
    chat_session = db.query(ChatSession).filter(ChatSession.id == test_chat_session.id).first()
    assert chat_session is None

def test_create_chat_message(client: TestClient, test_user: User, test_chat_session: ChatSession, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test create chat message
    response = client.post(
        f"/chat/sessions/{test_chat_session.id}/messages",
        json={"content": "Test message content"},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["content"] == "Test message content"
    assert data["chat_session_id"] == test_chat_session.id
    assert data["sender_type"] == "user"
    
    # Check that the message was created in the database
    message = db.query(ChatMessage).filter(ChatMessage.id == data["id"]).first()
    assert message is not None
    assert message.content == "Test message content"

def test_read_chat_messages(client: TestClient, test_user: User, test_chat_session: ChatSession):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test get all messages for a chat session
    response = client.get(
        f"/chat/sessions/{test_chat_session.id}/messages",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 5  # From the fixture
    
    # Test with non-existent chat session
    response = client.get(
        "/chat/sessions/nonexistent-id/messages",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 404
    assert "Chat session not found" in response.json()["detail"]
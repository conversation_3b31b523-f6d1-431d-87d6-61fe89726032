import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from app.core.security import create_access_token
from app.models.user import User, Profile

def test_read_users_me(client: TestClient, test_user: User):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test get current user
    response = client.get(
        "/users/me",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_user.id
    assert data["email"] == test_user.email
    assert data["full_name"] == test_user.full_name
    assert "profile" in data
    assert data["profile"]["id"] == test_user.id

def test_update_user_me(client: TestClient, test_user: User, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test update current user
    new_name = "Updated Name"
    response = client.put(
        "/users/me",
        json={"full_name": new_name},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["full_name"] == new_name
    
    # Check that the user was updated in the database
    db.refresh(test_user)
    assert test_user.full_name == new_name

def test_update_user_profile(client: TestClient, test_user: User, db: Session):
    # Create access token for the test user
    access_token = create_access_token(subject=test_user.id)
    
    # Test update user profile
    new_phone = "9876543210"
    new_bio = "Updated bio"
    response = client.put(
        "/users/me/profile",
        json={"phone_number": new_phone, "bio": new_bio},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["profile"]["phone_number"] == new_phone
    assert data["profile"]["bio"] == new_bio
    
    # Check that the profile was updated in the database
    profile = db.query(Profile).filter(Profile.id == test_user.id).first()
    assert profile.phone_number == new_phone
    assert profile.bio == new_bio

def test_read_users(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get all users (admin only)
    response = client.get(
        "/users",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 2  # At least the test user and admin
    
    # Test with role filter
    response = client.get(
        "/users?role=client",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert all(user["role"] == "client" for user in data)
    
    # Test with search filter
    response = client.get(
        f"/users?search={test_user.email}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["email"] == test_user.email
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        "/users",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_read_user(client: TestClient, test_admin: User, test_user: User):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test get specific user (admin only)
    response = client.get(
        f"/users/{test_user.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_user.id
    assert data["email"] == test_user.email
    assert "profile" in data
    
    # Test with non-existent user
    response = client.get(
        "/users/nonexistent-id",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 404
    assert "User not found" in response.json()["detail"]
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.get(
        f"/users/{test_user.id}",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_update_user_role(client: TestClient, test_admin: User, test_user: User, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test update user role (admin only)
    response = client.patch(
        f"/users/{test_user.id}/role?role=attorney",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["role"] == "attorney"
    
    # Check that the user role was updated in the database
    db.refresh(test_user)
    assert test_user.role == "attorney"
    
    # Test with invalid role
    response = client.patch(
        f"/users/{test_user.id}/role?role=invalid",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 400
    assert "Invalid role" in response.json()["detail"]
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.patch(
        f"/users/{test_user.id}/role?role=admin",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]

def test_update_user_status(client: TestClient, test_admin: User, test_user: User, db: Session):
    # Create access token for the admin
    access_token = create_access_token(subject=test_admin.id)
    
    # Test deactivate user (admin only)
    response = client.patch(
        f"/users/{test_user.id}/status?is_active=false",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["is_active"] is False
    
    # Check that the user status was updated in the database
    db.refresh(test_user)
    assert test_user.is_active is False
    
    # Test activate user
    response = client.patch(
        f"/users/{test_user.id}/status?is_active=true",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["is_active"] is True
    
    # Check that the user status was updated in the database
    db.refresh(test_user)
    assert test_user.is_active is True
    
    # Test deactivating self (should fail)
    response = client.patch(
        f"/users/{test_admin.id}/status?is_active=false",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    assert response.status_code == 400
    assert "Cannot deactivate your own account" in response.json()["detail"]
    
    # Test with non-admin user (should fail)
    user_token = create_access_token(subject=test_user.id)
    response = client.patch(
        f"/users/{test_user.id}/status?is_active=false",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
    assert "Not enough permissions" in response.json()["detail"]
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.security import create_access_token, create_refresh_token
from app.models.user import User, RefreshToken

def test_register(client: TestClient, db: Session):
    # Test user registration
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "full_name": "New User"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "New User"
    assert data["role"] == "client"
    assert data["is_active"] is True
    assert data["is_verified"] is False
    
    # Check that the user was created in the database
    user = db.query(User).filter(User.email == "<EMAIL>").first()
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.full_name == "New User"
    
    # Test registration with existing email
    response = client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "full_name": "Another User"
        }
    )
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]

def test_login(client: TestClient, test_user: User):
    # Test successful login
    response = client.post(
        "/auth/login",
        data={
            "username": test_user.email,
            "password": "password"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    
    # Test login with wrong password
    response = client.post(
        "/auth/login",
        data={
            "username": test_user.email,
            "password": "wrongpassword"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]
    
    # Test login with non-existent user
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "password"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]

def test_refresh_token(client: TestClient, test_user: User, db: Session):
    # Create a refresh token
    refresh_token = create_refresh_token(subject=test_user.id)
    
    # Store the refresh token in the database
    db_refresh_token = RefreshToken(
        id="test-refresh-token",
        token=refresh_token,
        user_id=test_user.id,
        expires_at=datetime.utcnow() + timedelta(days=7)
    )
    db.add(db_refresh_token)
    db.commit()
    
    # Test refresh token
    response = client.post(
        "/auth/refresh",
        json={"refresh_token": refresh_token}
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["refresh_token"] == refresh_token
    assert data["token_type"] == "bearer"
    
    # Test with invalid refresh token
    response = client.post(
        "/auth/refresh",
        json={"refresh_token": "invalid-token"}
    )
    assert response.status_code == 401

def test_logout(client: TestClient, test_user: User, db: Session):
    # Create a refresh token
    refresh_token = create_refresh_token(subject=test_user.id)
    
    # Store the refresh token in the database
    db_refresh_token = RefreshToken(
        id="test-refresh-token",
        token=refresh_token,
        user_id=test_user.id,
        expires_at=datetime.utcnow() + timedelta(days=7)
    )
    db.add(db_refresh_token)
    db.commit()
    
    # Test logout
    response = client.post(
        "/auth/logout",
        json={"refresh_token": refresh_token}
    )
    assert response.status_code == 200
    assert "Successfully logged out" in response.json()["detail"]
    
    # Check that the refresh token was revoked
    db_token = db.query(RefreshToken).filter(RefreshToken.token == refresh_token).first()
    assert db_token.is_revoked is True

def test_password_reset(client: TestClient, test_user: User):
    # Test password reset request
    response = client.post(
        "/auth/password-reset",
        json={"email": test_user.email}
    )
    assert response.status_code == 200
    assert "If your email is registered" in response.json()["detail"]
    
    # Test password reset request with non-existent email
    response = client.post(
        "/auth/password-reset",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "If your email is registered" in response.json()["detail"]

def test_activate_account(client: TestClient, test_user: User, db: Session):
    # Create an activation token
    token = create_access_token(
        subject=test_user.id,
        expires_delta=timedelta(hours=24)
    )
    
    # Set user as not verified
    test_user.is_verified = False
    db.commit()
    
    # Test account activation
    response = client.post(
        f"/auth/activate?token={token}"
    )
    assert response.status_code == 200
    assert "Account activated successfully" in response.json()["detail"]
    
    # Check that the user is now verified
    db.refresh(test_user)
    assert test_user.is_verified is True
    
    # Test with invalid token
    response = client.post(
        "/auth/activate?token=invalid-token"
    )
    assert response.status_code == 400
    assert "Invalid or expired token" in response.json()["detail"]
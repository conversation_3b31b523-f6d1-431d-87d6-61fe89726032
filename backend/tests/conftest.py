import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import os
import sys

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app
from app.db.session import Base, get_db
from app.core.security import get_password_hash
from app.models.user import User, Profile
from app.models.chat import ChatSession, ChatMessage
from app.models.citation import Citation, Tag

# Create an in-memory SQLite database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="function")
def db():
    # Create the database tables
    Base.metadata.create_all(bind=engine)
    
    # Create a new session for each test
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        
    # Drop the database tables
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def client(db):
    # Override the get_db dependency to use the test database
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as c:
        yield c
    
    # Remove the override
    app.dependency_overrides = {}

@pytest.fixture(scope="function")
def test_user(db):
    # Create a test user
    user = User(
        id="test-user-id",
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        full_name="Test User",
        role="client",
        is_active=True,
        is_verified=True
    )
    db.add(user)
    
    # Create a profile for the user
    profile = Profile(
        id=user.id,
        phone_number="1234567890",
        address="123 Test St",
        bio="Test bio"
    )
    db.add(profile)
    
    db.commit()
    
    return user

@pytest.fixture(scope="function")
def test_admin(db):
    # Create a test admin
    admin = User(
        id="test-admin-id",
        email="<EMAIL>",
        hashed_password=get_password_hash("password"),
        full_name="Test Admin",
        role="admin",
        is_active=True,
        is_verified=True
    )
    db.add(admin)
    
    # Create a profile for the admin
    profile = Profile(
        id=admin.id,
        phone_number="0987654321",
        address="456 Admin St",
        bio="Admin bio"
    )
    db.add(profile)
    
    db.commit()
    
    return admin

@pytest.fixture(scope="function")
def test_chat_session(db, test_user):
    # Create a test chat session
    chat_session = ChatSession(
        id="test-session-id",
        title="Test Chat Session",
        user_id=test_user.id,
        is_archived=False,
        is_flagged=False
    )
    db.add(chat_session)
    
    # Create some test messages
    messages = [
        ChatMessage(
            id=f"test-message-{i}",
            chat_session_id=chat_session.id,
            sender_type="user" if i % 2 == 0 else "system",
            content=f"Test message {i}",
            is_flagged=False
        )
        for i in range(5)
    ]
    db.add_all(messages)
    
    db.commit()
    
    return chat_session

@pytest.fixture(scope="function")
def test_citation(db, test_admin):
    # Create a test citation
    citation = Citation(
        id="test-citation-id",
        title="Test Citation",
        content="This is a test citation content",
        source="Test Source",
        article_number="123",
        section="Test Section",
        category="Test Category",
        created_by=test_admin.id
    )
    db.add(citation)
    
    # Create some test tags
    tags = [
        Tag(id=f"test-tag-{i}", name=f"test-tag-{i}")
        for i in range(3)
    ]
    db.add_all(tags)
    
    db.commit()
    
    return citation
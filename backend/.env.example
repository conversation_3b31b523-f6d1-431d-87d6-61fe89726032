# Project settings
PROJECT_NAME="Arabic AI API"
PROJECT_DESCRIPTION="API for Arabic AI platform with multiple models and services"
PROJECT_VERSION="0.1.0"

# Security settings
SECRET_KEY=supersecretkey
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/arabic_ai

# CORS settings
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8000

# Email settings
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.example.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="Arabic AI Platform"

# Moyasar Payment settings
MOYASAR_API_KEY=sk_test_LKEQhH1bAQ2VHCY1vq5DZygsa9VCSZBDoZ9mqWKM
MOYASAR_PUBLISHABLE_KEY=pk_test_n6Tk44mXL86NHH83VRjJ7uCo2wVASrY5gsg9q9fi
MOYASAR_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxx
from pydantic_settings import BaseSettings
from typing import List, Optional, Dict, Any
import os
from pathlib import Path
import base64

class Settings(BaseSettings):
    # Project settings
    PROJECT_NAME: str = "Arabic AI API"
    PROJECT_DESCRIPTION: str = "API for Arabic AI platform with multiple models and services"
    PROJECT_VERSION: str = "0.1.0"
    
    # API settings
    API_V1_STR: str = "/api"
    
    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecretkey")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/arabic_ai")
    
    # CORS settings
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8000",
        "https://yourdomain.com",
    ]
    
    # AI model settings
    DEFAULT_LLM_MODEL: str = "Llama 3.2 (8B)"
    DEFAULT_TEMPERATURE: float = 0.7
    DEFAULT_MAX_TOKENS: int = 1000
    
    # Moyasar settings
    MOYASAR_API_KEY: str = os.getenv("MOYASAR_API_KEY", "")
    MOYASAR_PUBLISHABLE_KEY: str = os.getenv("MOYASAR_PUBLISHABLE_KEY", "")
    MOYASAR_WEBHOOK_SECRET: str = os.getenv("MOYASAR_WEBHOOK_SECRET", "")
    
    # Computed properties
    @property
    def MOYASAR_API_KEY_BASE64(self) -> str:
        """
        Return the Moyasar API key encoded in base64 for Basic Auth
        """
        if not self.MOYASAR_API_KEY:
            return ""
        return base64.b64encode(f"{self.MOYASAR_API_KEY}:".encode()).decode()
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
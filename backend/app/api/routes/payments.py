from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import uuid
import os
import json
import hmac
import hashlib
import requests
from datetime import datetime, timedelta

from app.db.session import get_db
from app.models.user import User
from app.models.payment import Subscription, Payment, Invoice
from app.schemas.payment import (
    PaymentInitiate,
    Payment as PaymentSchema,
    PaymentResponse,
    PaymentWebhook,
    PaymentStatusResponse,
    PaymentHistoryResponse
)
from app.core.config import settings
from app.core.security import get_current_active_user
from app.api.utils.email import send_invoice_email

router = APIRouter()

@router.post("/initiate", response_model=PaymentResponse)
async def initiate_payment(
    payment_data: PaymentInitiate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Initiate a payment with Moyasar
    """
    # Validate plan
    valid_plans = ["basic", "premium", "enterprise"]
    if payment_data.plan not in valid_plans:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid plan. Must be one of: {', '.join(valid_plans)}",
        )
    
    # Get plan details
    plan_details = {
        "basic": {"amount": 199, "duration_months": 1},
        "premium": {"amount": 499, "duration_months": 1},
        "enterprise": {"amount": 999, "duration_months": 1}
    }
    
    # Calculate amount in halalas (Moyasar uses the smallest currency unit)
    amount_halalas = int(plan_details[payment_data.plan]["amount"] * 100)
    
    # Create a payment record
    payment_id = str(uuid.uuid4())
    db_payment = Payment(
        id=payment_id,
        user_id=current_user.id,
        amount=plan_details[payment_data.plan]["amount"],
        currency="SAR",
        status="pending",
        payment_method=payment_data.payment_method,
        payment_id=f"moyasar_{payment_id}"
    )
    db.add(db_payment)
    db.commit()
    
    # Generate callback URL
    callback_url = payment_data.callback_url or f"{settings.API_V1_STR}/payments/callback"
    
    # In a real application, this would be the response from Moyasar
    moyasar_response = {
        "id": f"moyasar_{payment_id}",
        "status": "initiated",
        "amount": amount_halalas,
        "currency": "SAR",
        "description": f"{payment_data.plan.capitalize()} Plan Subscription",
        "callback_url": callback_url,
        "source": {
            "type": payment_data.payment_method,
            "transaction_url": f"https://pay.moyasar.com/{payment_id}"
        }
    }
    
    return {
        "payment_id": payment_id,
        "moyasar_id": moyasar_response["id"],
        "amount": plan_details[payment_data.plan]["amount"],
        "currency": "SAR",
        "transaction_url": moyasar_response["source"]["transaction_url"],
        "callback_url": callback_url
    }

@router.post("/webhook", status_code=status.HTTP_200_OK)
async def payment_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Handle Moyasar webhook callbacks
    """
    # Verify webhook signature
    webhook_secret = settings.MOYASAR_WEBHOOK_SECRET
    webhook_signature = request.headers.get("Moyasar-Signature")
    
    if webhook_secret and webhook_signature:
        # Read request body
        body = await request.body()
        body_str = body.decode("utf-8")
        
        # Compute signature
        computed_signature = hmac.new(
            webhook_secret.encode(),
            body_str.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Verify signature
        if computed_signature != webhook_signature:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid webhook signature",
            )
    
    # Parse webhook data
    webhook_data = await request.json()
    
    # Find the payment by Moyasar ID
    moyasar_id = webhook_data["id"]
    payment = db.query(Payment).filter(Payment.payment_id == moyasar_id).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found",
        )
    
    # Update payment status
    payment.status = webhook_data["status"]
    db.commit()
    
    # If payment is completed, create subscription and invoice
    if webhook_data["status"] == "paid":
        # Get user
        user = db.query(User).filter(User.id == payment.user_id).first()
        
        # Determine plan from payment amount
        plan = "basic"
        if payment.amount >= 499:
            plan = "premium"
        if payment.amount >= 999:
            plan = "enterprise"
        
        # Calculate subscription duration
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=30)  # 30-day subscription
        
        # Create or update subscription
        existing_subscription = db.query(Subscription).filter(
            Subscription.user_id == user.id,
            Subscription.status == "active"
        ).first()
        
        if existing_subscription:
            # Update existing subscription
            existing_subscription.plan = plan
            existing_subscription.end_date = end_date
            existing_subscription.updated_at = datetime.utcnow()
            subscription_id = existing_subscription.id
            db.commit()
        else:
            # Create new subscription
            subscription_id = str(uuid.uuid4())
            db_subscription = Subscription(
                id=subscription_id,
                user_id=user.id,
                plan=plan,
                status="active",
                start_date=start_date,
                end_date=end_date,
                auto_renew=True
            )
            db.add(db_subscription)
            db.commit()
        
        # Update payment with subscription ID
        payment.subscription_id = subscription_id
        db.commit()
        
        # Create invoice
        invoice_number = f"INV-{datetime.utcnow().strftime('%Y%m%d')}-{payment.id[:8]}"
        tax_amount = payment.amount * 0.15  # 15% VAT
        
        db_invoice = Invoice(
            id=str(uuid.uuid4()),
            payment_id=payment.id,
            invoice_number=invoice_number,
            invoice_date=datetime.utcnow(),
            due_date=datetime.utcnow(),  # Due immediately since it's already paid
            total_amount=payment.amount,
            tax_amount=tax_amount,
            status="paid"
        )
        db.add(db_invoice)
        db.commit()
        
        # Send invoice email in background
        background_tasks.add_task(
            send_invoice_email,
            email=user.email,
            invoice_number=invoice_number,
            amount=payment.amount,
            plan=plan
        )
        
        # Update user role based on plan
        if plan == "basic":
            user.role = "subscriber_basic"
        elif plan == "premium":
            user.role = "subscriber_premium"
        elif plan == "enterprise":
            user.role = "subscriber_enterprise"
        
        db.commit()
    
    return {"detail": "Webhook processed successfully"}

@router.get("/status/{payment_id}", response_model=PaymentStatusResponse)
async def get_payment_status(
    payment_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get the status of a payment
    """
    # Find the payment in the database
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found",
        )
    
    # If payment is pending, check with Moyasar
    if payment.status == "pending":
        # In a real application, you would call Moyasar API to check the status
        # For this example, we'll simulate a response
        
        # Simulate a 50% chance of success for demonstration
        import random
        if random.random() > 0.5:
            payment.status = "paid"
        else:
            payment.status = "failed"
        
        db.commit()
    
    return {
        "payment_id": payment.id,
        "moyasar_id": payment.payment_id,
        "status": payment.status,
        "amount": payment.amount,
        "currency": payment.currency,
        "created_at": payment.created_at.isoformat()
    }

@router.get("/history", response_model=PaymentHistoryResponse)
async def get_payment_history(
    skip: int = 0,
    limit: int = 10,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get payment history for the current user
    """
    # Get total count
    total = db.query(Payment).filter(Payment.user_id == current_user.id).count()
    
    # Get payments with pagination
    payments = db.query(Payment).filter(
        Payment.user_id == current_user.id
    ).order_by(Payment.created_at.desc()).offset(skip).limit(limit).all()
    
    return {
        "payments": payments,
        "total": total
    }

@router.get("/{payment_id}", response_model=PaymentSchema)
async def get_payment_details(
    payment_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get details of a specific payment
    """
    # Find the payment in the database
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found",
        )
    
    return payment
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from app.core.security import validate_admin_access
from app.db.session import get_db
from app.models.user import User
from app.models.chat import ChatSession, ChatMessage
from app.models.payment import Subscription, Payment, Invoice
from app.models.audit import AuditLog, ContentReport
from app.schemas.user import User as UserSchema
from app.schemas.chat import ChatSession as ChatSessionSchema
from app.schemas.payment import Subscription as SubscriptionSchema, Payment as PaymentSchema
from app.schemas.admin import (
    UsageReport,
    PerformanceReport,
    FinancialReport,
    ContentReportRead,
    AuditLogRead
)

router = APIRouter()

@router.get("/users", response_model=List[UserSchema])
async def admin_get_users(
    skip: int = 0,
    limit: int = 100,
    role: Optional[str] = None,
    status: Optional[bool] = None,
    search: Optional[str] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get all users with filtering options
    """
    query = db.query(User)
    
    # Apply filters
    if role:
        query = query.filter(User.role == role)
    
    if status is not None:
        query = query.filter(User.is_active == status)
    
    if search:
        query = query.filter(
            (User.email.ilike(f"%{search}%")) | 
            (User.full_name.ilike(f"%{search}%"))
        )
    
    users = query.offset(skip).limit(limit).all()
    return users

@router.get("/chats", response_model=List[ChatSessionSchema])
async def admin_get_chats(
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[str] = None,
    is_flagged: Optional[bool] = None,
    is_archived: Optional[bool] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get all chat sessions with filtering options
    """
    query = db.query(ChatSession)
    
    # Apply filters
    if user_id:
        query = query.filter(ChatSession.user_id == user_id)
    
    if is_flagged is not None:
        query = query.filter(ChatSession.is_flagged == is_flagged)
    
    if is_archived is not None:
        query = query.filter(ChatSession.is_archived == is_archived)
    
    if start_date:
        query = query.filter(ChatSession.created_at >= start_date)
    
    if end_date:
        query = query.filter(ChatSession.created_at <= end_date)
    
    chat_sessions = query.order_by(ChatSession.updated_at.desc()).offset(skip).limit(limit).all()
    return chat_sessions

@router.get("/reports/usage", response_model=UsageReport)
async def admin_get_usage_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get usage reports
    """
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get total users
    total_users = db.query(User).count()
    
    # Get active users (users who logged in during the date range)
    active_users = db.query(User).filter(
        User.last_login.between(start_date, end_date)
    ).count()
    
    # Get new users (users created during the date range)
    new_users = db.query(User).filter(
        User.created_at.between(start_date, end_date)
    ).count()
    
    # Get total chat sessions
    total_sessions = db.query(ChatSession).count()
    
    # Get new chat sessions (created during the date range)
    new_sessions = db.query(ChatSession).filter(
        ChatSession.created_at.between(start_date, end_date)
    ).count()
    
    # Get total messages
    total_messages = db.query(ChatMessage).count()
    
    # Get new messages (created during the date range)
    new_messages = db.query(ChatMessage).filter(
        ChatMessage.created_at.between(start_date, end_date)
    ).count()
    
    # Calculate average messages per session
    avg_messages_per_session = total_messages / total_sessions if total_sessions > 0 else 0
    
    return {
        "start_date": start_date,
        "end_date": end_date,
        "total_users": total_users,
        "active_users": active_users,
        "new_users": new_users,
        "total_sessions": total_sessions,
        "new_sessions": new_sessions,
        "total_messages": total_messages,
        "new_messages": new_messages,
        "avg_messages_per_session": avg_messages_per_session
    }

@router.get("/reports/performance", response_model=PerformanceReport)
async def admin_get_performance_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get performance reports
    """
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # In a real application, you would calculate these metrics from actual data
    # For this example, we'll return placeholder values
    
    return {
        "start_date": start_date,
        "end_date": end_date,
        "avg_response_time_ms": 250,
        "p95_response_time_ms": 500,
        "p99_response_time_ms": 750,
        "error_rate": 0.02,
        "uptime_percentage": 99.9,
        "api_calls_total": 15000,
        "api_calls_per_day": 500
    }

@router.get("/reports/financial", response_model=FinancialReport)
async def admin_get_financial_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get financial reports
    """
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get total revenue
    total_revenue = db.query(func.sum(Payment.amount)).filter(
        Payment.status == "completed",
        Payment.created_at.between(start_date, end_date)
    ).scalar() or 0
    
    # Get subscription counts by plan
    basic_count = db.query(Subscription).filter(
        Subscription.plan == "basic",
        Subscription.status == "active"
    ).count()
    
    premium_count = db.query(Subscription).filter(
        Subscription.plan == "premium",
        Subscription.status == "active"
    ).count()
    
    enterprise_count = db.query(Subscription).filter(
        Subscription.plan == "enterprise",
        Subscription.status == "active"
    ).count()
    
    # Get new subscriptions
    new_subscriptions = db.query(Subscription).filter(
        Subscription.created_at.between(start_date, end_date)
    ).count()
    
    # Get canceled subscriptions
    canceled_subscriptions = db.query(Subscription).filter(
        Subscription.status == "canceled",
        Subscription.updated_at.between(start_date, end_date)
    ).count()
    
    return {
        "start_date": start_date,
        "end_date": end_date,
        "total_revenue": total_revenue,
        "subscription_counts": {
            "basic": basic_count,
            "premium": premium_count,
            "enterprise": enterprise_count
        },
        "new_subscriptions": new_subscriptions,
        "canceled_subscriptions": canceled_subscriptions,
        "monthly_recurring_revenue": total_revenue / 30 * 30,  # Simplified calculation
        "average_revenue_per_user": total_revenue / (basic_count + premium_count + enterprise_count) if (basic_count + premium_count + enterprise_count) > 0 else 0
    }

@router.get("/content-reports", response_model=List[ContentReportRead])
async def admin_get_content_reports(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get content reports
    """
    query = db.query(ContentReport)
    
    # Apply filters
    if status:
        query = query.filter(ContentReport.status == status)
    
    reports = query.order_by(ContentReport.created_at.desc()).offset(skip).limit(limit).all()
    
    result = []
    for report in reports:
        # Get reporter and reviewer info
        reporter = db.query(User).filter(User.id == report.reporter_id).first() if report.reporter_id else None
        reviewer = db.query(User).filter(User.id == report.reviewed_by).first() if report.reviewed_by else None
        
        result.append({
            **report.__dict__,
            "reporter_name": reporter.full_name if reporter else None,
            "reviewer_name": reviewer.full_name if reviewer else None
        })
    
    return result

@router.patch("/content-reports/{report_id}", response_model=ContentReportRead)
async def admin_update_content_report(
    report_id: str,
    status: str,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to update a content report status
    """
    # Validate status
    valid_statuses = ["pending", "approved", "rejected"]
    if status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}",
        )
    
    # Get report
    report = db.query(ContentReport).filter(ContentReport.id == report_id).first()
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found",
        )
    
    # Update report
    report.status = status
    report.reviewed_by = current_user.id
    report.reviewed_at = datetime.utcnow()
    
    db.commit()
    db.refresh(report)
    
    # Get reporter and reviewer info
    reporter = db.query(User).filter(User.id == report.reporter_id).first() if report.reporter_id else None
    reviewer = db.query(User).filter(User.id == report.reviewed_by).first() if report.reviewed_by else None
    
    return {
        **report.__dict__,
        "reporter_name": reporter.full_name if reporter else None,
        "reviewer_name": reviewer.full_name if reviewer else None
    }

@router.get("/audit-log", response_model=List[AuditLogRead])
async def admin_get_audit_log(
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[str] = None,
    action: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get audit logs
    """
    query = db.query(AuditLog)
    
    # Apply filters
    if user_id:
        query = query.filter(AuditLog.user_id == user_id)
    
    if action:
        query = query.filter(AuditLog.action == action)
    
    if start_date:
        query = query.filter(AuditLog.created_at >= start_date)
    
    if end_date:
        query = query.filter(AuditLog.created_at <= end_date)
    
    logs = query.order_by(AuditLog.created_at.desc()).offset(skip).limit(limit).all()
    
    result = []
    for log in logs:
        # Get user info
        user = db.query(User).filter(User.id == log.user_id).first() if log.user_id else None
        
        result.append({
            **log.__dict__,
            "user_name": user.full_name if user else None
        })
    
    return result

@router.post("/ai-models/update", status_code=status.HTTP_200_OK)
async def admin_update_ai_model(
    model_name: str,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    current_user: User = Depends(validate_admin_access)
):
    """
    Admin endpoint to update AI model settings
    """
    # In a real application, you would update the AI model settings in your database or configuration
    # For this example, we'll just return success
    
    return {
        "detail": "AI model settings updated successfully",
        "model_name": model_name,
        "temperature": temperature,
        "max_tokens": max_tokens
    }

@router.get("/subscriptions", response_model=List[SubscriptionSchema])
async def admin_get_subscriptions(
    skip: int = 0,
    limit: int = 100,
    plan: Optional[str] = None,
    status: Optional[str] = None,
    user_id: Optional[str] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to get all subscriptions
    """
    query = db.query(Subscription)
    
    # Apply filters
    if plan:
        query = query.filter(Subscription.plan == plan)
    
    if status:
        query = query.filter(Subscription.status == status)
    
    if user_id:
        query = query.filter(Subscription.user_id == user_id)
    
    subscriptions = query.order_by(Subscription.created_at.desc()).offset(skip).limit(limit).all()
    return subscriptions

@router.patch("/subscriptions/{subscription_id}", response_model=SubscriptionSchema)
async def admin_update_subscription(
    subscription_id: str,
    plan: Optional[str] = None,
    status: Optional[str] = None,
    auto_renew: Optional[bool] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to update a subscription
    """
    # Get subscription
    subscription = db.query(Subscription).filter(Subscription.id == subscription_id).first()
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found",
        )
    
    # Update fields
    if plan is not None:
        subscription.plan = plan
    
    if status is not None:
        subscription.status = status
    
    if auto_renew is not None:
        subscription.auto_renew = auto_renew
    
    if end_date is not None:
        subscription.end_date = end_date
    
    db.commit()
    db.refresh(subscription)
    
    return subscription

@router.get("/settings", status_code=status.HTTP_200_OK)
async def admin_get_settings(
    current_user: User = Depends(validate_admin_access)
):
    """
    Admin endpoint to get system settings
    """
    # In a real application, you would retrieve settings from your database
    # For this example, we'll return placeholder values
    
    return {
        "general": {
            "site_name": "Legal Chat Platform",
            "maintenance_mode": False,
            "registration_enabled": True
        },
        "security": {
            "max_login_attempts": 5,
            "session_timeout_minutes": 30,
            "password_reset_expiry_hours": 24
        },
        "chat": {
            "max_message_length": 4000,
            "max_file_size_mb": 10,
            "allowed_file_types": [".pdf", ".docx", ".doc", ".txt", ".jpg", ".jpeg", ".png"]
        },
        "ai": {
            "model_name": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000
        },
        "payment": {
            "currency": "SAR",
            "tax_percentage": 15,
            "payment_methods": ["creditcard", "applepay", "stcpay"]
        }
    }
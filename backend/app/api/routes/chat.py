from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
import httpx
import json
from typing import Dict, Any, Optional

from app.db.session import get_db
from app.models.external_api import ExternalAPI
from app.schemas.chat import ChatRequest, ChatResponse

router = APIRouter()

@router.post("", response_model=ChatResponse)
async def chat(
    request: ChatRequest = Body(...),
    db: Session = Depends(get_db)
):
    """
    Send a message to the appropriate AI model based on user's plan
    """
    # Determine available plan levels based on user's plan
    available_plan_levels = []
    if request.user_plan == "free":
        available_plan_levels = ["free"]
    elif request.user_plan == "basic":
        available_plan_levels = ["free", "basic"]
    elif request.user_plan == "premium":
        available_plan_levels = ["free", "basic", "premium"]
    elif request.user_plan == "enterprise":
        available_plan_levels = ["free", "basic", "premium", "enterprise"]
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user plan",
        )
    
    # Get the best available LLM model for the user's plan
    model = db.query(ExternalAPI).filter(
        ExternalAPI.type == "llm",
        ExternalAPI.plan_level.in_(available_plan_levels),
        ExternalAPI.is_active == True
    ).order_by(
        # Order by plan level in descending order to get the best available model
        # This uses a CASE statement to map plan levels to numeric values
        case(
            (ExternalAPI.plan_level == "enterprise", 4),
            (ExternalAPI.plan_level == "premium", 3),
            (ExternalAPI.plan_level == "basic", 2),
            (ExternalAPI.plan_level == "free", 1),
        ).desc()
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No suitable model found for your plan",
        )
    
    # In a real application, we would call the external API
    # For this example, we'll simulate a response
    try:
        # Parse the model config
        config = model.config if model.config else {}
        
        # Simulate calling the external API
        # In a real application, you would use httpx or requests to call the actual API
        # async with httpx.AsyncClient() as client:
        #     response = await client.post(
        #         model.endpoint,
        #         json={
        #             "prompt": request.message,
        #             "temperature": config.get("temperature", 0.7),
        #             "max_tokens": config.get("max_tokens", 1000),
        #             "system_prompt": config.get("system_prompt", "You are a helpful assistant.")
        #         },
        #         headers={"Authorization": f"Bearer {model.api_key}"} if model.api_key else {}
        #     )
        #     result = response.json()
        
        # Simulate a response
        result = {
            "response": f"This is a simulated response from {model.name} to your message: '{request.message}'",
            "model": model.name,
            "tokens_used": len(request.message) // 2,  # Simplified token count
        }
        
        return ChatResponse(
            response=result["response"],
            model=result["model"],
            tokens_used=result["tokens_used"],
            session_id=request.session_id
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calling AI model: {str(e)}",
        )
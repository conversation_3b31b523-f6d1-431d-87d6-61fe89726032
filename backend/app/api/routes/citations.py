from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

from app.core.security import get_current_active_user, validate_admin_access
from app.db.session import get_db
from app.models.user import User
from app.models.citation import Citation, Tag, citation_tags
from app.schemas.citation import (
    Citation as CitationSchema,
    CitationCreate,
    CitationUpdate,
    CitationImport
)

router = APIRouter()

@router.post("", response_model=CitationSchema)
async def create_citation(
    citation: CitationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new citation
    """
    # Create citation
    db_citation = Citation(
        id=str(uuid.uuid4()),
        title=citation.title,
        content=citation.content,
        source=citation.source,
        source_url=citation.source_url,
        article_number=citation.article_number,
        section=citation.section,
        category=citation.category,
        created_by=current_user.id
    )
    db.add(db_citation)
    db.commit()
    
    # Add tags if provided
    if citation.tags:
        for tag_name in citation.tags:
            # Check if tag exists
            tag = db.query(Tag).filter(Tag.name == tag_name).first()
            if not tag:
                # Create new tag
                tag = Tag(id=str(uuid.uuid4()), name=tag_name)
                db.add(tag)
                db.commit()
            
            # Add tag to citation
            db.execute(
                citation_tags.insert().values(
                    citation_id=db_citation.id,
                    tag_id=tag.id
                )
            )
    
    db.commit()
    db.refresh(db_citation)
    
    # Get tags for response
    tags = db.query(Tag).join(
        citation_tags,
        citation_tags.c.tag_id == Tag.id
    ).filter(
        citation_tags.c.citation_id == db_citation.id
    ).all()
    
    return {
        **db_citation.__dict__,
        "tags": [tag.name for tag in tags]
    }

@router.get("", response_model=List[CitationSchema])
async def read_citations(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category: Optional[str] = None,
    tag: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve citations with optional filtering
    """
    query = db.query(Citation)
    
    # Apply filters
    if search:
        query = query.filter(
            (Citation.title.ilike(f"%{search}%")) |
            (Citation.content.ilike(f"%{search}%")) |
            (Citation.article_number.ilike(f"%{search}%"))
        )
    
    if category:
        query = query.filter(Citation.category == category)
    
    if tag:
        query = query.join(
            citation_tags,
            citation_tags.c.citation_id == Citation.id
        ).join(
            Tag,
            Tag.id == citation_tags.c.tag_id
        ).filter(
            Tag.name == tag
        )
    
    citations = query.offset(skip).limit(limit).all()
    
    # Get tags for each citation
    result = []
    for citation in citations:
        tags = db.query(Tag).join(
            citation_tags,
            citation_tags.c.tag_id == Tag.id
        ).filter(
            citation_tags.c.citation_id == citation.id
        ).all()
        
        result.append({
            **citation.__dict__,
            "tags": [tag.name for tag in tags]
        })
    
    return result

@router.get("/{citation_id}", response_model=CitationSchema)
async def read_citation(
    citation_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific citation by id
    """
    citation = db.query(Citation).filter(Citation.id == citation_id).first()
    if not citation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Citation not found",
        )
    
    # Get tags
    tags = db.query(Tag).join(
        citation_tags,
        citation_tags.c.tag_id == Tag.id
    ).filter(
        citation_tags.c.citation_id == citation.id
    ).all()
    
    return {
        **citation.__dict__,
        "tags": [tag.name for tag in tags]
    }

@router.put("/{citation_id}", response_model=CitationSchema)
async def update_citation(
    citation_id: str,
    citation: CitationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update a citation
    """
    db_citation = db.query(Citation).filter(Citation.id == citation_id).first()
    if not db_citation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Citation not found",
        )
    
    # Check if user is admin or the creator of the citation
    if current_user.role != "admin" and db_citation.created_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )
    
    # Update fields
    for field, value in citation.dict(exclude_unset=True, exclude={"tags"}).items():
        setattr(db_citation, field, value)
    
    # Update tags if provided
    if citation.tags is not None:
        # Remove existing tags
        db.execute(
            citation_tags.delete().where(
                citation_tags.c.citation_id == citation_id
            )
        )
        
        # Add new tags
        for tag_name in citation.tags:
            # Check if tag exists
            tag = db.query(Tag).filter(Tag.name == tag_name).first()
            if not tag:
                # Create new tag
                tag = Tag(id=str(uuid.uuid4()), name=tag_name)
                db.add(tag)
                db.commit()
            
            # Add tag to citation
            db.execute(
                citation_tags.insert().values(
                    citation_id=citation_id,
                    tag_id=tag.id
                )
            )
    
    db.commit()
    db.refresh(db_citation)
    
    # Get tags for response
    tags = db.query(Tag).join(
        citation_tags,
        citation_tags.c.tag_id == Tag.id
    ).filter(
        citation_tags.c.citation_id == db_citation.id
    ).all()
    
    return {
        **db_citation.__dict__,
        "tags": [tag.name for tag in tags]
    }

@router.delete("/{citation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_citation(
    citation_id: str,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Delete a citation (admin only)
    """
    db_citation = db.query(Citation).filter(Citation.id == citation_id).first()
    if not db_citation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Citation not found",
        )
    
    # Delete the citation (cascade will delete tag associations)
    db.delete(db_citation)
    db.commit()
    
    return None

@router.post("/import", status_code=status.HTTP_201_CREATED)
async def import_citations(
    citations: CitationImport,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Import multiple citations (admin only)
    """
    imported_count = 0
    
    for citation in citations.citations:
        # Create citation
        db_citation = Citation(
            id=str(uuid.uuid4()),
            title=citation.title,
            content=citation.content,
            source=citation.source,
            source_url=citation.source_url,
            article_number=citation.article_number,
            section=citation.section,
            category=citation.category,
            created_by=current_user.id
        )
        db.add(db_citation)
        db.commit()
        
        # Add tags if provided
        if citation.tags:
            for tag_name in citation.tags:
                # Check if tag exists
                tag = db.query(Tag).filter(Tag.name == tag_name).first()
                if not tag:
                    # Create new tag
                    tag = Tag(id=str(uuid.uuid4()), name=tag_name)
                    db.add(tag)
                    db.commit()
                
                # Add tag to citation
                db.execute(
                    citation_tags.insert().values(
                        citation_id=db_citation.id,
                        tag_id=tag.id
                    )
                )
        
        db.commit()
        imported_count += 1
    
    return {"detail": f"Successfully imported {imported_count} citations"}
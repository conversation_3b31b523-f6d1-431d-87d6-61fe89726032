from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.db.session import get_db
from app.models.external_api import ExternalAPI
from app.schemas.external_api import ExternalAPIRead, ExternalAPICreate, ExternalAPIUpdate

router = APIRouter()

@router.get("/active", response_model=ExternalAPIRead)
async def get_active_api(
    type: str = Query(..., description="API type (llm, speech, vision, analysis, translation)"),
    plan: str = Query(..., description="User plan level (free, basic, premium, enterprise)"),
    db: Session = Depends(get_db)
):
    """
    Get an active API model based on type and user plan level
    """
    # Validate type
    valid_types = ["llm", "speech", "vision", "analysis", "translation"]
    if type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid API type. Must be one of: {', '.join(valid_types)}",
        )
    
    # Validate plan
    valid_plans = ["free", "basic", "premium", "enterprise"]
    if plan not in valid_plans:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid plan level. Must be one of: {', '.join(valid_plans)}",
        )
    
    # Get available APIs for the plan level
    available_plan_levels = []
    if plan == "free":
        available_plan_levels = ["free"]
    elif plan == "basic":
        available_plan_levels = ["free", "basic"]
    elif plan == "premium":
        available_plan_levels = ["free", "basic", "premium"]
    elif plan == "enterprise":
        available_plan_levels = ["free", "basic", "premium", "enterprise"]
    
    # Query for active API of the specified type and available for the plan level
    api = db.query(ExternalAPI).filter(
        ExternalAPI.type == type,
        ExternalAPI.plan_level.in_(available_plan_levels),
        ExternalAPI.is_active == True
    ).order_by(
        # Order by plan level in descending order to get the best available model
        # This uses a CASE statement to map plan levels to numeric values
        case(
            (ExternalAPI.plan_level == "enterprise", 4),
            (ExternalAPI.plan_level == "premium", 3),
            (ExternalAPI.plan_level == "basic", 2),
            (ExternalAPI.plan_level == "free", 1),
        ).desc()
    ).first()
    
    if not api:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No active API found for type {type} and plan {plan}",
        )
    
    return api

@router.get("", response_model=List[ExternalAPIRead])
async def get_all_apis(
    type: Optional[str] = None,
    plan_level: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """
    Get all external APIs with optional filtering
    """
    query = db.query(ExternalAPI)
    
    if type:
        query = query.filter(ExternalAPI.type == type)
    
    if plan_level:
        query = query.filter(ExternalAPI.plan_level == plan_level)
    
    if is_active is not None:
        query = query.filter(ExternalAPI.is_active == is_active)
    
    apis = query.all()
    return apis

@router.post("", response_model=ExternalAPIRead, status_code=status.HTTP_201_CREATED)
async def create_api(
    api_in: ExternalAPICreate,
    db: Session = Depends(get_db)
):
    """
    Create a new external API
    """
    db_api = ExternalAPI(**api_in.dict())
    db.add(db_api)
    db.commit()
    db.refresh(db_api)
    return db_api

@router.get("/{api_id}", response_model=ExternalAPIRead)
async def get_api(
    api_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a specific external API by ID
    """
    api = db.query(ExternalAPI).filter(ExternalAPI.id == api_id).first()
    if not api:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API not found",
        )
    return api

@router.put("/{api_id}", response_model=ExternalAPIRead)
async def update_api(
    api_id: str,
    api_in: ExternalAPIUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an external API
    """
    api = db.query(ExternalAPI).filter(ExternalAPI.id == api_id).first()
    if not api:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API not found",
        )
    
    # Update fields
    for field, value in api_in.dict(exclude_unset=True).items():
        setattr(api, field, value)
    
    db.commit()
    db.refresh(api)
    return api

@router.delete("/{api_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_api(
    api_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete an external API
    """
    api = db.query(ExternalAPI).filter(ExternalAPI.id == api_id).first()
    if not api:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API not found",
        )
    
    db.delete(api)
    db.commit()
    return None
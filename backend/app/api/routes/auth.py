from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import uuid

from app.core.config import settings
from app.core.security import (
    get_password_hash, 
    verify_password, 
    create_access_token, 
    create_refresh_token,
    get_token_payload
)
from app.db.session import get_db
from app.models.user import User, RefreshToken, Profile
from app.schemas.user import UserCreate, User as UserSchema
from app.schemas.token import Token, RefreshTokenRequest
from app.api.utils.email import send_verification_email, send_password_reset_email

router = APIRouter()

@router.post("/register", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def register(
    user_in: UserCreate, 
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Register a new user
    """
    # Check if user with this email already exists
    user = db.query(User).filter(User.email == user_in.email).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )
    
    # Create new user
    db_user = User(
        id=str(uuid.uuid4()),
        email=user_in.email,
        hashed_password=get_password_hash(user_in.password),
        full_name=user_in.full_name,
        role="client",  # Default role
        is_active=True,
        is_verified=False,  # Requires email verification
    )
    db.add(db_user)
    
    # Create user profile
    db_profile = Profile(
        id=db_user.id,
    )
    db.add(db_profile)
    
    db.commit()
    db.refresh(db_user)
    
    # Send verification email in background
    background_tasks.add_task(
        send_verification_email,
        email=user_in.email,
        token=create_access_token(
            subject=db_user.id,
            expires_delta=timedelta(hours=24)
        )
    )
    
    return db_user

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = db.query(User).filter(User.email == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user",
        )
    
    # Update last login time
    user.last_login = datetime.utcnow()
    db.commit()
    
    # Create access and refresh tokens
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)
    
    # Store refresh token in database
    expires_at = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    db_refresh_token = RefreshToken(
        id=str(uuid.uuid4()),
        token=refresh_token,
        user_id=user.id,
        expires_at=expires_at
    )
    db.add(db_refresh_token)
    db.commit()
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
    }

@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token_in: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Refresh access token
    """
    try:
        # Validate refresh token
        token_data = get_token_payload(refresh_token_in.refresh_token)
        
        if token_data.type != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if token exists in database and is not revoked
        db_token = db.query(RefreshToken).filter(
            RefreshToken.token == refresh_token_in.refresh_token,
            RefreshToken.is_revoked == False
        ).first()
        
        if not db_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or revoked refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if user exists and is active
        user = db.query(User).filter(User.id == token_data.sub).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create new access token
        access_token = create_access_token(subject=user.id)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token_in.refresh_token,
            "token_type": "bearer",
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Could not validate credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

@router.post("/logout", status_code=status.HTTP_200_OK)
async def logout(
    refresh_token_in: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Logout user by revoking refresh token
    """
    # Find and revoke the refresh token
    db_token = db.query(RefreshToken).filter(
        RefreshToken.token == refresh_token_in.refresh_token
    ).first()
    
    if db_token:
        db_token.is_revoked = True
        db.commit()
    
    return {"detail": "Successfully logged out"}

@router.post("/password-reset", status_code=status.HTTP_200_OK)
async def password_reset(
    email: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Send password reset email
    """
    user = db.query(User).filter(User.email == email).first()
    if user:
        # Generate password reset token
        token = create_access_token(
            subject=user.id,
            expires_delta=timedelta(hours=1)
        )
        
        # Send password reset email in background
        background_tasks.add_task(
            send_password_reset_email,
            email=user.email,
            token=token
        )
    
    # Always return success to prevent email enumeration
    return {"detail": "If your email is registered, you will receive a password reset link"}

@router.post("/activate", status_code=status.HTTP_200_OK)
async def activate_account(
    token: str,
    db: Session = Depends(get_db)
):
    """
    Activate user account with verification token
    """
    try:
        # Validate token
        token_data = get_token_payload(token)
        
        # Find user
        user = db.query(User).filter(User.id == token_data.sub).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )
        
        # Activate user
        user.is_verified = True
        db.commit()
        
        return {"detail": "Account activated successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid or expired token: {str(e)}",
        )
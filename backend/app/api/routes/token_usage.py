from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime

from app.db.session import get_db
from app.models.user import User
from app.schemas.token_usage import TokenUsageUpdate, TokenUsageResponse

router = APIRouter()

@router.post("/update", response_model=TokenUsageResponse)
async def update_token_usage(
    data: TokenUsageUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    Update token usage for a user
    """
    try:
        # Get current month in YYYY-MM format
        current_month = datetime.now().strftime("%Y-%m")
        
        # Call the update_token_usage function
        db.execute(
            "SELECT update_token_usage(:user_id, :tokens)",
            {
                "user_id": data.user_id,
                "tokens": data.tokens_used
            }
        )
        
        # Get the updated usage
        result = db.execute(
            """
            SELECT 
                total_tokens_used, 
                tokens_limit,
                (total_tokens_used * 100.0 / tokens_limit) AS usage_percentage
            FROM user_token_usage 
            WHERE user_id = :user_id AND month = :month
            """,
            {
                "user_id": data.user_id,
                "month": current_month
            }
        ).fetchone()
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Token usage record not found"
            )
        
        # Get notification status
        notification = db.execute(
            """
            SELECT threshold_percentage, sent_at
            FROM token_usage_notifications
            WHERE user_id = :user_id AND month = :month
            ORDER BY threshold_percentage DESC
            LIMIT 1
            """,
            {
                "user_id": data.user_id,
                "month": current_month
            }
        ).fetchone()
        
        db.commit()
        
        # Define warning threshold
        warning_threshold = 90  # ← configurable threshold
        last_threshold = notification.threshold_percentage if notification else 0
        
        return {
            "user_id": data.user_id,
            "month": current_month,
            "total_tokens_used": result.total_tokens_used,
            "tokens_limit": result.tokens_limit,
            "usage_percentage": result.usage_percentage,
            "warning": last_threshold >= warning_threshold,  # ← here is the warning flag
            "last_notification": {
                "threshold_percentage": last_threshold,
                "sent_at": notification.sent_at if notification else None
            } if notification else None
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating token usage: {str(e)}"
        )

@router.get("/user/{user_id}", response_model=TokenUsageResponse)
async def get_user_token_usage(
    user_id: str,
    month: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get token usage for a user
    """
    try:
        # If month is not provided, use current month
        if not month:
            month = datetime.now().strftime("%Y-%m")
        
        # Get the usage
        result = db.execute(
            """
            SELECT 
                total_tokens_used, 
                tokens_limit,
                (total_tokens_used * 100.0 / tokens_limit) AS usage_percentage
            FROM user_token_usage 
            WHERE user_id = :user_id AND month = :month
            """,
            {
                "user_id": user_id,
                "month": month
            }
        ).fetchone()
        
        if not result:
            # No record found, return default values
            # Get user's subscription plan
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Determine token limit based on subscription plan
            tokens_limit = 5000  # Default for free plan
            if user.role == "subscriber_enterprise":
                tokens_limit = 500000
            elif user.role == "subscriber_premium":
                tokens_limit = 100000
            elif user.role == "subscriber_basic":
                tokens_limit = 25000
            
            return {
                "user_id": user_id,
                "month": month,
                "total_tokens_used": 0,
                "tokens_limit": tokens_limit,
                "usage_percentage": 0,
                "warning": False,
                "last_notification": None
            }
        
        # Get notification status
        notification = db.execute(
            """
            SELECT threshold_percentage, sent_at
            FROM token_usage_notifications
            WHERE user_id = :user_id AND month = :month
            ORDER BY threshold_percentage DESC
            LIMIT 1
            """,
            {
                "user_id": user_id,
                "month": month
            }
        ).fetchone()
        
        # Define warning threshold
        warning_threshold = 90  # ← configurable threshold
        last_threshold = notification.threshold_percentage if notification else 0
        
        return {
            "user_id": user_id,
            "month": month,
            "total_tokens_used": result.total_tokens_used,
            "tokens_limit": result.tokens_limit,
            "usage_percentage": result.usage_percentage,
            "warning": last_threshold >= warning_threshold,  # ← here is the warning flag
            "last_notification": {
                "threshold_percentage": last_threshold,
                "sent_at": notification.sent_at if notification else None
            } if notification else None
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting token usage: {str(e)}"
        )
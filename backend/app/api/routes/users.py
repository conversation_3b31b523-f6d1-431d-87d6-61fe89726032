from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.security import get_current_active_user, validate_admin_access
from app.db.session import get_db
from app.models.user import User, Profile
from app.schemas.user import User as UserSchema, UserUpdate, UserUpdateMe, ProfileUpdate, UserWithProfile

router = APIRouter()

@router.get("/me", response_model=UserWithProfile)
async def read_users_me(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user information
    """
    # Get user profile
    profile = db.query(Profile).filter(Profile.id == current_user.id).first()
    
    # Return user with profile
    return {
        **current_user.__dict__,
        "profile": profile.__dict__ if profile else None
    }

@router.put("/me", response_model=UserWithProfile)
async def update_user_me(
    user_in: UserUpdateMe,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user information
    """
    # Update user
    if user_in.full_name is not None:
        current_user.full_name = user_in.full_name
    
    db.commit()
    db.refresh(current_user)
    
    # Get user profile
    profile = db.query(Profile).filter(Profile.id == current_user.id).first()
    
    # Return updated user with profile
    return {
        **current_user.__dict__,
        "profile": profile.__dict__ if profile else None
    }

@router.put("/me/profile", response_model=UserWithProfile)
async def update_user_profile(
    profile_in: ProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile
    """
    # Get user profile
    profile = db.query(Profile).filter(Profile.id == current_user.id).first()
    
    if not profile:
        # Create profile if it doesn't exist
        profile = Profile(id=current_user.id)
        db.add(profile)
    
    # Update profile fields
    for field, value in profile_in.dict(exclude_unset=True).items():
        setattr(profile, field, value)
    
    db.commit()
    db.refresh(profile)
    
    # Return user with updated profile
    return {
        **current_user.__dict__,
        "profile": profile.__dict__
    }

@router.get("", response_model=List[UserSchema])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    role: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Retrieve users (admin only)
    """
    query = db.query(User)
    
    # Apply filters
    if role:
        query = query.filter(User.role == role)
    
    if search:
        query = query.filter(
            (User.email.ilike(f"%{search}%")) | 
            (User.full_name.ilike(f"%{search}%"))
        )
    
    users = query.offset(skip).limit(limit).all()
    return users

@router.get("/{user_id}", response_model=UserWithProfile)
async def read_user(
    user_id: str,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Get a specific user by id (admin only)
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Get user profile
    profile = db.query(Profile).filter(Profile.id == user.id).first()
    
    # Return user with profile
    return {
        **user.__dict__,
        "profile": profile.__dict__ if profile else None
    }

@router.patch("/{user_id}/role", response_model=UserSchema)
async def update_user_role(
    user_id: str,
    role: str,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Update user role (admin only)
    """
    # Validate role
    valid_roles = ["admin", "attorney", "client"]
    if role not in valid_roles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid role. Must be one of: {', '.join(valid_roles)}",
        )
    
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Update role
    user.role = role
    db.commit()
    db.refresh(user)
    
    return user

@router.patch("/{user_id}/status", response_model=UserSchema)
async def update_user_status(
    user_id: str,
    is_active: bool,
    current_user: User = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Activate or suspend user (admin only)
    """
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Prevent self-deactivation
    if user.id == current_user.id and not is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own account",
        )
    
    # Update status
    user.is_active = is_active
    db.commit()
    db.refresh(user)
    
    return user
from fastapi import WebSocket
from typing import Dict, List, Any
import json

class ConnectionManager:
    def __init__(self):
        # Map of session_id -> list of connected websockets
        self.active_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """
        Connect a websocket to a chat session
        """
        await websocket.accept()
        
        if session_id not in self.active_connections:
            self.active_connections[session_id] = []
        
        self.active_connections[session_id].append(websocket)
    
    def disconnect(self, websocket: WebSocket, session_id: str):
        """
        Disconnect a websocket from a chat session
        """
        if session_id in self.active_connections:
            if websocket in self.active_connections[session_id]:
                self.active_connections[session_id].remove(websocket)
            
            # Clean up empty lists
            if not self.active_connections[session_id]:
                del self.active_connections[session_id]
    
    async def send_personal_message(self, message: Any, websocket: WebSocket):
        """
        Send a message to a specific websocket
        """
        if isinstance(message, dict):
            message = json.dumps(message)
        await websocket.send_text(message)
    
    async def broadcast(self, session_id: str, message: Any):
        """
        Broadcast a message to all connected websockets for a session
        """
        if session_id in self.active_connections:
            if isinstance(message, dict):
                message = json.dumps(message)
            
            for connection in self.active_connections[session_id]:
                await connection.send_text(message)
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app.core.config import settings

logger = logging.getLogger(__name__)

def send_email(email_to: str, subject: str, html_content: str):
    """
    Send an email
    """
    # Check if SMTP settings are configured
    if not all([settings.SMTP_HOST, settings.SMTP_PORT, settings.SMTP_USER, settings.SMTP_PASSWORD]):
        logger.warning(f"<PERSON><PERSON> not configured, would send email to {email_to}: {subject}")
        return
    
    message = MIMEMultipart()
    message["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL}>"
    message["To"] = email_to
    message["Subject"] = subject
    
    message.attach(MIMEText(html_content, "html"))
    
    try:
        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
            if settings.SMTP_TLS:
                server.starttls()
            if settings.SMTP_USER and settings.SMTP_PASSWORD:
                server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            server.sendmail(
                settings.EMAILS_FROM_EMAIL, email_to, message.as_string()
            )
        logger.info(f"Email sent to {email_to}")
    except Exception as e:
        logger.error(f"Failed to send email to {email_to}: {e}")

def send_invoice_email(email: str, invoice_number: str, amount: float, plan: str):
    """
    Send invoice email
    """
    subject = f"فاتورة {invoice_number} لاشتراكك"
    html_content = f"""
    <html dir="rtl">
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <img src="https://yourdomain.com/logo.png" alt="شُريح" style="max-width: 150px;">
            </div>
            
            <h1 style="color: #2635ED; text-align: center; margin-bottom: 30px;">فاتورة {invoice_number}</h1>
            
            <div style="background-color: #f9f9f9; border-radius: 10px; padding: 20px; margin-bottom: 30px;">
                <p style="margin-bottom: 10px;"><strong>شكراً لاشتراكك في منصة شُريح للذكاء الاصطناعي القانوني.</strong></p>
                <p style="margin-bottom: 10px;"><strong>الباقة:</strong> {plan.capitalize()}</p>
                <p style="margin-bottom: 10px;"><strong>المبلغ:</strong> {amount} ريال</p>
                <p style="margin-bottom: 10px;"><strong>ضريبة القيمة المضافة (15%):</strong> {amount * 0.15} ريال</p>
                <p style="margin-bottom: 10px;"><strong>الإجمالي:</strong> {amount * 1.15} ريال</p>
                <p style="margin-bottom: 10px;"><strong>تاريخ الفاتورة:</strong> {datetime.now().strftime('%Y-%m-%d')}</p>
            </div>
            
            <p style="text-align: center; margin-bottom: 20px;">يمكنك عرض وتنزيل الفاتورة من لوحة التحكم الخاصة بك.</p>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="https://yourdomain.com/invoices" style="background-color: #2635ED; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">عرض الفاتورة</a>
            </div>
            
            <div style="margin-top: 50px; text-align: center; color: #777; font-size: 12px;">
                <p>© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.</p>
                <p>إذا كانت لديك أي استفسارات، يرجى التواصل معنا على <a href="mailto:<EMAIL>" style="color: #2635ED;"><EMAIL></a></p>
            </div>
        </body>
    </html>
    """
    
    send_email(email, subject, html_content)
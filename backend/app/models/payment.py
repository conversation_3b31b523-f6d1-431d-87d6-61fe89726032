from sqlalchemy import Column, String, DateTime, Float, Enum, Foreign<PERSON>ey, <PERSON>olean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.db.session import Base

class Subscription(Base):
    __tablename__ = "subscriptions"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    plan = Column(Enum("free", "basic", "premium", "enterprise", name="subscription_plan"), nullable=False)
    status = Column(Enum("active", "canceled", "expired", "past_due", name="subscription_status"), nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    auto_renew = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="subscriptions")
    payments = relationship("Payment", back_populates="subscription", cascade="all, delete-orphan")

class Payment(Base):
    __tablename__ = "payments"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    subscription_id = Column(String, ForeignKey("subscriptions.id", ondelete="CASCADE"), nullable=True)
    amount = Column(Float, nullable=False)
    currency = Column(String, default="SAR", nullable=False)
    status = Column(Enum("pending", "completed", "failed", "refunded", name="payment_status"), nullable=False)
    payment_method = Column(String, nullable=False)
    payment_id = Column(String, nullable=True)  # External payment ID (e.g., from Moyasar)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="payments")
    subscription = relationship("Subscription", back_populates="payments")
    invoice = relationship("Invoice", back_populates="payment", uselist=False, cascade="all, delete-orphan")

class Invoice(Base):
    __tablename__ = "invoices"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    payment_id = Column(String, ForeignKey("payments.id", ondelete="CASCADE"), nullable=False)
    invoice_number = Column(String, nullable=False, unique=True)
    invoice_date = Column(DateTime(timezone=True), nullable=False)
    due_date = Column(DateTime(timezone=True), nullable=False)
    total_amount = Column(Float, nullable=False)
    tax_amount = Column(Float, nullable=False)
    status = Column(Enum("draft", "issued", "paid", "void", name="invoice_status"), nullable=False)
    notes = Column(Text, nullable=True)
    wafeq_id = Column(String, nullable=True)  # External invoice ID (e.g., from Wafeq)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    payment = relationship("Payment", back_populates="invoice")
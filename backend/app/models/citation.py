from sqlalchemy import Column, String, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.db.session import Base

# Association table for citation tags
citation_tags = Table(
    "citation_tags",
    Base.metadata,
    Column("citation_id", String, ForeignKey("citations.id", ondelete="CASCADE"), primary_key=True),
    Column("tag_id", String, ForeignKey("tags.id", ondelete="CASCADE"), primary_key=True)
)

class Citation(Base):
    __tablename__ = "citations"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    source = Column(String, nullable=False)
    source_url = Column(String, nullable=True)
    article_number = Column(String, nullable=True)
    section = Column(String, nullable=True)
    category = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(String, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    
    # Relationships
    message_citations = relationship("MessageCitation", back_populates="citation", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary=citation_tags, back_populates="citations")
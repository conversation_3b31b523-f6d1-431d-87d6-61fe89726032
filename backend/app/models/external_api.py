from sqlalchemy import Colum<PERSON>, String, <PERSON>olean, DateTime, Enum, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from app.db.session import Base

class ExternalAPI(Base):
    __tablename__ = "external_apis"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    endpoint = Column(String, nullable=False)
    type = Column(Enum("llm", "speech", "vision", "analysis", "translation", name="api_type"), nullable=False)
    config = Column(JSON, default={})
    api_key = Column(String, nullable=True)
    plan_level = Column(Enum("free", "basic", "premium", "enterprise", name="plan_level"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, ForeignKey, Text, Enum, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.db.session import Base

# Association table for chat session tags
chat_session_tags = Table(
    "chat_session_tags",
    Base.metadata,
    Column("chat_session_id", String, ForeignKey("chat_sessions.id", ondelete="CASCADE"), primary_key=True),
    Column("tag_id", String, ForeignKey("tags.id", ondelete="CASCADE"), primary_key=True)
)

class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False)
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    is_archived = Column(Boolean, default=False)
    is_flagged = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="chat_session", cascade="all, delete-orphan")
    attachments = relationship("ChatAttachment", back_populates="chat_session", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary=chat_session_tags, back_populates="chat_sessions")

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    chat_session_id = Column(String, ForeignKey("chat_sessions.id", ondelete="CASCADE"), nullable=False)
    sender_type = Column(Enum("user", "system", name="sender_type"), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_flagged = Column(Boolean, default=False)

    # Relationships
    chat_session = relationship("ChatSession", back_populates="messages")
    citations = relationship("MessageCitation", back_populates="message", cascade="all, delete-orphan")

class ChatAttachment(Base):
    __tablename__ = "chat_attachments"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    chat_session_id = Column(String, ForeignKey("chat_sessions.id", ondelete="CASCADE"), nullable=False)
    file_name = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_type = Column(String, nullable=False)
    file_size = Column(String, nullable=False)  # in bytes
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    chat_session = relationship("ChatSession", back_populates="attachments")

class Tag(Base):
    __tablename__ = "tags"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    chat_sessions = relationship("ChatSession", secondary=chat_session_tags, back_populates="tags")

class MessageCitation(Base):
    __tablename__ = "message_citations"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    message_id = Column(String, ForeignKey("chat_messages.id", ondelete="CASCADE"), nullable=False)
    citation_id = Column(String, ForeignKey("citations.id", ondelete="CASCADE"), nullable=False)
    start_index = Column(String, nullable=True)  # Position in the message where citation starts
    end_index = Column(String, nullable=True)    # Position in the message where citation ends
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    message = relationship("ChatMessage", back_populates="citations")
    citation = relationship("Citation", back_populates="message_citations")
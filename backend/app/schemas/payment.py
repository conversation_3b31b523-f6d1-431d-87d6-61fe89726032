from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class SubscriptionPlan(str, Enum):
    free = "free"
    basic = "basic"
    premium = "premium"
    enterprise = "enterprise"

class SubscriptionStatus(str, Enum):
    active = "active"
    canceled = "canceled"
    expired = "expired"
    past_due = "past_due"

class PaymentStatus(str, Enum):
    pending = "pending"
    paid = "paid"
    failed = "failed"
    refunded = "refunded"

class InvoiceStatus(str, Enum):
    draft = "draft"
    issued = "issued"
    paid = "paid"
    void = "void"

class PaymentMethod(str, Enum):
    creditcard = "creditcard"
    applepay = "applepay"
    stcpay = "stcpay"
    mada = "mada"

class PaymentInitiate(BaseModel):
    plan: SubscriptionPlan
    payment_method: PaymentMethod = "creditcard"
    callback_url: Optional[str] = None

class PaymentResponse(BaseModel):
    payment_id: str
    moyasar_id: str
    amount: float
    currency: str
    transaction_url: str
    callback_url: str

class PaymentStatusResponse(BaseModel):
    payment_id: str
    moyasar_id: str
    status: PaymentStatus
    amount: float
    currency: str
    created_at: str

class PaymentWebhook(BaseModel):
    id: str
    status: str
    amount: float
    currency: str
    fee: float
    refunded: float
    refunded_at: Optional[datetime] = None
    captured: float
    captured_at: Optional[datetime] = None
    voided_at: Optional[datetime] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class SubscriptionBase(BaseModel):
    plan: SubscriptionPlan
    auto_renew: bool = True

class SubscriptionCreate(SubscriptionBase):
    pass

class SubscriptionUpdate(BaseModel):
    plan: Optional[SubscriptionPlan] = None
    status: Optional[SubscriptionStatus] = None
    auto_renew: Optional[bool] = None
    end_date: Optional[datetime] = None

class SubscriptionInDBBase(SubscriptionBase):
    id: str
    user_id: str
    status: SubscriptionStatus
    start_date: datetime
    end_date: datetime
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class Subscription(SubscriptionInDBBase):
    pass

class PaymentBase(BaseModel):
    amount: float = Field(..., gt=0)
    currency: str = "SAR"
    payment_method: str

class PaymentCreate(PaymentBase):
    subscription_id: Optional[str] = None

class PaymentInDBBase(PaymentBase):
    id: str
    user_id: str
    subscription_id: Optional[str] = None
    status: PaymentStatus
    payment_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class Payment(PaymentInDBBase):
    pass

class PaymentHistoryResponse(BaseModel):
    payments: List[Payment]
    total: int

class InvoiceBase(BaseModel):
    invoice_number: str
    invoice_date: datetime
    due_date: datetime
    total_amount: float = Field(..., gt=0)
    tax_amount: float = Field(..., ge=0)
    notes: Optional[str] = None

class InvoiceCreate(InvoiceBase):
    pass

class InvoiceInDBBase(InvoiceBase):
    id: str
    payment_id: str
    status: InvoiceStatus
    wafeq_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class Invoice(InvoiceInDBBase):
    pass
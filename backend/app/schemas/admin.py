from pydantic import BaseModel
from typing import Dict, Optional, List
from datetime import datetime

class UsageReport(BaseModel):
    start_date: datetime
    end_date: datetime
    total_users: int
    active_users: int
    new_users: int
    total_sessions: int
    new_sessions: int
    total_messages: int
    new_messages: int
    avg_messages_per_session: float

class PerformanceReport(BaseModel):
    start_date: datetime
    end_date: datetime
    avg_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    error_rate: float
    uptime_percentage: float
    api_calls_total: int
    api_calls_per_day: float

class FinancialReport(BaseModel):
    start_date: datetime
    end_date: datetime
    total_revenue: float
    subscription_counts: Dict[str, int]
    new_subscriptions: int
    canceled_subscriptions: int
    monthly_recurring_revenue: float
    average_revenue_per_user: float

class ContentReportRead(BaseModel):
    id: str
    reporter_id: Optional[str]
    reporter_name: Optional[str]
    resource_type: str
    resource_id: str
    reason: str
    description: Optional[str]
    status: str
    reviewed_by: Optional[str]
    reviewer_name: Optional[str]
    reviewed_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        orm_mode = True

class AuditLogRead(BaseModel):
    id: str
    user_id: Optional[str]
    user_name: Optional[str]
    action: str
    resource_type: str
    resource_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    details: Optional[Dict]
    created_at: datetime
    
    class Config:
        orm_mode = True
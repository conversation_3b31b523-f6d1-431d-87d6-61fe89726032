from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class TokenUsageUpdate(BaseModel):
    user_id: str
    tokens_used: int = Field(..., gt=0)

class NotificationInfo(BaseModel):
    threshold_percentage: int
    sent_at: datetime

class TokenUsageResponse(BaseModel):
    user_id: str
    month: str
    total_tokens_used: int
    tokens_limit: int
    usage_percentage: float
    warning: Optional[bool] = False
    last_notification: Optional[NotificationInfo] = None
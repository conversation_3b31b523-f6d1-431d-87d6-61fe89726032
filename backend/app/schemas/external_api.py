from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any
from datetime import datetime
import uuid

class ExternalAPIBase(BaseModel):
    name: str
    endpoint: str
    type: str
    config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    api_key: Optional[str] = None
    plan_level: str
    is_active: Optional[bool] = True

    @validator('type')
    def validate_type(cls, v):
        valid_types = ["llm", "speech", "vision", "analysis", "translation"]
        if v not in valid_types:
            raise ValueError(f"Type must be one of: {', '.join(valid_types)}")
        return v
    
    @validator('plan_level')
    def validate_plan_level(cls, v):
        valid_plans = ["free", "basic", "premium", "enterprise"]
        if v not in valid_plans:
            raise ValueError(f"Plan level must be one of: {', '.join(valid_plans)}")
        return v

class ExternalAPICreate(ExternalAPIBase):
    pass

class ExternalAPIUpdate(BaseModel):
    name: Optional[str] = None
    endpoint: Optional[str] = None
    type: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    api_key: Optional[str] = None
    plan_level: Optional[str] = None
    is_active: Optional[bool] = None
    
    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            valid_types = ["llm", "speech", "vision", "analysis", "translation"]
            if v not in valid_types:
                raise ValueError(f"Type must be one of: {', '.join(valid_types)}")
        return v
    
    @validator('plan_level')
    def validate_plan_level(cls, v):
        if v is not None:
            valid_plans = ["free", "basic", "premium", "enterprise"]
            if v not in valid_plans:
                raise ValueError(f"Plan level must be one of: {', '.join(valid_plans)}")
        return v

class ExternalAPIRead(ExternalAPIBase):
    id: uuid.UUID
    created_at: datetime
    
    class Config:
        orm_mode = True
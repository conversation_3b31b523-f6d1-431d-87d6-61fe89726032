from pydantic import BaseModel, Field, validator
from typing import Optional

class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1)
    session_id: str
    user_plan: str
    
    @validator('user_plan')
    def validate_user_plan(cls, v):
        valid_plans = ["free", "basic", "premium", "enterprise"]
        if v not in valid_plans:
            raise ValueError(f"User plan must be one of: {', '.join(valid_plans)}")
        return v

class ChatResponse(BaseModel):
    response: str
    model: str
    tokens_used: int
    session_id: str
from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List
from datetime import datetime

class CitationBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    source: str = Field(..., min_length=1, max_length=200)
    source_url: Optional[HttpUrl] = None
    article_number: Optional[str] = None
    section: Optional[str] = None
    category: Optional[str] = None

class CitationCreate(CitationBase):
    tags: Optional[List[str]] = None

class CitationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    source: Optional[str] = Field(None, min_length=1, max_length=200)
    source_url: Optional[HttpUrl] = None
    article_number: Optional[str] = None
    section: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None

class CitationInDBBase(CitationBase):
    id: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    
    class Config:
        orm_mode = True

class Citation(CitationInDBBase):
    tags: List[str] = []

class CitationImport(BaseModel):
    citations: List[CitationCreate]
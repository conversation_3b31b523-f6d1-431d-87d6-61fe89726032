# Legal Chat Platform API

A FastAPI backend for a legal chat platform with authentication, real-time chat, legal citation management, and payment integration.

## Features

- User authentication and authorization with JWT tokens
- Role-based access control (<PERSON><PERSON>, <PERSON>, Client)
- Real-time chat functionality with WebSockets
- Legal citation management and search
- Administrative dashboard
- Payment integration with Moyasar
- E-invoicing with Wafeq
- Comprehensive reporting and analytics

## Requirements

- Python 3.10+
- PostgreSQL
- Docker (optional)

## Installation

### Local Development

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/legal-chat-api.git
   cd legal-chat-api
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file with your configuration.

5. Run database migrations:
   ```bash
   alembic upgrade head
   ```

6. Start the development server:
   ```bash
   uvicorn app.main:app --reload
   ```

### Docker Deployment

1. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

2. The API will be available at http://localhost:8000

## API Documentation

Once the server is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Project Structure

```
backend/
├── alembic/                  # Database migrations
├── app/
│   ├── api/
│   │   ├── routes/           # API endpoints
│   │   └── utils/            # API utilities
│   ├── core/                 # Core functionality
│   ├── db/                   # Database setup
│   ├── models/               # SQLAlchemy models
│   ├── schemas/              # Pydantic schemas
│   ├── static/               # Static files
│   ├── uploads/              # Uploaded files
│   └── main.py               # FastAPI application
├── tests/                    # Unit tests
├── .env.example              # Environment variables example
├── alembic.ini               # Alembic configuration
├── docker-compose.yml        # Docker Compose configuration
├── Dockerfile                # Docker configuration
├── README.md                 # Project documentation
└── requirements.txt          # Python dependencies
```

## Testing

Run the tests with pytest:

```bash
pytest
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
# 🔄 باقات الاشتراك ونظام استخدام التوكينز

## 📋 جدول المحتويات

- [نظرة عامة](#-نظرة-عامة)
- [هيكل باقات الاشتراك](#-هيكل-باقات-الاشتراك)
- [نظام استخدام التوكينز](#-نظام-استخدام-التوكينز)
- [آلية التنبيهات](#-آلية-التنبيهات)
- [التكامل مع قاعدة البيانات](#-التكامل-مع-قاعدة-البيانات)
- [واجهة المستخدم](#-واجهة-المستخدم)
- [الأدوار والصلاحيات](#-الأدوار-والصلاحيات)

---

## 🌟 نظرة عامة

نظام باقات الاشتراك وإدارة التوكينز في مشروع شُريح يوفر آلية متكاملة لتتبع وإدارة استخدام المستخدمين للنماذج اللغوية، مع تقديم تنبيهات ذكية عند الوصول لحدود الاستخدام، وربط ذلك بمستويات الاشتراك المختلفة.

---

## 📦 هيكل باقات الاشتراك

### مستويات الاشتراك

يتضمن النظام أربعة مستويات اشتراك رئيسية:

| **مستوى الاشتراك** | **السعر الشهري** | **السعر السنوي** | **الفئة المستهدفة** |
| :-- | :-- | :-- | :-- |
| المجاني | 0 ريال | 0 ريال | المستخدمون العاديون |
| الأساسي | 199 ريال | 1990 ريال | الطلاب والمطورون |
| المتقدم | 499 ريال | 4990 ريال | الشركات الصغيرة |
| المؤسسي | 999 ريال | 9990 ريال | المؤسسات الكبيرة |

### الميزات والحدود حسب الباقة

| **الميزة** | **المجاني** | **الأساسي** | **المتقدم** | **المؤسسي** |
| :-- | :-- | :-- | :-- | :-- |
| حد التوكينز الشهري | 5,000 | 25,000 | 100,000 | 500,000 |
| الحد الأقصى للمحادثات | 100 | 1,000 | 5,000 | غير محدود |
| المحادثة الصوتية | ❌ | ✅ | ✅ | ✅ |
| رفع الملفات | ❌ | ✅ (10MB) | ✅ (50MB) | ✅ (500MB) |
| النماذج المتاحة | أساسية فقط | أساسية + متوسطة | جميع النماذج | نماذج مخصصة |
| الدعم الفني | دعم المجتمع | دعم البريد الإلكتروني | دعم أولوية | دعم مخصص 24/7 |

### الأدوار المرتبطة بالباقات

كل مستوى اشتراك مرتبط بدور محدد في النظام:

- **المجاني**: `user`
- **الأساسي**: `subscriber_basic`
- **المتقدم**: `subscriber_premium`
- **المؤسسي**: `subscriber_enterprise`

بالإضافة إلى أدوار الإدارة:
- **المشرف**: `moderator`
- **المسؤول**: `administrator`

---

## 🔢 نظام استخدام التوكينز

### آلية حساب التوكينز

يتم حساب استخدام التوكينز بناءً على:

1. **طول النص المدخل**: عدد الكلمات/الأحرف في استفسار المستخدم
2. **طول الاستجابة**: عدد الكلمات/الأحرف في رد النموذج
3. **نوع النموذج المستخدم**: بعض النماذج تستهلك توكينز أكثر من غيرها

### تتبع الاستخدام

يتم تتبع استخدام التوكينز من خلال:

```typescript
// تحديث استخدام التوكينز بعد كل محادثة
async function updateTokenUsage(userId: string, tokensUsed: number) {
  // إرسال طلب لتحديث استخدام التوكينز
  const response = await api.post('/api/token-usage/update', {
    user_id: userId,
    tokens_used: tokensUsed
  });
  
  return response.data;
}
```

### إعادة تعيين الحدود

- يتم إعادة تعيين حدود التوكينز تلقائيًا في بداية كل شهر
- يتم الاحتفاظ بسجل تاريخي للاستخدام للأغراض التحليلية

---

## 🔔 آلية التنبيهات

### عتبات التنبيه

يتم إرسال تنبيهات للمستخدم عند الوصول إلى نسب محددة من حد التوكينز:

- **50%**: تنبيه منخفض الأولوية
- **75%**: تنبيه متوسط الأولوية
- **90%**: تنبيه عالي الأولوية
- **100%**: تنبيه حرج (مع تقييد الاستخدام)

### مكونات التنبيه

```jsx
<TokenUsageAlert 
  currentUsage={9000}
  maxAllowed={10000}
  resetDate="2024-07-31"
  onClose={() => setShowTokenAlert(false)}
/>
```

يتضمن التنبيه:

- **عنوان التنبيه**: "تحذير استخدام"
- **رسالة**: "لقد وصلت إلى 90% من حد التوكينز الشهري المخصص لك. عند الوصول إلى 100%، سيتم تقييد استخدامك للنظام."
- **تفاصيل الاستخدام**: الاستخدام الحالي، الحد الأقصى، تاريخ إعادة التعيين
- **شريط تقدم**: يوضح نسبة الاستخدام بصريًا
- **أزرار إجراءات**: "ترقية الاشتراك" و "عرض الاستخدام"

### منطق عرض التنبيهات

```jsx
// التحقق من عرض التنبيه بناءً على دور المستخدم
useEffect(() => {
  // عرض التنبيه فقط للمستخدمين العاديين، وليس للمسؤولين
  if (profile) {
    const isAdmin = profile.role === 'administrator';
    const isHighUsage = usagePercentage >= 90;
    
    // عرض التنبيه إذا:
    // 1. المستخدم ليس مسؤولاً
    // 2. الاستخدام مرتفع (>=90%)
    setShouldShow(!isAdmin && isHighUsage);
  }
}, [profile, usagePercentage]);
```

---

## 🗄️ التكامل مع قاعدة البيانات

### هيكل الجداول

#### جدول `user_token_usage`

```sql
CREATE TABLE user_token_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  total_tokens_used BIGINT NOT NULL DEFAULT 0,
  tokens_limit BIGINT NOT NULL DEFAULT 5000,
  last_updated TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each user has only one record per month
  UNIQUE(user_id, month)
);
```

#### جدول `token_usage_notifications`

```sql
CREATE TABLE token_usage_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  month VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  threshold_percentage INT NOT NULL, -- e.g., 50, 75, 90, 100
  sent_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each threshold is only notified once per user per month
  UNIQUE(user_id, month, threshold_percentage)
);
```

### وظيفة تحديث الاستخدام

```sql
CREATE OR REPLACE FUNCTION update_token_usage(
  p_user_id UUID,
  p_tokens INT
)
RETURNS VOID AS $$
DECLARE
  v_month VARCHAR(7);
  v_tokens_limit BIGINT;
  v_current_usage BIGINT;
  v_new_usage BIGINT;
  v_percentage INT;
  v_subscription_plan VARCHAR;
BEGIN
  -- Get current month in YYYY-MM format
  v_month := to_char(now(), 'YYYY-MM');
  
  -- Get user's subscription plan
  SELECT role INTO v_subscription_plan FROM profiles WHERE id = p_user_id;
  
  -- Determine token limit based on subscription plan
  CASE v_subscription_plan
    WHEN 'subscriber_enterprise' THEN v_tokens_limit := 500000;
    WHEN 'subscriber_premium' THEN v_tokens_limit := 100000;
    WHEN 'subscriber_basic' THEN v_tokens_limit := 25000;
    ELSE v_tokens_limit := 5000; -- Free plan
  END CASE;
  
  -- Insert or update the usage record
  INSERT INTO user_token_usage (user_id, month, total_tokens_used, tokens_limit, last_updated)
  VALUES (p_user_id, v_month, p_tokens, v_tokens_limit, now())
  ON CONFLICT (user_id, month) 
  DO UPDATE SET 
    total_tokens_used = user_token_usage.total_tokens_used + p_tokens,
    last_updated = now();
    
  -- Get the updated usage
  SELECT total_tokens_used INTO v_current_usage 
  FROM user_token_usage 
  WHERE user_id = p_user_id AND month = v_month;
  
  -- Calculate percentage
  v_percentage := (v_current_usage * 100) / v_tokens_limit;
  
  -- Check if we need to send notifications at various thresholds
  -- Only send if we've crossed the threshold with this update
  IF v_percentage >= 90 AND v_current_usage - p_tokens < (v_tokens_limit * 0.9) THEN
    -- Send 90% notification
    INSERT INTO token_usage_notifications (user_id, month, threshold_percentage)
    VALUES (p_user_id, v_month, 90)
    ON CONFLICT (user_id, month, threshold_percentage) DO NOTHING;
    
    -- Also insert into the general notifications table if it exists
    BEGIN
      INSERT INTO notifications (user_id, title, message, type, details, created_at)
      VALUES (
        p_user_id, 
        'تحذير استخدام', 
        'لقد وصلت إلى 90% من حد التوكينز الشهري المخصص لك',
        'warning',
        jsonb_build_object(
          'currentUsage', v_current_usage,
          'maxAllowed', v_tokens_limit,
          'resetDate', (date_trunc('month', now()) + interval '1 month - 1 day')::date
        ),
        now()
      );
    EXCEPTION WHEN undefined_table THEN
      -- Notifications table might not exist, ignore
    END;
  END IF;
  
  -- Similar checks for 75% and 50% thresholds...
  
  -- If usage exceeds 100%, implement rate limiting
  IF v_percentage >= 100 THEN
    -- Log that the user has exceeded their limit
    NULL;
  END IF;
END;
$$ LANGUAGE plpgsql;
```

### سياسات أمان مستوى الصف (RLS)

```sql
-- سياسات جدول user_token_usage
CREATE POLICY "Users can view their own token usage"
  ON user_token_usage
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all token usage"
  ON user_token_usage
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can update token usage"
  ON user_token_usage
  FOR ALL
  USING (true);

-- سياسات جدول token_usage_notifications
CREATE POLICY "Users can view their own notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all notifications"
  ON token_usage_notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "System can insert notifications"
  ON token_usage_notifications
  FOR INSERT
  WITH CHECK (true);
```

---

## 🖥️ واجهة المستخدم

### مكون تنبيه استخدام التوكينز

```jsx
export const TokenUsageAlert: React.FC<TokenUsageAlertProps> = ({
  currentUsage,
  maxAllowed,
  resetDate,
  onClose
}) => {
  const { language } = useTranslation();
  const navigate = useNavigate();
  const { profile } = useAuth();
  const [shouldShow, setShouldShow] = useState(false);
  
  // حساب النسبة المئوية
  const usagePercentage = Math.round((currentUsage / maxAllowed) * 100);
  
  // تنسيق الأرقام بفواصل
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US').format(num);
  };
  
  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // التحقق من عرض التنبيه بناءً على دور المستخدم
  useEffect(() => {
    // عرض التنبيه فقط للمستخدمين العاديين، وليس للمسؤولين
    if (profile) {
      const isAdmin = profile.role === 'administrator';
      const isHighUsage = usagePercentage >= 90;
      
      // عرض التنبيه إذا:
      // 1. المستخدم ليس مسؤولاً
      // 2. الاستخدام مرتفع (>=90%)
      setShouldShow(!isAdmin && isHighUsage);
    }
  }, [profile, usagePercentage]);

  // إذا لم يجب عرض التنبيه، أرجع null
  if (!shouldShow) {
    return null;
  }
  
  return (
    <Alert
      type="warning"
      showIcon
      icon={<WarningOutlined />}
      message={language === 'ar' ? "تحذير استخدام" : "Usage Warning"}
      description={
        <div className="space-y-3">
          <p>
            {language === 'ar' 
              ? `لقد وصلت إلى ${usagePercentage}% من حد التوكينز الشهري المخصص لك. عند الوصول إلى 100%، سيتم تقييد استخدامك للنظام.`
              : `You have reached ${usagePercentage}% of your monthly token limit. When you reach 100%, your system usage will be restricted.`
            }
          </p>
          
          <div className="mt-2">
            <Progress 
              percent={usagePercentage} 
              status={usagePercentage > 95 ? "exception" : "active"}
              strokeColor={usagePercentage > 95 ? "#ff4d4f" : "#faad14"}
            />
            <div className="flex justify-between text-sm mt-1">
              <Text>{formatNumber(currentUsage)} {language === 'ar' ? "توكن" : "tokens"}</Text>
              <Text>{formatNumber(maxAllowed)} {language === 'ar' ? "توكن" : "tokens"}</Text>
            </div>
          </div>
          
          <div className="text-sm text-gray-500 mt-2">
            {language === 'ar' 
              ? `سيتم إعادة تعيين الحد في ${formatDate(resetDate)}`
              : `Limit will reset on ${formatDate(resetDate)}`
            }
          </div>
          
          <Space className="mt-3">
            <Button 
              type="primary" 
              icon={<ArrowUpOutlined />}
              onClick={() => navigate('/subscription-packages')}
            >
              {language === 'ar' ? "ترقية الاشتراك" : "Upgrade Subscription"}
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => navigate('/analytics')}
            >
              {language === 'ar' ? "عرض الاستخدام" : "View Usage"}
            </Button>
          </Space>
        </div>
      }
      closable
      onClose={onClose}
      className="mb-4"
    />
  );
};
```

### استخدام المكون في صفحات المشروع

```jsx
// في صفحة إدارة الاشتراكات
{showTokenAlert && highUsageSubscription && (
  <TokenUsageAlert 
    currentUsage={highUsageSubscription.tokensUsed}
    maxAllowed={highUsageSubscription.tokensLimit}
    resetDate="2024-07-31"
    onClose={() => setShowTokenAlert(false)}
  />
)}

// في صفحة باقات الاشتراك
{showTokenAlert && (
  <TokenUsageAlert 
    currentUsage={9000}
    maxAllowed={10000}
    resetDate="2024-07-31"
    onClose={() => setShowTokenAlert(false)}
  />
)}
```

---

## 👥 الأدوار والصلاحيات

### حدود الاستخدام حسب الدور

```typescript
// حدود الاستخدام لكل دور
const roleLimits: Record<UserRole, SubscriptionLimits> = {
  // المستخدم العادي
  user: {
    maxDailyQueries: 10,
    maxFileUploads: 0,
    maxFileSize: 0,
    maxTokensPerChat: 1000,
    voiceEnabled: false,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  },
  // المشترك الأساسي
  subscriber_basic: {
    maxDailyQueries: 100,
    maxFileUploads: 5,
    maxFileSize: 10,
    maxTokensPerChat: 2000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  },
  // المشترك المتقدم
  subscriber_premium: {
    maxDailyQueries: 500,
    maxFileUploads: 20,
    maxFileSize: 50,
    maxTokensPerChat: 4000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  },
  // المشترك المؤسسي
  subscriber_enterprise: {
    maxDailyQueries: -1, // غير محدود
    maxFileUploads: 100,
    maxFileSize: 500,
    maxTokensPerChat: 8000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  },
  // المشرف
  moderator: {
    maxDailyQueries: -1, // غير محدود
    maxFileUploads: 50,
    maxFileSize: 100,
    maxTokensPerChat: 6000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true, // يمكنه مراجعة المحتوى
    adminAccess: false
  },
  // المسؤول
  administrator: {
    maxDailyQueries: -1, // غير محدود
    maxFileUploads: -1, // غير محدود
    maxFileSize: -1, // غير محدود
    maxTokensPerChat: -1, // غير محدود
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true,
    adminAccess: true // يمكنه إدارة النظام
  }
};
```

### التحقق من وصول المستخدم إلى الميزات

```typescript
// التحقق من وصول المستخدم إلى ميزة
async function checkFeatureAccess(userId: string, feature: string): Promise<boolean> {
  const user = await getUserById(userId);
  if (!user) return false;
  
  const limits = roleLimits[user.role];
  
  switch (feature) {
    case 'voice_chat':
      return limits.voiceEnabled;
    case 'file_upload':
      return limits.maxFileUploads > 0;
    case 'citations':
      return limits.citationsEnabled;
    case 'custom_models':
      return limits.customModelsEnabled;
    case 'moderation':
      return limits.moderationAccess;
    case 'admin':
      return limits.adminAccess;
    default:
      return false;
  }
}
```

### التحقق من حدود الاستخدام

```typescript
// التحقق من حدود الاستخدام
async function checkUsageLimit(userId: string, action: string, value: number = 1): Promise<boolean> {
  const user = await getUserById(userId);
  if (!user) return false;
  
  const limits = roleLimits[user.role];
  
  switch (action) {
    case 'daily_queries':
      return limits.maxDailyQueries === -1 || value <= limits.maxDailyQueries;
    case 'file_size':
      return limits.maxFileSize === -1 || value <= limits.maxFileSize;
    case 'tokens_per_chat':
      return limits.maxTokensPerChat === -1 || value <= limits.maxTokensPerChat;
    default:
      return false;
  }
}
```

---

<div align="center">
  
  **تم إعداد هذا المستند بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
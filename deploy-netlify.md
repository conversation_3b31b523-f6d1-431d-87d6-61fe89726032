# 🚀 دليل نشر الواجهة الأمامية على Netlify

هذا الدليل يشرح خطوات نشر الواجهة الأمامية لمشروع شُريح على منصة Netlify.

## 📋 المتطلبات المسبقة

- حس<PERSON><PERSON> على [Netlify](https://www.netlify.com/)
- مستودع Git (GitHub, GitLab, أو Bitbucket)
- مشروع React (Vite) جاهز للنشر

## 🔧 خطوات النشر

### 1. تجهيز المشروع للنشر

1. تأكد من وجود ملف `netlify.toml` في المجلد الجذر للمشروع:

```toml
[build]
  base = "/"
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

2. تأكد من إعداد متغيرات البيئة في ملف `.env.production`:

```
VITE_API_URL=https://api.yourdomain.com
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 2. نشر المشروع على Netlify

#### الطريقة الأولى: النشر المباشر من Git

1. قم بتسجيل الدخول إلى [Netlify](https://app.netlify.com/)
2. انقر على "New site from Git"
3. اختر مزود Git الخاص بك (GitHub, GitLab, أو Bitbucket)
4. اختر المستودع الذي يحتوي على المشروع
5. قم بتكوين إعدادات البناء:
   - Build command: `npm run build`
   - Publish directory: `dist`
6. انقر على "Show advanced" وأضف متغيرات البيئة من ملف `.env.production`
7. انقر على "Deploy site"

#### الطريقة الثانية: النشر باستخدام Netlify CLI

1. قم بتثبيت Netlify CLI:
```bash
npm install -g netlify-cli
```

2. قم بتسجيل الدخول إلى حسابك:
```bash
netlify login
```

3. قم بربط المشروع بموقع Netlify:
```bash
netlify init
```

4. قم ببناء المشروع:
```bash
npm run build
```

5. قم بنشر المشروع:
```bash
netlify deploy --prod
```

### 3. إعداد المجال المخصص

1. في لوحة تحكم Netlify، انتقل إلى "Site settings" > "Domain management"
2. انقر على "Add custom domain"
3. أدخل المجال الخاص بك (مثل: yourdomain.com)
4. اتبع التعليمات لإعداد سجلات DNS

### 4. تكوين متغيرات البيئة

1. في لوحة تحكم Netlify، انتقل إلى "Site settings" > "Build & deploy" > "Environment"
2. أضف متغيرات البيئة التالية:
   - `VITE_API_URL`: عنوان URL للواجهة الخلفية
   - `VITE_SUPABASE_URL`: عنوان URL لمشروع Supabase
   - `VITE_SUPABASE_ANON_KEY`: مفتاح Anon لمشروع Supabase

### 5. تكوين إعادة التوجيه

تأكد من وجود ملف `_redirects` في مجلد `public` بالمحتوى التالي:

```
/*    /index.html   200
```

هذا سيضمن أن جميع المسارات تُوجه إلى `index.html` لدعم التوجيه على جانب العميل.

## 🔄 تحديث الموقع

### التحديث التلقائي

إذا قمت بإعداد النشر المستمر (CI/CD)، فسيتم تحديث الموقع تلقائيًا عند دفع التغييرات إلى الفرع الرئيسي.

### التحديث اليدوي

لتحديث الموقع يدويًا:

1. قم ببناء المشروع:
```bash
npm run build
```

2. قم بنشر التحديثات:
```bash
netlify deploy --prod
```

## 🔍 مراقبة الموقع

1. في لوحة تحكم Netlify، انتقل إلى "Analytics"
2. يمكنك مراقبة:
   - عدد الزيارات
   - أوقات التحميل
   - أخطاء النشر
   - حالة البناء

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل البناء

إذا فشلت عملية البناء:

1. تحقق من سجلات البناء في لوحة تحكم Netlify
2. تأكد من أن أمر البناء صحيح
3. تأكد من تثبيت جميع التبعيات

### مشاكل التوجيه

إذا كانت هناك مشاكل في التوجيه:

1. تأكد من وجود ملف `_redirects` أو `netlify.toml` مع قواعد إعادة التوجيه الصحيحة
2. تأكد من أن React Router مكون بشكل صحيح

### مشاكل CORS

إذا كانت هناك مشاكل CORS:

1. تأكد من أن الواجهة الخلفية تسمح بطلبات من مجال Netlify
2. أضف مجال Netlify إلى قائمة `CORS_ORIGINS` في إعدادات الواجهة الخلفية

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
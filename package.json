{"name": "legal-chat-admin", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.0", "react-toastify": "^10.0.5", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.10", "@types/node": "^20.9.4", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "vite": "^5.0.0"}}
# 📋 دليل النشر لمشروع شُريح

<div align="center">
  <img src="/public/ShuraihAIUI.svg" alt="شُريح" width="120" height="120">
  
  **خطة النشر الشاملة لمنصة شُريح للذكاء الاصطناعي القانوني**
</div>

---

## 📑 جدول المحتويات

- [نظرة عامة](#-نظرة-عامة)
- [متطلبات النشر](#-متطلبات-النشر)
- [خطوات نشر الواجهة الأمامية](#-خطوات-نشر-الواجهة-الأمامية)
- [خطوات نشر الواجهة الخلفية](#-خطوات-نشر-الواجهة-الخلفية)
- [إعد<PERSON> Supabase](#-إعداد-supabase)
- [إعد<PERSON> Qdrant](#-إعداد-qdrant)
- [إعد<PERSON> نماذج الذكاء الاصطناعي](#-إعداد-نماذج-الذكاء-الاصطناعي)
- [إعداد النطاقات والشهادات](#-إعداد-النطاقات-والشهادات)
- [إعداد المراقبة والتنبيهات](#-إعداد-المراقبة-والتنبيهات)
- [قائمة التحقق قبل الإطلاق](#-قائمة-التحقق-قبل-الإطلاق)
- [خطة الطوارئ](#-خطة-الطوارئ)

---

## 🌟 نظرة عامة

هذا الدليل يوضح خطوات نشر مشروع شُريح للذكاء الاصطناعي القانوني في بيئة الإنتاج. المشروع يتكون من:

1. **الواجهة الأمامية**: تطبيق React مبني باستخدام Refine.dev وAnt Design
2. **الواجهة الخلفية**: API مبني باستخدام FastAPI
3. **قاعدة البيانات**: Supabase (PostgreSQL)
4. **قاعدة بيانات المتجهات**: Qdrant لنظام RAG
5. **نماذج الذكاء الاصطناعي**: Ollama لتشغيل النماذج المحلية

---

## 🛠️ متطلبات النشر

### الخوادم المطلوبة

| المكون | المواصفات الموصى بها | الملاحظات |
|--------|----------------------|-----------|
| **خادم الواجهة الأمامية** | 2 vCPU, 4GB RAM | يمكن استخدام Netlify أو Vercel |
| **خادم الواجهة الخلفية** | 4 vCPU, 8GB RAM | يفضل خادم مخصص أو VPS |
| **خادم نماذج الذكاء الاصطناعي** | 8+ vCPU, 32GB+ RAM, GPU | يتطلب GPU لتشغيل نماذج LLM |
| **خادم Qdrant** | 4 vCPU, 16GB RAM | لقاعدة بيانات المتجهات |

### البرمجيات المطلوبة

- **نظام التشغيل**: Ubuntu 22.04 LTS
- **Docker & Docker Compose**: لتشغيل الخدمات في حاويات
- **Nginx**: كبروكسي عكسي
- **Certbot**: لشهادات SSL
- **Prometheus & Grafana**: للمراقبة (اختياري)

---

## 🖥️ خطوات نشر الواجهة الأمامية

### 1. إعداد متغيرات البيئة للإنتاج

قم بإنشاء ملف `.env.production` في مجلد `frontend` مع المتغيرات التالية:

```
VITE_API_URL=https://api.yourdomain.com
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 2. بناء التطبيق للإنتاج

```bash
cd frontend
npm install
npm run build
```

سيتم إنشاء مجلد `dist` يحتوي على ملفات الإنتاج.

### 3. نشر الملفات على Netlify (موصى به)

#### باستخدام واجهة Netlify

1. قم بإنشاء حساب على [Netlify](https://www.netlify.com/)
2. انقر على "New site from Git"
3. اختر مستودع Git الخاص بك
4. اضبط إعدادات البناء:
   - Build command: `cd frontend && npm install && npm run build`
   - Publish directory: `frontend/dist`
5. أضف متغيرات البيئة من الخطوة 1
6. انقر على "Deploy site"

#### باستخدام Netlify CLI

```bash
npm install -g netlify-cli
cd frontend
netlify login
netlify deploy --prod
```

### 4. إعداد إعادة التوجيه في Netlify

أنشئ ملف `frontend/public/_redirects` بالمحتوى التالي:

```
/*    /index.html   200
```

هذا سيضمن أن جميع المسارات تُوجه إلى `index.html` لدعم التوجيه على جانب العميل.

### 5. تكوين المجال المخصص

1. أضف المجال المخصص الخاص بك في إعدادات Netlify
2. قم بتكوين سجلات DNS للإشارة إلى خوادم Netlify
3. انتظر حتى يتم إصدار شهادة SSL تلقائيًا

---

## 🔧 خطوات نشر الواجهة الخلفية

### 1. إعداد الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Docker و Docker Compose
sudo apt install -y docker.io docker-compose
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER

# تثبيت Nginx
sudo apt install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 2. إنشاء ملف Docker Compose

أنشئ ملف `docker-compose.yml` في مجلد المشروع:

```yaml
version: '3.8'

services:
  api:
    build: ./backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/arabic_ai
      - SECRET_KEY=your-production-secret-key
      - CORS_ORIGINS=https://yourdomain.com
    volumes:
      - ./backend:/app
    depends_on:
      - db

  db:
    image: postgres:15
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=arabic_ai
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### 3. إنشاء ملف Dockerfile للواجهة الخلفية

أنشئ ملف `backend/Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 4. إعداد Nginx كبروكسي عكسي

أنشئ ملف `/etc/nginx/sites-available/api.yourdomain.com`:

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

ثم قم بتفعيله:

```bash
sudo ln -s /etc/nginx/sites-available/api.yourdomain.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. إعداد SSL مع Certbot

```bash
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d api.yourdomain.com
```

### 6. بدء تشغيل الخدمات

```bash
cd /path/to/project
docker-compose up -d
```

---

## 🗃️ إعداد Supabase

### 1. إنشاء مشروع Supabase

1. قم بإنشاء حساب على [Supabase](https://supabase.com/)
2. أنشئ مشروعًا جديدًا
3. احتفظ بعنوان URL ومفتاح API الخاص بالمشروع

### 2. تطبيق هجرات قاعدة البيانات

```bash
# تثبيت Supabase CLI
npm install -g supabase

# تسجيل الدخول
supabase login

# ربط المشروع
supabase link --project-ref your-project-ref

# تطبيق الهجرات
supabase db push
```

### 3. إعداد المصادقة

1. انتقل إلى قسم "Authentication" في لوحة تحكم Supabase
2. قم بتكوين إعدادات البريد الإلكتروني
3. قم بتكوين مزودي المصادقة الاجتماعية (اختياري)

### 4. إعداد سياسات RLS (أمان مستوى الصف)

تأكد من تطبيق سياسات RLS المناسبة لكل جدول. يمكنك التحقق من ذلك في قسم "Table Editor" في لوحة تحكم Supabase.

---

## 🔍 إعداد Qdrant

### 1. نشر Qdrant باستخدام Docker

أضف الخدمة التالية إلى ملف `docker-compose.yml`:

```yaml
qdrant:
  image: qdrant/qdrant
  restart: always
  ports:
    - "6333:6333"
    - "6334:6334"
  volumes:
    - qdrant_data:/qdrant/storage
  environment:
    - QDRANT_ALLOW_CORS=true

volumes:
  qdrant_data:
```

### 2. إنشاء المجموعات (Collections)

بعد تشغيل Qdrant، قم بإنشاء المجموعات اللازمة:

```python
import requests

# إنشاء مجموعة للمستندات القانونية
response = requests.put(
    "http://localhost:6333/collections/legal_documents",
    json={
        "vectors": {
            "size": 768,
            "distance": "Cosine"
        }
    }
)
print(response.json())
```

### 3. تكوين النسخ الاحتياطي

أنشئ سكريبت لعمل نسخ احتياطية دورية:

```bash
#!/bin/bash
# backup-qdrant.sh

BACKUP_DIR="/path/to/backups/qdrant"
DATE=$(date +%Y-%m-%d)

# إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
mkdir -p $BACKUP_DIR

# عمل نسخة احتياطية
docker exec -it qdrant qdrant_backup --db-path /qdrant/storage --output /qdrant/backups/backup-$DATE

# نسخ النسخة الاحتياطية إلى مجلد خارجي
docker cp qdrant:/qdrant/backups/backup-$DATE $BACKUP_DIR/

# حذف النسخ الاحتياطية القديمة (أكثر من 7 أيام)
find $BACKUP_DIR -name "backup-*" -type d -mtime +7 -exec rm -rf {} \;
```

أضف هذا السكريبت إلى cron للتشغيل اليومي:

```bash
chmod +x backup-qdrant.sh
(crontab -l ; echo "0 2 * * * /path/to/backup-qdrant.sh") | crontab -
```

---

## 🤖 إعداد نماذج الذكاء الاصطناعي

### 1. تثبيت Ollama

```bash
# تثبيت Ollama
curl -fsSL https://ollama.com/install.sh | sh

# تشغيل Ollama كخدمة
sudo systemctl enable ollama
sudo systemctl start ollama
```

### 2. تنزيل النماذج المطلوبة

```bash
# تنزيل نماذج LLM
ollama pull llama3:8b
ollama pull qwen:7b
ollama pull deepseek-coder:6.7b

# إنشاء نموذج قانوني مخصص
cat > legal-llama.modelfile << EOF
FROM llama3:8b
SYSTEM You are a legal assistant specialized in Saudi law. Always provide accurate legal information and cite relevant laws and regulations.
EOF

ollama create legal-llama -f legal-llama.modelfile
```

### 3. إعداد خدمة Ollama

أنشئ ملف `/etc/systemd/system/ollama.service`:

```ini
[Unit]
Description=Ollama Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root
ExecStart=/usr/local/bin/ollama serve
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

ثم قم بتفعيله:

```bash
sudo systemctl daemon-reload
sudo systemctl enable ollama
sudo systemctl start ollama
```

### 4. تكوين API للنماذج

تأكد من تحديث جدول `external_apis` في Supabase بعناوين URL الصحيحة للنماذج المثبتة.

---

## 🌐 إعداد النطاقات والشهادات

### 1. إعداد سجلات DNS

قم بإنشاء سجلات DNS التالية:

| النوع | الاسم | القيمة |
|-------|------|--------|
| A | yourdomain.com | عنوان IP للواجهة الأمامية |
| A | api.yourdomain.com | عنوان IP للواجهة الخلفية |
| A | ai.yourdomain.com | عنوان IP لخادم نماذج الذكاء الاصطناعي |
| CNAME | www | yourdomain.com |

### 2. إعداد شهادات SSL

استخدم Certbot لإعداد شهادات SSL لجميع النطاقات:

```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com
sudo certbot --nginx -d ai.yourdomain.com
```

### 3. تكوين التجديد التلقائي

تأكد من تكوين التجديد التلقائي لشهادات SSL:

```bash
sudo systemctl status certbot.timer
```

---

## 📊 إعداد المراقبة والتنبيهات

### 1. تثبيت Prometheus و Grafana

أضف الخدمات التالية إلى ملف `docker-compose.yml`:

```yaml
prometheus:
  image: prom/prometheus
  restart: always
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
    - prometheus_data:/prometheus

grafana:
  image: grafana/grafana
  restart: always
  ports:
    - "3000:3000"
  volumes:
    - grafana_data:/var/lib/grafana
  depends_on:
    - prometheus

volumes:
  prometheus_data:
  grafana_data:
```

### 2. إعداد ملف تكوين Prometheus

أنشئ ملف `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'api'
    static_configs:
      - targets: ['api:8000']

  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### 3. إعداد Node Exporter لمراقبة الخادم

أضف الخدمة التالية إلى ملف `docker-compose.yml`:

```yaml
node-exporter:
  image: prom/node-exporter
  restart: always
  ports:
    - "9100:9100"
  volumes:
    - /proc:/host/proc:ro
    - /sys:/host/sys:ro
    - /:/rootfs:ro
  command:
    - '--path.procfs=/host/proc'
    - '--path.sysfs=/host/sys'
    - '--path.rootfs=/rootfs'
```

### 4. إعداد لوحات Grafana

1. افتح Grafana على المنفذ 3000
2. قم بتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور الافتراضيين (admin/admin)
3. أضف Prometheus كمصدر بيانات
4. قم باستيراد لوحات القياس الجاهزة لـ Node Exporter و FastAPI

---

## ✅ قائمة التحقق قبل الإطلاق

### 1. الأمان

- [ ] تم تغيير جميع كلمات المرور الافتراضية
- [ ] تم تكوين جدار الحماية (Firewall)
- [ ] تم تفعيل HTTPS لجميع النطاقات
- [ ] تم تأمين مفاتيح API وإخفاؤها من الكود المصدري
- [ ] تم تكوين سياسات RLS في Supabase

### 2. الأداء

- [ ] تم اختبار الأداء تحت الحمل
- [ ] تم تكوين التخزين المؤقت (Caching)
- [ ] تم تحسين استجابة API
- [ ] تم ضغط الأصول الثابتة

### 3. المراقبة

- [ ] تم إعداد مراقبة الخدمات
- [ ] تم إعداد تنبيهات للأحداث الحرجة
- [ ] تم إعداد تسجيل الأخطاء

### 4. النسخ الاحتياطي

- [ ] تم إعداد نسخ احتياطية دورية لقاعدة البيانات
- [ ] تم إعداد نسخ احتياطية لـ Qdrant
- [ ] تم اختبار استعادة النسخ الاحتياطية

### 5. التوثيق

- [ ] تم توثيق عملية النشر
- [ ] تم توثيق عملية الصيانة
- [ ] تم توثيق إجراءات الطوارئ

---

## 🚨 خطة الطوارئ

### 1. استعادة قاعدة البيانات

في حالة تلف قاعدة البيانات:

```bash
# استعادة قاعدة بيانات Supabase
supabase db restore --project-ref your-project-ref --backup-file backup.sql

# استعادة Qdrant
docker stop qdrant
docker rm qdrant
rm -rf /path/to/qdrant_data
mkdir -p /path/to/qdrant_data
tar -xzf /path/to/backups/qdrant/backup-YYYY-MM-DD.tar.gz -C /path/to/qdrant_data
docker-compose up -d qdrant
```

### 2. استعادة الخدمات

في حالة تعطل الخدمات:

```bash
# إعادة تشغيل جميع الخدمات
docker-compose down
docker-compose up -d

# التحقق من حالة الخدمات
docker-compose ps
```

### 3. التواصل مع الدعم

في حالة وجود مشاكل تقنية معقدة:

- **الدعم التقني**: <EMAIL>
- **رقم الطوارئ**: +966 5XXXXXXXX

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
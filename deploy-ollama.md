# 🚀 دليل إعداد ونشر Ollama لمشروع شُريح

هذا الدليل يشرح خطوات إعداد ونشر Ollama لتشغيل نماذج الذكاء الاصطناعي المحلية في مشروع شُريح.

## 📋 المتطلبات المسبقة

### متطلبات الأجهزة
- **وحدة المعالجة المركزية (CPU)**: 8+ أنوية
- **ذاكرة الوصول العشوائي (RAM)**: 32GB+ (64GB موصى به للنماذج الكبيرة)
- **وحدة معالجة الرسومات (GPU)**: NVIDIA RTX 4080/4090 أو A100 (موصى به)
- **مساحة التخزين**: 100GB+ SSD

### متطلبات البرمجيات
- نظام تشغيل Ubuntu 22.04 LTS
- NVIDIA CUDA 12.1+ (إذا كنت تستخدم GPU)
- Docker و Docker Compose (اختياري)

## 🔧 خطوات الإعداد

### 1. تثبيت Ollama

#### التثبيت المباشر

```bash
# تثبيت Ollama
curl -fsSL https://ollama.com/install.sh | sh

# التحقق من التثبيت
ollama --version
```

#### التثبيت باستخدام Docker

```bash
# إنشاء مجلد للبيانات
mkdir -p /opt/ollama/models

# إنشاء ملف docker-compose.yml
cat > /opt/ollama/docker-compose.yml << 'EOF'
version: '3.8'

services:
  ollama:
    image: ollama/ollama
    restart: always
    ports:
      - "11434:11434"
    volumes:
      - ./models:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
EOF

# تشغيل Ollama
cd /opt/ollama
docker-compose up -d
```

### 2. تنزيل النماذج المطلوبة

```bash
# تنزيل نماذج LLM الأساسية
ollama pull llama3:8b
ollama pull qwen:7b
ollama pull deepseek-coder:6.7b

# تنزيل نماذج متخصصة (اختياري)
ollama pull mistral:7b
ollama pull phi3:mini
```

### 3. إنشاء نماذج مخصصة

#### إنشاء نموذج قانوني مخصص

```bash
# إنشاء ملف Modelfile
cat > legal-llama.modelfile << 'EOF'
FROM llama3:8b

# تعليمات النظام للمساعد القانوني
SYSTEM """
أنت مساعد قانوني متخصص في القانون السعودي والشريعة الإسلامية. 
يجب عليك دائمًا:
1. تقديم معلومات قانونية دقيقة ومستندة إلى المصادر.
2. الاستشهاد بالأنظمة واللوائح والقوانين ذات الصلة.
3. توضيح أن إجاباتك هي للمعلومات العامة فقط وليست استشارة قانونية رسمية.
4. الإشارة إلى المواد القانونية المحددة عند الاقتباس.
5. الامتناع عن الإجابة إذا كان السؤال خارج نطاق معرفتك القانونية.

المصادر القانونية الرئيسية:
- النظام الأساسي للحكم
- نظام المرافعات الشرعية
- نظام الإجراءات الجزائية
- نظام العمل
- نظام الشركات
- نظام التنفيذ
- نظام المحاماة
"""

# تعديل معاملات النموذج للدقة القانونية
PARAMETER temperature 0.2
PARAMETER top_p 0.9
PARAMETER top_k 40
EOF

# إنشاء النموذج
ollama create legal-llama -f legal-llama.modelfile
```

#### إنشاء نموذج مخصص للترجمة

```bash
# إنشاء ملف Modelfile
cat > arabic-translator.modelfile << 'EOF'
FROM qwen:7b

# تعليمات النظام للمترجم
SYSTEM """
أنت مترجم محترف متخصص في الترجمة بين اللغتين العربية والإنجليزية.
يجب عليك دائمًا:
1. ترجمة النص بدقة مع الحفاظ على المعنى الأصلي.
2. مراعاة السياق الثقافي والاصطلاحات اللغوية.
3. الحفاظ على أسلوب النص الأصلي (رسمي، غير رسمي، تقني، إلخ).
4. ترجمة المصطلحات التقنية والقانونية بدقة.
5. تقديم ترجمة واحدة فقط دون اقتراحات بديلة ما لم يُطلب منك ذلك.
"""

# تعديل معاملات النموذج للترجمة
PARAMETER temperature 0.3
PARAMETER top_p 0.9
EOF

# إنشاء النموذج
ollama create arabic-translator -f arabic-translator.modelfile
```

### 4. إعداد خدمة Ollama

#### إعداد خدمة systemd

```bash
# إنشاء ملف خدمة systemd
cat > /etc/systemd/system/ollama.service << 'EOF'
[Unit]
Description=Ollama Service
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/ollama serve
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# تفعيل وتشغيل الخدمة
systemctl daemon-reload
systemctl enable ollama
systemctl start ollama
```

### 5. إعداد واجهة API للنماذج

#### إنشاء خدمة FastAPI بسيطة

```bash
# إنشاء مجلد للمشروع
mkdir -p /opt/ai-api
cd /opt/ai-api

# إنشاء ملف requirements.txt
cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn==0.24.0
httpx==0.25.1
pydantic==2.4.2
python-dotenv==1.0.0
EOF

# إنشاء ملف main.py
cat > main.py << 'EOF'
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import httpx
import os
from typing import Dict, Any, Optional, List

app = FastAPI(title="Shuraih AI API")

class GenerateRequest(BaseModel):
    prompt: str
    model: str = "llama3:8b"
    system: Optional[str] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000

class GenerateResponse(BaseModel):
    text: str
    model: str
    tokens_used: int

@app.post("/generate", response_model=GenerateResponse)
async def generate(request: GenerateRequest):
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": request.model,
                    "prompt": request.prompt,
                    "system": request.system,
                    "options": {
                        "temperature": request.temperature,
                        "num_predict": request.max_tokens
                    }
                }
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=response.text)
            
            result = response.json()
            return {
                "text": result["response"],
                "model": request.model,
                "tokens_used": result.get("eval_count", 0)
            }
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Error communicating with Ollama: {str(e)}")

@app.get("/models")
async def list_models():
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags")
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=response.text)
            
            return response.json()
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Error communicating with Ollama: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
EOF

# إنشاء ملف Dockerfile
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
EOF

# إنشاء ملف docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  ai-api:
    build: .
    restart: always
    ports:
      - "8080:8080"
    depends_on:
      - ollama
    networks:
      - ai-network

  ollama:
    image: ollama/ollama
    restart: always
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - ai-network

networks:
  ai-network:
    driver: bridge

volumes:
  ollama-data:
EOF

# بناء وتشغيل الخدمات
docker-compose up -d
```

### 6. إعداد Nginx كبروكسي عكسي

```bash
# إنشاء ملف تكوين Nginx
cat > /etc/nginx/sites-available/ai.yourdomain.com << 'EOF'
server {
    listen 80;
    server_name ai.yourdomain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# تفعيل الموقع
ln -s /etc/nginx/sites-available/ai.yourdomain.com /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# إعداد SSL
certbot --nginx -d ai.yourdomain.com
```

## 🔄 تحديث النماذج

### 1. تحديث نموذج موجود

```bash
# تحديث نموذج
ollama pull llama3:8b

# إعادة إنشاء النماذج المخصصة
ollama create legal-llama -f legal-llama.modelfile
```

### 2. إضافة نموذج جديد

```bash
# تنزيل نموذج جديد
ollama pull mixtral:8x7b

# إنشاء نموذج مخصص جديد
cat > mixtral-legal.modelfile << 'EOF'
FROM mixtral:8x7b
SYSTEM "أنت مساعد قانوني متخصص في القانون السعودي."
PARAMETER temperature 0.2
EOF

ollama create mixtral-legal -f mixtral-legal.modelfile
```

## 📊 مراقبة Ollama

### 1. مراقبة استخدام الموارد

```bash
# مراقبة استخدام وحدة المعالجة المركزية والذاكرة
docker stats

# مراقبة استخدام وحدة معالجة الرسومات
nvidia-smi
```

### 2. مراقبة سجلات Ollama

```bash
# عرض سجلات Ollama
docker logs -f ollama

# عرض سجلات واجهة API
docker logs -f ai-api
```

### 3. إعداد Prometheus و Grafana (اختياري)

أضف الخدمات التالية إلى ملف `docker-compose.yml`:

```yaml
prometheus:
  image: prom/prometheus
  restart: always
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
    - prometheus_data:/prometheus
  networks:
    - ai-network

grafana:
  image: grafana/grafana
  restart: always
  ports:
    - "3000:3000"
  volumes:
    - grafana_data:/var/lib/grafana
  depends_on:
    - prometheus
  networks:
    - ai-network

volumes:
  prometheus_data:
  grafana_data:
```

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل الذاكرة

إذا واجهت مشاكل في الذاكرة:

```bash
# التحقق من استخدام الذاكرة
free -h

# زيادة مساحة التبادل
sudo fallocate -l 16G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

### مشاكل وحدة معالجة الرسومات

إذا واجهت مشاكل في وحدة معالجة الرسومات:

```bash
# التحقق من تعريفات NVIDIA
nvidia-smi

# إعادة تثبيت تعريفات NVIDIA
sudo apt purge nvidia*
sudo apt autoremove
sudo apt install nvidia-driver-535 nvidia-cuda-toolkit
```

### مشاكل الاتصال

إذا واجهت مشاكل في الاتصال بـ Ollama:

```bash
# التحقق من حالة خدمة Ollama
systemctl status ollama

# التحقق من منافذ الاستماع
netstat -tuln | grep 11434

# إعادة تشغيل خدمة Ollama
systemctl restart ollama
```

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
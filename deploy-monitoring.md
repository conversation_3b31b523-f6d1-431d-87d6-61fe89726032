# 🚀 دليل إعداد نظام المراقبة والتنبيهات لمشروع شُريح

هذا الدليل يشرح خطوات إعداد نظام شامل لمراقبة وتنبيه جميع مكونات مشروع شُريح.

## 📋 المتطلبات المسبقة

- خادم Ubuntu 22.04 LTS
- Docker و Docker Compose مثبتان
- وصول SSH إلى جميع الخوادم المراد مراقبتها

## 🔧 خطوات الإعداد

### 1. إنشاء مجلد للمراقبة

```bash
mkdir -p /opt/monitoring
cd /opt/monitoring
```

### 2. إعداد Prometheus

#### إنشاء ملف تكوين Prometheus

```bash
cat > prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "rules/*.yml"

scrape_configs:
  # مراقبة Prometheus نفسه
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # مراقبة Node Exporter (للخوادم)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          instance: 'main-server'
      # يمكن إضافة خوادم أخرى هنا
      # - targets: ['node-exporter-2:9100']
      #   labels:
      #     instance: 'ai-server'

  # مراقبة الواجهة الخلفية
  - job_name: 'backend'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['backend:8000']

  # مراقبة Qdrant
  - job_name: 'qdrant'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['qdrant:6333']

  # مراقبة Cadvisor (لحاويات Docker)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
EOF

# إنشاء مجلد للقواعد
mkdir -p rules

# إنشاء قواعد التنبيه
cat > rules/alerts.yml << 'EOF'
groups:
  - name: shuraih_alerts
    rules:
      # تنبيهات الخادم
      - alert: HighCPULoad
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "ارتفاع استخدام وحدة المعالجة المركزية ({{ $value }}%)"
          description: "استخدام وحدة المعالجة المركزية مرتفع على {{ $labels.instance }} منذ 5 دقائق"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "ارتفاع استخدام الذاكرة ({{ $value }}%)"
          description: "استخدام الذاكرة مرتفع على {{ $labels.instance }} منذ 5 دقائق"

      - alert: LowDiskSpace
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "مساحة القرص منخفضة ({{ $value }}%)"
          description: "مساحة القرص منخفضة على {{ $labels.instance }} منذ 5 دقائق"

      # تنبيهات API
      - alert: APIHighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, instance, job)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "ارتفاع وقت استجابة API"
          description: "95% من طلبات API تستغرق أكثر من 1 ثانية"

      - alert: APIHighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) * 100 > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "ارتفاع معدل أخطاء API ({{ $value }}%)"
          description: "معدل أخطاء API أعلى من 5% منذ 5 دقائق"
EOF
```

### 3. إعداد Alertmanager

```bash
cat > alertmanager.yml << 'EOF'
global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK_URL'

route:
  group_by: ['alertname', 'job']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  routes:
    - match:
        severity: critical
      receiver: 'slack-critical'
      continue: true

receivers:
  - name: 'slack-notifications'
    slack_configs:
      - channel: '#monitoring'
        send_resolved: true
        title: '{{ .GroupLabels.alertname }}'
        text: >-
          {{ range .Alerts }}
            *Alert:* {{ .Annotations.summary }}
            *Description:* {{ .Annotations.description }}
            *Severity:* {{ .Labels.severity }}
            *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  - name: 'slack-critical'
    slack_configs:
      - channel: '#alerts-critical'
        send_resolved: true
        title: '[CRITICAL] {{ .GroupLabels.alertname }}'
        text: >-
          {{ range .Alerts }}
            *Alert:* {{ .Annotations.summary }}
            *Description:* {{ .Annotations.description }}
            *Severity:* {{ .Labels.severity }}
            *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
EOF
```

### 4. إعداد Grafana

```bash
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# إعداد مصدر البيانات
cat > grafana/provisioning/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

# إعداد مجلد لوحات المراقبة
cat > grafana/provisioning/dashboards/dashboards.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'Default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    options:
      path: /var/lib/grafana/dashboards
EOF

# إنشاء مجلد للوحات المراقبة
mkdir -p grafana/dashboards
```

### 5. إعداد Docker Compose

```bash
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  alertmanager:
    image: prom/alertmanager
    restart: always
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'

  grafana:
    image: grafana/grafana
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=secure_password
      - GF_USERS_ALLOW_SIGN_UP=false

  node-exporter:
    image: prom/node-exporter
    restart: always
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--path.rootfs=/rootfs'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'

  cadvisor:
    image: gcr.io/cadvisor/cadvisor
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro

volumes:
  prometheus_data:
  alertmanager_data:
  grafana_data:
EOF
```

### 6. تشغيل خدمات المراقبة

```bash
docker-compose up -d
```

### 7. إعداد لوحات Grafana

بعد تشغيل الخدمات، قم بالوصول إلى Grafana على المنفذ 3000 (http://your-server:3000) وقم بإعداد لوحات المراقبة.

يمكنك استيراد لوحات المراقبة الجاهزة من [Grafana Dashboards](https://grafana.com/grafana/dashboards/):

- Node Exporter Dashboard (ID: 1860)
- Docker Monitoring (ID: 893)
- Prometheus Stats (ID: 2)

### 8. إعداد Nginx كبروكسي عكسي (اختياري)

```bash
# إنشاء ملف تكوين Nginx
cat > /etc/nginx/sites-available/monitoring.yourdomain.com << 'EOF'
server {
    listen 80;
    server_name monitoring.yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# تفعيل الموقع
ln -s /etc/nginx/sites-available/monitoring.yourdomain.com /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# إعداد SSL
certbot --nginx -d monitoring.yourdomain.com
```

## 🔄 إعداد مراقبة الواجهة الخلفية

### 1. إضافة Prometheus Middleware إلى FastAPI

قم بتحديث ملف `backend/app/main.py`:

```python
from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import time
from prometheus_client import Counter, Histogram, make_asgi_app

# إعداد مقاييس Prometheus
REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total HTTP Requests",
    ["method", "endpoint", "status"]
)
REQUEST_LATENCY = Histogram(
    "http_request_duration_seconds",
    "HTTP Request Duration in seconds",
    ["method", "endpoint"]
)

app = FastAPI(title="Shuraih API")

# إضافة Prometheus middleware
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Middleware لقياس الطلبات
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    method = request.method
    endpoint = request.url.path
    
    # استثناء مسار المقاييس نفسه
    if endpoint == "/metrics":
        return await call_next(request)
    
    start_time = time.time()
    
    # تنفيذ الطلب
    response = await call_next(request)
    
    # قياس وقت الاستجابة
    duration = time.time() - start_time
    status = response.status_code
    
    # تسجيل المقاييس
    REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
    REQUEST_LATENCY.labels(method=method, endpoint=endpoint).observe(duration)
    
    return response
```

### 2. تثبيت التبعيات اللازمة

أضف التبعيات التالية إلى ملف `backend/requirements.txt`:

```
prometheus-client==0.17.1
```

## 📊 إعداد مراقبة Supabase

### 1. إعداد مراقبة قاعدة البيانات

يمكنك مراقبة قاعدة بيانات Supabase باستخدام postgres_exporter:

```bash
cat > docker-compose.override.yml << 'EOF'
services:
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter
    restart: always
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
    depends_on:
      - prometheus
EOF

# تحديث ملف تكوين Prometheus
cat >> prometheus.yml << 'EOF'
  # مراقبة PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
EOF
```

### 2. إعداد تنبيهات Supabase

أضف القواعد التالية إلى ملف `rules/alerts.yml`:

```yaml
  # تنبيهات Supabase
  - alert: SupabaseHighDatabaseConnections
    expr: pg_stat_activity_count > 100
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "ارتفاع عدد اتصالات قاعدة البيانات ({{ $value }})"
      description: "عدد اتصالات قاعدة البيانات مرتفع منذ 5 دقائق"

  - alert: SupabaseDatabaseHighCPU
    expr: rate(pg_stat_database_xact_commit[5m]) + rate(pg_stat_database_xact_rollback[5m]) > 500
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "ارتفاع استخدام وحدة المعالجة المركزية لقاعدة البيانات"
      description: "معدل المعاملات مرتفع في قاعدة البيانات منذ 5 دقائق"
```

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل Prometheus

إذا واجهت مشاكل في Prometheus:

```bash
# التحقق من حالة خدمة Prometheus
docker-compose ps prometheus

# التحقق من سجلات Prometheus
docker-compose logs prometheus

# التحقق من تكوين Prometheus
docker-compose exec prometheus promtool check config /etc/prometheus/prometheus.yml
```

### مشاكل Grafana

إذا واجهت مشاكل في Grafana:

```bash
# التحقق من حالة خدمة Grafana
docker-compose ps grafana

# التحقق من سجلات Grafana
docker-compose logs grafana

# إعادة تعيين كلمة مرور المسؤول
docker-compose exec grafana grafana-cli admin reset-admin-password new_password
```

### مشاكل Alertmanager

إذا واجهت مشاكل في Alertmanager:

```bash
# التحقق من حالة خدمة Alertmanager
docker-compose ps alertmanager

# التحقق من سجلات Alertmanager
docker-compose logs alertmanager

# التحقق من تكوين Alertmanager
docker-compose exec alertmanager amtool check-config /etc/alertmanager/alertmanager.yml
```

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
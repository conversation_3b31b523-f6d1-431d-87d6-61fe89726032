# 🔄 دليل تكامل بوابة الدفع Moyasar مع مشروع شُريح

<div align="center">
  <img src="/public/ShuraihAIUI.svg" alt="شُريح" width="120" height="120">
  
  **دليل شامل لتكامل بوابة الدفع Moyasar مع منصة شُريح للذكاء الاصطناعي القانوني**
</div>

## 📋 جدول المحتويات

- [1. تكامل الدفع: نظرة شاملة](#1-تكامل-الدفع-نظرة-شاملة)
- [2. إعداد Moyasar وخطوات الـ Webhook](#2-إعداد-moyasar-وخطوات-الـ-webhook)
- [3. كود الدفع في واجهة المستخدم (React)](#3-كود-الدفع-في-واجهة-المستخدم-react)
- [4. نقاط الربط في Backend (FastAPI)](#4-نقاط-الربط-في-backend-fastapi)
- [5. جداول Supabase وربطها بـ Refine.dev](#5-جداول-supabase-وربطها-بـ-refinedev)
- [6. الأمان](#6-الأمان)
- [7. تجربة المستخدم](#7-تجربة-المستخدم)
- [8. تسلسل التنفيذ (Step-by-Step)](#8-تسلسل-التنفيذ-step-by-step)
- [9. الأسئلة المتكررة/الملاحظات النهائية](#9-الأسئلة-المتكررةالملاحظات-النهائية)

---

## 1. تكامل الدفع: نظرة شاملة

### ما هو؟
تكامل بوابة الدفع Moyasar مع منصة شُريح للذكاء الاصطناعي القانوني، لتمكين المستخدمين من الاشتراك في الباقات المختلفة وإدارة المدفوعات بسلاسة.

### لماذا؟
- **تجربة مستخدم سلسة**: دفع سهل دون مغادرة التطبيق
- **أمان عالي**: توافق مع معايير PCI-DSS
- **مرونة**: دعم لمختلف وسائل الدفع (مدى، فيزا، ماستركارد)
- **تكامل محلي**: بوابة دفع سعودية تدعم العملة المحلية والمتطلبات المحلية

### المعمارية العامة

```mermaid
sequenceDiagram
    participant User as المستخدم
    participant UI as واجهة المستخدم
    participant API as FastAPI Backend
    participant Moyasar as بوابة Moyasar
    participant DB as Supabase
    participant Admin as لوحة المسؤول

    User->>UI: اختيار باقة اشتراك
    UI->>API: طلب إنشاء دفعة
    API->>Moyasar: إنشاء جلسة دفع
    Moyasar-->>UI: إرجاع نموذج الدفع
    User->>UI: إدخال بيانات البطاقة
    UI->>Moyasar: إرسال بيانات الدفع
    Moyasar-->>API: إشعار webhook بنجاح الدفع
    API->>DB: تحديث حالة الاشتراك
    DB-->>Admin: عرض تقارير المدفوعات
    DB-->>UI: تحديث واجهة المستخدم
```

---

## 2. إعداد Moyasar وخطوات الـ Webhook

### ما هو؟
Moyasar هي بوابة دفع سعودية تتيح قبول المدفوعات عبر الإنترنت بطرق متعددة.

### لماذا؟
- دعم للبطاقات المحلية (مدى) والعالمية
- واجهة برمجة بسيطة وموثقة
- دعم للعملة المحلية (الريال السعودي)
- تكامل سهل مع التطبيقات الويب

### خطوات الإعداد

#### 1. إنشاء حساب Moyasar
1. قم بالتسجيل على [موقع Moyasar](https://moyasar.com/en/signup)
2. أكمل عملية التحقق من الهوية والمستندات المطلوبة

#### 2. الحصول على مفاتيح API
1. انتقل إلى لوحة التحكم > الإعدادات > مفاتيح API
2. احصل على:
   - **مفتاح الاختبار العام** (`pk_test_XXXXXXXX`) - للبيئة التجريبية
   - **مفتاح الاختبار السري** (`sk_test_XXXXXXXX`) - للبيئة التجريبية
   - **المفتاح العام** (`pk_live_XXXXXXXX`) - للإنتاج
   - **المفتاح السري** (`sk_live_XXXXXXXX`) - للإنتاج

#### 3. إعداد Webhook
1. انتقل إلى لوحة التحكم > الإعدادات > Webhooks
2. أضف عنوان URL للـ webhook: `https://api.yourdomain.com/api/webhook/moyasar`
3. قم بإنشاء وحفظ رمز سري للتحقق من صحة الطلبات
4. اختر الأحداث التي ترغب في تلقي إشعارات عنها (مثل `payment_paid`, `payment_failed`)

#### 4. تخزين المفاتيح في متغيرات البيئة
أضف المفاتيح إلى ملف `.env` في مشروع Backend:

```
MOYASAR_API_KEY=sk_test_XXXXXXXX
MOYASAR_PUBLISHABLE_KEY=pk_test_XXXXXXXX
MOYASAR_WEBHOOK_SECRET=your-webhook-secret
```

---

## 3. كود الدفع في واجهة المستخدم (React)

### ما هو؟
مكونات React لعرض نموذج الدفع وإدارة عملية الدفع في واجهة المستخدم.

### لماذا؟
- تجربة مستخدم سلسة دون مغادرة التطبيق
- تكامل مباشر مع بوابة الدفع
- إمكانية تخصيص الشكل والتجربة

### التنفيذ

#### 1. تثبيت مكتبة Moyasar
إضافة مكتبة Moyasar إلى ملف `index.html`:

```html
<!-- frontend/index.html -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <!-- ... -->
    <link rel="stylesheet" href="https://unpkg.com/[email protected]/dist/moyasar.css" />
    <script src="https://unpkg.com/[email protected]/dist/moyasar.umd.js"></script>
  </head>
  <body>
    <!-- ... -->
  </body>
</html>
```

#### 2. إنشاء مكون الدفع
إنشاء ملف `frontend/src/components/payment/MoyasarCheckout.tsx`:

```tsx
import { useEffect, useState } from 'react';
import { Button, Modal, Spin, Alert, Typography } from 'antd';
import { api } from '../../lib/api';

const { Title, Text } = Typography;

interface MoyasarCheckoutProps {
  planId: string;
  planName: string;
  amount: number; // بالريال السعودي
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const MoyasarCheckout: React.FC<MoyasarCheckoutProps> = ({
  planId,
  planName,
  amount,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentInitiated, setPaymentInitiated] = useState(false);

  useEffect(() => {
    // إعادة تعيين الحالة عند فتح النافذة
    if (isOpen) {
      setLoading(true);
      setError(null);
      setPaymentInitiated(false);
      
      // إنشاء جلسة دفع جديدة
      initializePayment();
    }
  }, [isOpen]);

  const initializePayment = async () => {
    try {
      // تحويل المبلغ إلى هللات (1 ريال = 100 هللة)
      const amountInHalalas = Math.round(amount * 100);
      
      // طلب إنشاء دفعة من الخادم
      const response = await api.post('/api/payments/initiate', {
        plan_id: planId,
        amount: amountInHalalas,
        description: `اشتراك في باقة ${planName}`
      });
      
      const { payment_id, transaction_url, publishable_key } = response.data;
      
      // تهيئة نموذج الدفع
      if ((window as any).Moyasar) {
        (window as any).Moyasar.init({
          element: '.moyasar-form',
          amount: amountInHalalas,
          currency: 'SAR',
          description: `اشتراك في باقة ${planName}`,
          publishable_api_key: publishable_key,
          callback_url: window.location.origin + '/payment-callback',
          methods: ['creditcard', 'applepay', 'stcpay'],
          metadata: {
            plan_id: planId,
            user_email: '<EMAIL>' // يمكن استبدالها ببريد المستخدم الفعلي
          },
          on_completed: (payment: any) => {
            // تم إكمال الدفع بنجاح
            verifyPayment(payment.id);
          }
        });
        
        setPaymentInitiated(true);
      } else {
        setError('فشل في تحميل مكتبة Moyasar');
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'حدث خطأ أثناء إنشاء جلسة الدفع');
    } finally {
      setLoading(false);
    }
  };

  const verifyPayment = async (paymentId: string) => {
    try {
      setLoading(true);
      
      // التحقق من حالة الدفع
      const response = await api.post('/api/payments/verify', {
        payment_id: paymentId
      });
      
      if (response.data.status === 'paid') {
        // تم الدفع بنجاح
        onSuccess();
      } else {
        // الدفع لم يكتمل بعد
        setError('لم يتم تأكيد الدفع بعد. سيتم إشعارك عند اكتمال العملية.');
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'حدث خطأ أثناء التحقق من حالة الدفع');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`الاشتراك في باقة ${planName}`}
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={700}
      centered
    >
      {loading ? (
        <div className="flex flex-col items-center justify-center py-10">
          <Spin size="large" />
          <Text className="mt-4">جاري تجهيز بوابة الدفع...</Text>
        </div>
      ) : error ? (
        <div className="py-6">
          <Alert
            type="error"
            message="خطأ في عملية الدفع"
            description={error}
            showIcon
          />
          <div className="mt-4 flex justify-end">
            <Button onClick={onClose}>إغلاق</Button>
            <Button type="primary" onClick={initializePayment} className="mr-2">
              إعادة المحاولة
            </Button>
          </div>
        </div>
      ) : (
        <div className="py-4">
          {paymentInitiated ? (
            <>
              <div className="mb-6">
                <Title level={5}>تفاصيل الاشتراك:</Title>
                <div className="flex justify-between mb-2">
                  <Text>الباقة:</Text>
                  <Text strong>{planName}</Text>
                </div>
                <div className="flex justify-between">
                  <Text>المبلغ:</Text>
                  <Text strong>{amount} ريال سعودي</Text>
                </div>
              </div>
              
              <div className="moyasar-form"></div>
              
              <div className="mt-6 text-center text-gray-500 text-sm">
                <p>جميع المعاملات آمنة ومشفرة</p>
                <p>لن يتم تخزين بيانات بطاقتك على خوادمنا</p>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-10">
              <Alert
                type="warning"
                message="لم يتم تهيئة نموذج الدفع"
                description="يرجى إعادة المحاولة مرة أخرى"
                showIcon
              />
              <Button type="primary" onClick={initializePayment} className="mt-4">
                إعادة المحاولة
              </Button>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};
```

#### 3. استخدام مكون الدفع في صفحة الاشتراكات

```tsx
// frontend/src/pages/subscription/SubscriptionPlans.tsx
import { useState } from 'react';
import { Card, Button, Tag, List, Typography } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { MoyasarCheckout } from '../../components/payment/MoyasarCheckout';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;

interface Plan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number | null;
  features: string[];
  blockedFeatures: string[];
}

export const SubscriptionPlans = () => {
  const { user } = useAuth();
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  
  // بيانات الباقات
  const plans: Plan[] = [
    {
      id: 'free',
      name: 'المجانية',
      monthlyPrice: 0,
      yearlyPrice: null,
      features: ['النماذج الأساسية', 'دعم المجتمع'],
      blockedFeatures: ['الصوت', 'رفع الملفات', 'النماذج المتقدمة']
    },
    {
      id: 'basic',
      name: 'الأساسية',
      monthlyPrice: 199,
      yearlyPrice: 1990,
      features: ['رفع ملفات (10MB)', 'نماذج متوسطة', 'النماذج الأساسية', 'دعم المجتمع'],
      blockedFeatures: ['النماذج القانونية', 'التحليل المتقدم']
    },
    {
      id: 'premium',
      name: 'المتقدمة',
      monthlyPrice: 499,
      yearlyPrice: 4990,
      features: ['الصوت', 'نماذج متقدمة', 'RAG', 'رفع ملفات (10MB)', 'نماذج متوسطة', 'النماذج الأساسية', 'دعم المجتمع'],
      blockedFeatures: ['النماذج المخصصة', 'دعم 24/7']
    },
    {
      id: 'enterprise',
      name: 'المؤسسية',
      monthlyPrice: 999,
      yearlyPrice: 9990,
      features: ['جميع المزايا', 'تخصيص النماذج', 'الصوت', 'نماذج متقدمة', 'RAG', 'رفع ملفات (10MB)', 'نماذج متوسطة', 'النماذج الأساسية', 'دعم المجتمع'],
      blockedFeatures: []
    }
  ];
  
  const handleSubscribe = (plan: Plan) => {
    setSelectedPlan(plan);
    setIsCheckoutOpen(true);
  };
  
  const handlePaymentSuccess = () => {
    setIsCheckoutOpen(false);
    // يمكن إضافة إجراءات إضافية هنا مثل تحديث حالة المستخدم أو عرض رسالة نجاح
  };
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-8">
        <Title level={2}>باقات الاشتراك</Title>
        <Text>اختر الباقة المناسبة لاحتياجاتك</Text>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.map(plan => (
          <Card
            key={plan.id}
            title={
              <div className="text-center">
                <Title level={3}>{plan.name}</Title>
                <div className="mt-2">
                  <Text className="text-3xl font-bold">
                    {plan.monthlyPrice === 0 ? 'مجاناً' : `${plan.monthlyPrice} ريال`}
                  </Text>
                  {plan.monthlyPrice > 0 && <Text className="text-gray-500">/شهرياً</Text>}
                </div>
              </div>
            }
            className="h-full flex flex-col"
          >
            <div className="flex-grow">
              <List
                itemLayout="horizontal"
                dataSource={plan.features}
                renderItem={feature => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<CheckOutlined className="text-green-500" />}
                      title={feature}
                    />
                  </List.Item>
                )}
              />
              
              {plan.blockedFeatures.length > 0 && (
                <>
                  <div className="my-2 border-t border-gray-200"></div>
                  <List
                    itemLayout="horizontal"
                    dataSource={plan.blockedFeatures}
                    renderItem={feature => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<CloseOutlined className="text-red-500" />}
                          title={<Text className="text-gray-400">{feature}</Text>}
                        />
                      </List.Item>
                    )}
                  />
                </>
              )}
            </div>
            
            <div className="mt-4">
              {plan.id === 'free' ? (
                <Button block disabled={user?.subscription?.plan === 'free'}>
                  {user?.subscription?.plan === 'free' ? 'الباقة الحالية' : 'اشترك مجاناً'}
                </Button>
              ) : (
                <Button
                  type="primary"
                  block
                  onClick={() => handleSubscribe(plan)}
                  disabled={user?.subscription?.plan === plan.id}
                >
                  {user?.subscription?.plan === plan.id ? 'الباقة الحالية' : 'اشترك الآن'}
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>
      
      {/* نافذة الدفع */}
      {selectedPlan && (
        <MoyasarCheckout
          planId={selectedPlan.id}
          planName={selectedPlan.name}
          amount={selectedPlan.monthlyPrice}
          isOpen={isCheckoutOpen}
          onClose={() => setIsCheckoutOpen(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
};
```

---

## 4. نقاط الربط في Backend (FastAPI)

### ما هو؟
نقاط نهاية API للتعامل مع عمليات الدفع والتحقق منها وتحديث حالة الاشتراك.

### لماذا؟
- التعامل الآمن مع بوابة الدفع باستخدام المفتاح السري
- معالجة الإشعارات من Webhook
- تحديث حالة الاشتراك في قاعدة البيانات

### التنفيذ

#### 1. إنشاء ملف `backend/app/api/routes/payments.py`

```python
from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks, status, Body
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import httpx
import json
import os

from app.db.session import get_db
from app.core.security import get_current_active_user
from app.models.user import User
from app.models.payment import Subscription, Payment, Invoice
from app.schemas.payment import PaymentInitiate, PaymentVerify, PaymentWebhook
from app.core.config import settings
from app.api.utils.email import send_invoice_email

router = APIRouter()

# مفاتيح Moyasar
MOYASAR_SECRET_KEY = os.getenv("MOYASAR_SECRET_KEY")
MOYASAR_PUBLISHABLE_KEY = os.getenv("MOYASAR_PUBLISHABLE_KEY")
MOYASAR_WEBHOOK_SECRET = os.getenv("MOYASAR_WEBHOOK_SECRET")

# عنوان API لـ Moyasar
MOYASAR_API_URL = "https://api.moyasar.com/v1"

@router.post("/initiate", status_code=status.HTTP_200_OK)
async def initiate_payment(
    data: PaymentInitiate = Body(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إنشاء جلسة دفع جديدة مع Moyasar
    """
    try:
        # التحقق من صحة الباقة
        plan_id = data.plan_id
        
        # في التطبيق الحقيقي، يجب التحقق من وجود الباقة في قاعدة البيانات
        # plan = db.query(Plan).filter(Plan.id == plan_id).first()
        # if not plan:
        #     raise HTTPException(status_code=404, detail="الباقة غير موجودة")
        
        # إنشاء بيانات الدفع
        payment_data = {
            "amount": data.amount,  # المبلغ بالهللة
            "currency": "SAR",
            "description": data.description or f"اشتراك في باقة {plan_id}",
            "callback_url": f"{settings.FRONTEND_URL}/payment-callback",
            "source": {
                "type": "creditcard",
                "3ds": True,
            },
            "metadata": {
                "user_id": str(current_user.id),
                "plan_id": plan_id
            }
        }
        
        # إرسال طلب إنشاء الدفع إلى Moyasar
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{MOYASAR_API_URL}/payments",
                json=payment_data,
                auth=(MOYASAR_SECRET_KEY, "")
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"خطأ في إنشاء الدفع: {response.text}"
                )
            
            payment_response = response.json()
        
        # إنشاء سجل دفع في قاعدة البيانات
        payment = Payment(
            id=payment_response["id"],
            user_id=current_user.id,
            amount=data.amount / 100,  # تحويل من هللة إلى ريال
            currency="SAR",
            status="pending",
            payment_method="creditcard",
            payment_id=payment_response["id"]
        )
        db.add(payment)
        db.commit()
        
        # إرجاع بيانات الدفع للواجهة
        return {
            "payment_id": payment_response["id"],
            "transaction_url": payment_response.get("source", {}).get("transaction_url", ""),
            "publishable_key": MOYASAR_PUBLISHABLE_KEY
        }
        
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"خطأ في الاتصال ببوابة الدفع: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"حدث خطأ أثناء إنشاء الدفع: {str(e)}"
        )

@router.post("/verify", status_code=status.HTTP_200_OK)
async def verify_payment(
    data: PaymentVerify = Body(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    التحقق من حالة الدفع
    """
    try:
        # التحقق من وجود الدفعة في قاعدة البيانات
        payment = db.query(Payment).filter(Payment.payment_id == data.payment_id).first()
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الدفعة غير موجودة"
            )
        
        # التحقق من أن الدفعة تخص المستخدم الحالي
        if payment.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="ليس لديك صلاحية للوصول إلى هذه الدفعة"
            )
        
        # الاستعلام عن حالة الدفع من Moyasar
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{MOYASAR_API_URL}/payments/{data.payment_id}",
                auth=(MOYASAR_SECRET_KEY, "")
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"خطأ في التحقق من الدفع: {response.text}"
                )
            
            payment_data = response.json()
        
        # تحديث حالة الدفع في قاعدة البيانات
        payment.status = payment_data["status"]
        db.commit()
        
        # إذا كانت حالة الدفع "paid"، قم بتحديث الاشتراك
        if payment_data["status"] == "paid":
            # استخراج معرف الباقة من البيانات الوصفية
            plan_id = payment_data.get("metadata", {}).get("plan_id")
            
            if plan_id:
                # التحقق من وجود اشتراك سابق
                subscription = db.query(Subscription).filter(
                    Subscription.user_id == current_user.id,
                    Subscription.status == "active"
                ).first()
                
                # حساب تاريخ انتهاء الاشتراك (شهر واحد من الآن)
                start_date = datetime.utcnow()
                end_date = start_date + timedelta(days=30)
                
                if subscription:
                    # تحديث الاشتراك الحالي
                    subscription.plan = plan_id
                    subscription.end_date = end_date
                    subscription.updated_at = datetime.utcnow()
                else:
                    # إنشاء اشتراك جديد
                    subscription = Subscription(
                        user_id=current_user.id,
                        plan=plan_id,
                        status="active",
                        start_date=start_date,
                        end_date=end_date,
                        auto_renew=True
                    )
                    db.add(subscription)
                
                # ربط الدفعة بالاشتراك
                payment.subscription_id = subscription.id
                
                # إنشاء فاتورة
                invoice_number = f"INV-{datetime.utcnow().strftime('%Y%m%d')}-{payment.id[:8]}"
                tax_amount = payment.amount * 0.15  # 15% VAT
                
                invoice = Invoice(
                    payment_id=payment.id,
                    invoice_number=invoice_number,
                    invoice_date=datetime.utcnow(),
                    due_date=datetime.utcnow(),  # مستحقة فوراً لأنها مدفوعة بالفعل
                    total_amount=payment.amount,
                    tax_amount=tax_amount,
                    status="paid"
                )
                db.add(invoice)
                
                db.commit()
        
        # إرجاع حالة الدفع
        return {
            "payment_id": data.payment_id,
            "status": payment_data["status"],
            "amount": payment_data["amount"] / 100,  # تحويل من هللة إلى ريال
            "message": "تم التحقق من حالة الدفع بنجاح"
        }
        
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"خطأ في الاتصال ببوابة الدفع: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"حدث خطأ أثناء التحقق من الدفع: {str(e)}"
        )

@router.post("/webhook/moyasar", status_code=status.HTTP_200_OK)
async def moyasar_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    معالجة إشعارات Webhook من Moyasar
    """
    try:
        # التحقق من توقيع Webhook
        webhook_token = request.headers.get("Moyasar-Token")
        if webhook_token != MOYASAR_WEBHOOK_SECRET:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="توقيع Webhook غير صالح"
            )
        
        # قراءة بيانات الإشعار
        payload = await request.json()
        
        # التحقق من نوع الإشعار
        event_type = payload.get("type")
        if event_type != "payment_paid":
            # يمكن معالجة أنواع أخرى من الإشعارات هنا
            return {"status": "success", "message": f"تم استلام الإشعار: {event_type}"}
        
        # الحصول على بيانات الدفع
        payment_data = payload.get("data", {})
        payment_id = payment_data.get("id")
        
        if not payment_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="معرف الدفع غير موجود في الإشعار"
            )
        
        # التحقق من وجود الدفعة في قاعدة البيانات
        payment = db.query(Payment).filter(Payment.payment_id == payment_id).first()
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الدفعة غير موجودة في قاعدة البيانات"
            )
        
        # تحديث حالة الدفع
        payment.status = payment_data.get("status", "pending")
        db.commit()
        
        # إذا كانت حالة الدفع "paid"، قم بتحديث الاشتراك
        if payment_data.get("status") == "paid":
            # استخراج معرف الباقة والمستخدم من البيانات الوصفية
            metadata = payment_data.get("metadata", {})
            plan_id = metadata.get("plan_id")
            user_id = metadata.get("user_id")
            
            if plan_id and user_id:
                # التحقق من وجود اشتراك سابق
                subscription = db.query(Subscription).filter(
                    Subscription.user_id == user_id,
                    Subscription.status == "active"
                ).first()
                
                # حساب تاريخ انتهاء الاشتراك (شهر واحد من الآن)
                start_date = datetime.utcnow()
                end_date = start_date + timedelta(days=30)
                
                if subscription:
                    # تحديث الاشتراك الحالي
                    subscription.plan = plan_id
                    subscription.end_date = end_date
                    subscription.updated_at = datetime.utcnow()
                else:
                    # إنشاء اشتراك جديد
                    subscription = Subscription(
                        user_id=user_id,
                        plan=plan_id,
                        status="active",
                        start_date=start_date,
                        end_date=end_date,
                        auto_renew=True
                    )
                    db.add(subscription)
                
                # ربط الدفعة بالاشتراك
                payment.subscription_id = subscription.id
                
                # إنشاء فاتورة
                invoice_number = f"INV-{datetime.utcnow().strftime('%Y%m%d')}-{payment.id[:8]}"
                tax_amount = payment.amount * 0.15  # 15% VAT
                
                invoice = Invoice(
                    payment_id=payment.id,
                    invoice_number=invoice_number,
                    invoice_date=datetime.utcnow(),
                    due_date=datetime.utcnow(),  # مستحقة فوراً لأنها مدفوعة بالفعل
                    total_amount=payment.amount,
                    tax_amount=tax_amount,
                    status="paid"
                )
                db.add(invoice)
                
                db.commit()
                
                # إرسال بريد إلكتروني بالفاتورة في الخلفية
                user = db.query(User).filter(User.id == user_id).first()
                if user:
                    background_tasks.add_task(
                        send_invoice_email,
                        email=user.email,
                        invoice_number=invoice_number,
                        amount=payment.amount,
                        plan=plan_id
                    )
        
        return {"status": "success", "message": "تم معالجة الإشعار بنجاح"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"حدث خطأ أثناء معالجة الإشعار: {str(e)}"
        )
```

#### 2. إنشاء ملف `backend/app/schemas/payment.py`

```python
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class PaymentInitiate(BaseModel):
    plan_id: str
    amount: int = Field(..., gt=0)  # المبلغ بالهللة
    description: Optional[str] = None

class PaymentVerify(BaseModel):
    payment_id: str

class PaymentWebhook(BaseModel):
    type: str
    data: Dict[str, Any]
```

#### 3. تحديث ملف `backend/app/main.py` لتضمين مسارات الدفع

```python
from app.api.routes import payments

# تضمين مسارات الدفع
app.include_router(payments.router, prefix="/api/payments", tags=["Payments"])
```

---

## 5. جداول Supabase وربطها بـ Refine.dev

### ما هو؟
هيكل قاعدة البيانات اللازم لتخزين بيانات الاشتراكات والمدفوعات والفواتير، وكيفية ربطها بلوحة الإدارة.

### لماذا؟
- تخزين بيانات الاشتراكات والمدفوعات بشكل منظم
- تطبيق سياسات أمان مستوى الصف (RLS)
- سهولة إدارة البيانات من خلال لوحة الإدارة

### التنفيذ

#### 1. إنشاء جداول Supabase

قم بإنشاء ملف هجرة جديد `supabase/migrations/payment_tables.sql`:

```sql
-- إنشاء جدول الباقات
CREATE TABLE IF NOT EXISTS plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  price_monthly FLOAT NOT NULL,
  price_yearly FLOAT,
  token_limit INTEGER NOT NULL,
  max_chats INTEGER NOT NULL,
  max_file_size INTEGER,
  features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- إنشاء جدول الاشتراكات
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id UUID NOT NULL REFERENCES plans(id),
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'expired', 'past_due')),
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- إنشاء جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
  amount FLOAT NOT NULL,
  currency TEXT NOT NULL DEFAULT 'SAR',
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method TEXT NOT NULL,
  payment_id TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- إنشاء جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  invoice_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  total_amount FLOAT NOT NULL,
  tax_amount FLOAT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'issued', 'paid', 'void')),
  notes TEXT,
  wafeq_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_plan_id ON subscriptions(plan_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);

CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);

CREATE INDEX IF NOT EXISTS idx_invoices_payment_id ON invoices(payment_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);

-- تمكين RLS
ALTER TABLE plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- سياسات RLS للباقات
CREATE POLICY "الجميع يمكنه عرض الباقات النشطة"
  ON plans
  FOR SELECT
  USING (is_active = true);

CREATE POLICY "المسؤولون يمكنهم إدارة الباقات"
  ON plans
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      JOIN profiles ON auth.users.id = profiles.id
      WHERE auth.users.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات RLS للاشتراكات
CREATE POLICY "المستخدمون يمكنهم عرض اشتراكاتهم"
  ON subscriptions
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "المسؤولون يمكنهم عرض جميع الاشتراكات"
  ON subscriptions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      JOIN profiles ON auth.users.id = profiles.id
      WHERE auth.users.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "المسؤولون يمكنهم إدارة الاشتراكات"
  ON subscriptions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      JOIN profiles ON auth.users.id = profiles.id
      WHERE auth.users.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات RLS للمدفوعات
CREATE POLICY "المستخدمون يمكنهم عرض مدفوعاتهم"
  ON payments
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "المسؤولون يمكنهم عرض جميع المدفوعات"
  ON payments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      JOIN profiles ON auth.users.id = profiles.id
      WHERE auth.users.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات RLS للفواتير
CREATE POLICY "المستخدمون يمكنهم عرض فواتيرهم"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM payments
      WHERE payments.id = invoices.payment_id
      AND payments.user_id = auth.uid()
    )
  );

CREATE POLICY "المسؤولون يمكنهم عرض جميع الفواتير"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      JOIN profiles ON auth.users.id = profiles.id
      WHERE auth.users.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- إدخال بيانات الباقات الافتراضية
INSERT INTO plans (name, description, price_monthly, price_yearly, token_limit, max_chats, max_file_size, features, is_active)
VALUES
  ('المجانية', 'باقة مجانية مع ميزات أساسية', 0, 0, 5000, 100, 0, '["النماذج الأساسية", "دعم المجتمع"]', true),
  ('الأساسية', 'باقة أساسية للاستخدام الشخصي', 199, 1990, 25000, 1000, 10, '["رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]', true),
  ('المتقدمة', 'باقة متقدمة للمحترفين', 499, 4990, 100000, 5000, 50, '["الصوت", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]', true),
  ('المؤسسية', 'باقة شاملة للمؤسسات', 999, 9990, 500000, -1, 500, '["جميع المزايا", "تخصيص النماذج", "الصوت", "نماذج متقدمة", "RAG", "رفع ملفات (10MB)", "نماذج متوسطة", "النماذج الأساسية", "دعم المجتمع"]', true)
ON CONFLICT DO NOTHING;
```

#### 2. تطبيق الهجرة على Supabase

```bash
supabase db push
```

#### 3. إنشاء موارد Refine.dev للباقات والاشتراكات

إنشاء ملف `src/pages/admin/plans/list.tsx`:

```tsx
import {
  List,
  Table,
  useTable,
  EditButton,
  ShowButton,
  DeleteButton,
  TagField,
  NumberField,
  BooleanField,
  DateField,
} from "@refinedev/antd";
import { Space, Tag } from "antd";

export const PlansList = () => {
  const { tableProps } = useTable({
    resource: "plans",
  });

  return (
    <List>
      <Table {...tableProps} rowKey="id">
        <Table.Column dataIndex="name" title="الاسم" />
        <Table.Column
          dataIndex="price_monthly"
          title="السعر الشهري"
          render={(value) => <NumberField value={value} options={{ style: 'currency', currency: 'SAR' }} />}
        />
        <Table.Column
          dataIndex="price_yearly"
          title="السعر السنوي"
          render={(value) => <NumberField value={value} options={{ style: 'currency', currency: 'SAR' }} />}
        />
        <Table.Column
          dataIndex="token_limit"
          title="حد التوكينز"
          render={(value) => <NumberField value={value} />}
        />
        <Table.Column
          dataIndex="max_chats"
          title="حد المحادثات"
          render={(value) => value === -1 ? <Tag>غير محدود</Tag> : <NumberField value={value} />}
        />
        <Table.Column
          dataIndex="is_active"
          title="الحالة"
          render={(value) => <BooleanField value={value} trueIcon={<Tag color="green">مفعلة</Tag>} falseIcon={<Tag color="red">معطلة</Tag>} />}
        />
        <Table.Column
          dataIndex="created_at"
          title="تاريخ الإنشاء"
          render={(value) => <DateField value={value} format="DD/MM/YYYY" />}
        />
        <Table.Column
          title="الإجراءات"
          dataIndex="actions"
          render={(_, record: any) => (
            <Space>
              <ShowButton hideText size="small" recordItemId={record.id} />
              <EditButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
```

إنشاء ملف `src/pages/admin/subscriptions/list.tsx`:

```tsx
import {
  List,
  Table,
  useTable,
  ShowButton,
  TagField,
  DateField,
} from "@refinedev/antd";
import { Space, Tag } from "antd";

export const SubscriptionsList = () => {
  const { tableProps } = useTable({
    resource: "subscriptions",
    meta: {
      select: "*, plans(name), profiles(email, full_name)",
    },
  });

  const getStatusTag = (status: string) => {
    switch (status) {
      case "active":
        return <Tag color="green">نشط</Tag>;
      case "canceled":
        return <Tag color="red">ملغي</Tag>;
      case "expired":
        return <Tag color="orange">منتهي</Tag>;
      case "past_due":
        return <Tag color="volcano">متأخر</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  return (
    <List>
      <Table {...tableProps} rowKey="id">
        <Table.Column
          dataIndex={["profiles", "full_name"]}
          title="المستخدم"
          render={(value, record: any) => (
            <div>
              <div>{value}</div>
              <div className="text-xs text-gray-500">{record.profiles?.email}</div>
            </div>
          )}
        />
        <Table.Column
          dataIndex={["plans", "name"]}
          title="الباقة"
        />
        <Table.Column
          dataIndex="status"
          title="الحالة"
          render={(value) => getStatusTag(value)}
        />
        <Table.Column
          dataIndex="start_date"
          title="تاريخ البدء"
          render={(value) => <DateField value={value} format="DD/MM/YYYY" />}
        />
        <Table.Column
          dataIndex="end_date"
          title="تاريخ الانتهاء"
          render={(value) => <DateField value={value} format="DD/MM/YYYY" />}
        />
        <Table.Column
          dataIndex="auto_renew"
          title="تجديد تلقائي"
          render={(value) => <TagField value={value ? "نعم" : "لا"} color={value ? "green" : "red"} />}
        />
        <Table.Column
          title="الإجراءات"
          dataIndex="actions"
          render={(_, record: any) => (
            <Space>
              <ShowButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
```

إنشاء ملف `src/pages/admin/payments/list.tsx`:

```tsx
import {
  List,
  Table,
  useTable,
  ShowButton,
  TagField,
  DateField,
  NumberField,
} from "@refinedev/antd";
import { Space, Tag } from "antd";

export const PaymentsList = () => {
  const { tableProps } = useTable({
    resource: "payments",
    meta: {
      select: "*, profiles(email, full_name)",
    },
  });

  const getStatusTag = (status: string) => {
    switch (status) {
      case "paid":
        return <Tag color="green">مدفوع</Tag>;
      case "pending":
        return <Tag color="orange">قيد الانتظار</Tag>;
      case "failed":
        return <Tag color="red">فشل</Tag>;
      case "refunded":
        return <Tag color="purple">مسترد</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  return (
    <List>
      <Table {...tableProps} rowKey="id">
        <Table.Column
          dataIndex={["profiles", "full_name"]}
          title="المستخدم"
          render={(value, record: any) => (
            <div>
              <div>{value}</div>
              <div className="text-xs text-gray-500">{record.profiles?.email}</div>
            </div>
          )}
        />
        <Table.Column
          dataIndex="amount"
          title="المبلغ"
          render={(value, record: any) => (
            <NumberField
              value={value}
              options={{ style: 'currency', currency: record.currency || 'SAR' }}
            />
          )}
        />
        <Table.Column
          dataIndex="status"
          title="الحالة"
          render={(value) => getStatusTag(value)}
        />
        <Table.Column
          dataIndex="payment_method"
          title="طريقة الدفع"
        />
        <Table.Column
          dataIndex="created_at"
          title="تاريخ الدفع"
          render={(value) => <DateField value={value} format="DD/MM/YYYY HH:mm" />}
        />
        <Table.Column
          title="الإجراءات"
          dataIndex="actions"
          render={(_, record: any) => (
            <Space>
              <ShowButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
```

#### 4. تحديث ملف `src/App.tsx` لإضافة موارد الدفع

```tsx
// في مصفوفة resources
{
  name: "plans",
  list: "/admin/plans",
  create: "/admin/plans/create",
  edit: "/admin/plans/edit/:id",
  show: "/admin/plans/show/:id",
  meta: {
    label: "الباقات",
    icon: "AppstoreOutlined",
  },
},
{
  name: "subscriptions",
  list: "/admin/subscriptions",
  show: "/admin/subscriptions/show/:id",
  meta: {
    label: "الاشتراكات",
    icon: "CrownOutlined",
  },
},
{
  name: "payments",
  list: "/admin/payments",
  show: "/admin/payments/show/:id",
  meta: {
    label: "المدفوعات",
    icon: "DollarOutlined",
  },
},
{
  name: "invoices",
  list: "/admin/invoices",
  show: "/admin/invoices/show/:id",
  meta: {
    label: "الفواتير",
    icon: "FileTextOutlined",
  },
},
```

---

## 6. الأمان

### ما هو؟
إجراءات وممارسات الأمان اللازمة لحماية بيانات الدفع والمعاملات المالية.

### لماذا؟
- حماية بيانات المستخدمين المالية
- الامتثال لمعايير أمان بيانات بطاقات الدفع (PCI-DSS)
- منع الاحتيال والوصول غير المصرح به

### التنفيذ

#### 1. عدم تخزين بيانات البطاقة

لا تقم أبدًا بتخزين بيانات بطاقات الدفع على خوادمك. تعتمد Moyasar على نموذج "Hosted Fields" حيث تتم معالجة بيانات البطاقة مباشرة على خوادمهم المتوافقة مع PCI-DSS.

#### 2. استخدام HTTPS

تأكد من أن جميع الاتصالات تتم عبر HTTPS لتشفير البيانات أثناء النقل.

```nginx
# في ملف تكوين Nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # إعدادات SSL الموصى بها
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # ...
}
```

#### 3. التحقق من توقيع Webhook

تحقق دائمًا من صحة توقيع Webhook للتأكد من أن الطلبات تأتي فعلاً من Moyasar.

```python
# في معالج Webhook
webhook_token = request.headers.get("Moyasar-Token")
if webhook_token != MOYASAR_WEBHOOK_SECRET:
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="توقيع Webhook غير صالح"
    )
```

#### 4. تطبيق سياسات أمان مستوى الصف (RLS)

تأكد من تطبيق سياسات RLS المناسبة في Supabase لضمان أن المستخدمين يمكنهم فقط الوصول إلى بياناتهم الخاصة.

```sql
-- مثال على سياسة RLS للمدفوعات
CREATE POLICY "المستخدمون يمكنهم عرض مدفوعاتهم فقط"
  ON payments
  FOR SELECT
  USING (user_id = auth.uid());
```

#### 5. تقييد معدل الطلبات

قم بتطبيق تقييد معدل الطلبات لمنع هجمات القوة الغاشمة على نقاط نهاية الدفع.

```python
# في FastAPI
from fastapi import Depends, HTTPException, status
from fastapi.security import APIKeyHeader
from starlette.requests import Request
import time
from typing import Dict

# قاموس لتخزين عدد الطلبات لكل IP
request_counts: Dict[str, Dict[str, int]] = {}

def rate_limit(
    request: Request,
    max_requests: int = 5,
    window_seconds: int = 60
):
    client_ip = request.client.host
    current_time = int(time.time())
    
    # تهيئة أو تحديث قاموس العدادات
    if client_ip not in request_counts:
        request_counts[client_ip] = {"count": 1, "window_start": current_time}
    else:
        # إعادة تعيين العداد إذا انتهت النافذة الزمنية
        if current_time - request_counts[client_ip]["window_start"] > window_seconds:
            request_counts[client_ip] = {"count": 1, "window_start": current_time}
        else:
            request_counts[client_ip]["count"] += 1
    
    # التحقق من تجاوز الحد
    if request_counts[client_ip]["count"] > max_requests:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="تم تجاوز الحد المسموح به من الطلبات. يرجى المحاولة مرة أخرى لاحقًا."
        )

# استخدام التابع في نقطة نهاية الدفع
@router.post("/initiate", status_code=status.HTTP_200_OK)
async def initiate_payment(
    data: PaymentInitiate = Body(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _: None = Depends(lambda request: rate_limit(request, max_requests=5, window_seconds=60))
):
    # ...
```

#### 6. تخزين المفاتيح السرية بأمان

تأكد من تخزين مفاتيح API السرية في متغيرات البيئة وليس في كود المصدر.

```python
# استخدام متغيرات البيئة
import os
from dotenv import load_dotenv

load_dotenv()

MOYASAR_SECRET_KEY = os.getenv("MOYASAR_SECRET_KEY")
MOYASAR_PUBLISHABLE_KEY = os.getenv("MOYASAR_PUBLISHABLE_KEY")
MOYASAR_WEBHOOK_SECRET = os.getenv("MOYASAR_WEBHOOK_SECRET")
```

---

## 7. تجربة المستخدم

### ما هو؟
تصميم تجربة مستخدم سلسة وبديهية لعملية الدفع والاشتراك.

### لماذا؟
- زيادة معدل التحويل
- تقليل معدل التخلي عن عملية الدفع
- تعزيز رضا المستخدم

### التنفيذ

#### 1. عرض باقات الاشتراك بوضوح

قم بتصميم صفحة باقات تعرض بوضوح:
- أسعار كل باقة
- الميزات المتاحة في كل باقة
- مقارنة واضحة بين الباقات

```tsx
// مثال على عرض الباقات
<div className="grid grid-cols-1 md:grid-cols-4 gap-6">
  {plans.map(plan => (
    <Card
      key={plan.id}
      title={plan.name}
      className={`h-full flex flex-col ${plan.id === 'premium' ? 'border-primary border-2' : ''}`}
    >
      <div className="text-center mb-4">
        <Text className="text-3xl font-bold">
          {plan.monthlyPrice === 0 ? 'مجاناً' : `${plan.monthlyPrice} ريال`}
        </Text>
        {plan.monthlyPrice > 0 && <Text className="text-gray-500">/شهرياً</Text>}
      </div>
      
      <List
        itemLayout="horizontal"
        dataSource={plan.features}
        renderItem={feature => (
          <List.Item>
            <List.Item.Meta
              avatar={<CheckOutlined className="text-green-500" />}
              title={feature}
            />
          </List.Item>
        )}
      />
      
      <div className="mt-auto pt-4">
        <Button
          type={plan.id === 'premium' ? 'primary' : 'default'}
          block
          onClick={() => handleSubscribe(plan)}
        >
          {plan.id === 'free' ? 'ابدأ مجاناً' : 'اشترك الآن'}
        </Button>
      </div>
    </Card>
  ))}
</div>
```

#### 2. تحديث حالة الاشتراك في الوقت الفعلي

استخدم Supabase Realtime للاستماع إلى تغييرات الاشتراك وتحديث واجهة المستخدم فورًا.

```tsx
// في مكون إدارة الاشتراك
import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export const SubscriptionStatus = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState(null);
  
  useEffect(() => {
    if (!user) return;
    
    // جلب بيانات الاشتراك الحالي
    const fetchSubscription = async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*, plans(*)')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();
      
      if (!error && data) {
        setSubscription(data);
      }
    };
    
    fetchSubscription();
    
    // الاستماع للتغييرات في الاشتراكات
    const subscription = supabase
      .channel('subscription-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'subscriptions',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          // تحديث حالة الاشتراك عند تغييرها
          fetchSubscription();
        }
      )
      .subscribe();
    
    return () => {
      subscription.unsubscribe();
    };
  }, [user]);
  
  if (!subscription) {
    return <div>لا يوجد اشتراك نشط</div>;
  }
  
  return (
    <div>
      <h3>اشتراكك الحالي: {subscription.plans.name}</h3>
      <p>ينتهي في: {new Date(subscription.end_date).toLocaleDateString('ar-SA')}</p>
      {/* عرض المزيد من التفاصيل */}
    </div>
  );
};
```

#### 3. إشعارات انتهاء الاشتراك

إنشاء وظيفة في Supabase Edge Functions لإرسال إشعارات قبل انتهاء الاشتراك.

```typescript
// supabase/functions/subscription-notifications/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // التعامل مع طلبات CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  
  try {
    // إنشاء عميل Supabase
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    // الحصول على الاشتراكات التي ستنتهي خلال 7 أيام
    const now = new Date();
    const sevenDaysLater = new Date();
    sevenDaysLater.setDate(now.getDate() + 7);
    
    const { data: expiringSubscriptions, error } = await supabaseClient
      .from('subscriptions')
      .select('*, profiles(email, full_name), plans(name)')
      .eq('status', 'active')
      .gte('end_date', now.toISOString())
      .lte('end_date', sevenDaysLater.toISOString());
    
    if (error) throw error;
    
    // إرسال إشعارات للمستخدمين
    for (const subscription of expiringSubscriptions) {
      // إضافة إشعار في جدول الإشعارات
      const { error: notificationError } = await supabaseClient
        .from('notifications')
        .insert({
          user_id: subscription.user_id,
          title: 'تنبيه انتهاء الاشتراك',
          message: `اشتراكك في باقة ${subscription.plans.name} سينتهي في ${new Date(subscription.end_date).toLocaleDateString('ar-SA')}`,
          type: 'warning',
          read: false,
        });
      
      if (notificationError) console.error('Error creating notification:', notificationError);
      
      // يمكن إضافة إرسال بريد إلكتروني هنا
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        processed: expiringSubscriptions.length,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});
```

#### 4. معالجة أخطاء الدفع بشكل مناسب

قم بعرض رسائل خطأ واضحة ومفيدة للمستخدم في حالة فشل الدفع.

```tsx
// في مكون الدفع
{error && (
  <Alert
    type="error"
    message="فشل في عملية الدفع"
    description={error}
    showIcon
    className="mb-4"
  />
)}
```

---

## 8. تسلسل التنفيذ (Step-by-Step)

### 1. إعداد Moyasar
- [ ] إنشاء حساب على Moyasar
- [ ] الحصول على مفاتيح API (العامة والسرية)
- [ ] إعداد Webhook وتوليد رمز سري

### 2. إعداد قاعدة البيانات
- [ ] إنشاء جداول الباقات والاشتراكات والمدفوعات والفواتير في Supabase
- [ ] تطبيق سياسات RLS المناسبة
- [ ] إدخال بيانات الباقات الافتراضية

### 3. تنفيذ Backend
- [ ] إنشاء نقاط نهاية API للدفع في FastAPI
- [ ] تنفيذ معالج Webhook
- [ ] إعداد وظيفة إرسال الفواتير بالبريد الإلكتروني

### 4. تنفيذ Frontend
- [ ] إضافة مكتبة Moyasar إلى المشروع
- [ ] إنشاء مكون الدفع
- [ ] تنفيذ صفحة عرض الباقات
- [ ] إنشاء مكون عرض حالة الاشتراك

### 5. تنفيذ لوحة الإدارة
- [ ] إنشاء موارد Refine.dev للباقات والاشتراكات والمدفوعات والفواتير
- [ ] تنفيذ صفحات القائمة والعرض والتعديل
- [ ] إنشاء لوحة تحكم لمراقبة المدفوعات والإيرادات

### 6. الاختبار
- [ ] اختبار عملية الدفع في بيئة Moyasar التجريبية
- [ ] اختبار معالجة Webhook
- [ ] اختبار تحديث حالة الاشتراك
- [ ] اختبار إرسال الفواتير

### 7. النشر
- [ ] تحديث متغيرات البيئة في بيئة الإنتاج
- [ ] التبديل إلى مفاتيح Moyasar للإنتاج
- [ ] تكوين Webhook للإنتاج
- [ ] مراقبة المدفوعات والأخطاء

---

## 9. الأسئلة المتكررة/الملاحظات النهائية

### هل يمكن استخدام بوابات دفع أخرى؟
نعم، يمكن تكييف هذا الدليل لاستخدام بوابات دفع أخرى مثل Tap أو STC Pay أو PayTabs. ستحتاج إلى تعديل كود التكامل وفقًا لمتطلبات كل بوابة.

### كيف يمكن اختبار الدفع دون استخدام بطاقة حقيقية؟
توفر Moyasar بيئة اختبار (Sandbox) يمكنك استخدامها مع بطاقات اختبار. على سبيل المثال:
- رقم البطاقة: `4111111111111111`
- تاريخ الانتهاء: أي تاريخ مستقبلي
- CVV: أي 3 أرقام
- اسم حامل البطاقة: أي اسم

### كيف يمكن التعامل مع الاشتراكات المتكررة؟
يمكن تنفيذ الاشتراكات المتكررة باستخدام وظيفة مجدولة تتحقق من الاشتراكات التي ستنتهي قريبًا وتقوم بإنشاء دفعة جديدة تلقائيًا إذا كان خيار التجديد التلقائي مفعلاً.

### ماذا يحدث إذا فشلت عملية الدفع؟
في حالة فشل الدفع، سيتم إرسال إشعار Webhook بحالة `payment_failed`. يمكنك معالجة هذا الإشعار وتحديث حالة الدفع في قاعدة البيانات وإشعار المستخدم.

### كيف يمكن إصدار استرداد للمدفوعات؟
يمكن إصدار استرداد من خلال لوحة تحكم Moyasar أو عبر واجهة برمجة التطبيقات. ستحتاج إلى تنفيذ نقطة نهاية API خاصة بالاسترداد في Backend وتحديث حالة الدفع والاشتراك وفقًا لذلك.

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **للاستفسارات التقنية: [<EMAIL>](mailto:<EMAIL>)**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
</div>
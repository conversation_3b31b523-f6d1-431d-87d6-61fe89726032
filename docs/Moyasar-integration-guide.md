# دليل تكامل Moyasar مع منصة شُريح

## 1. المعمارية الكاملة لتكامل الدفع

### 1.1 مخطط تدفق عملية الدفع

```mermaid
sequenceDiagram
    participant User as المستخدم
    participant UI as واجهة المستخدم
    participant API as FastAPI Backend
    participant <PERSON><PERSON><PERSON> as بوابة Moyasar
    participant Supabase as قاعدة البيانات
    participant Admin as لوحة المسؤول

    User->>UI: اختيار باقة الاشتراك
    UI->>API: طلب إنشاء عملية دفع (POST /payments/initiate)
    API->>Moyasar: إنشاء عملية دفع (Secret Key)
    Moyasar-->>API: بيانات عملية الدفع + رابط الدفع
    API-->>UI: معرف عملية الدفع + رابط الدفع
    UI->>User: عرض نموذج الدفع
    User->>UI: إدخال بيانات البطاقة
    UI->>Moyasar: إرسال بيانات البطاقة (Publishable Key)
    Moyasar-->>User: تأكيد الدفع (3D Secure إذا لزم الأمر)
    Moyasar-->>API: إشعار Webhook بنجاح/فشل الدفع
    API->>Supabase: تحديث حالة الاشتراك
    API->>User: إشعار بنجاح/فشل الدفع
    Supabase-->>Admin: تحديث حالة الاشتراك في لوحة المسؤول
```

### 1.2 مكونات النظام المتكاملة

| المكون | التقنية | دوره في عملية الدفع |
|--------|---------|---------------------|
| واجهة المستخدم | React + Vite + Tailwind | عرض باقات الاشتراك، تقديم نموذج الدفع، إرسال بيانات الدفع |
| لوحة المسؤول | Refine.dev | إدارة الباقات، مراقبة المدفوعات، إصدار الفواتير |
| الواجهة الخلفية | FastAPI | إنشاء عمليات الدفع، معالجة Webhook، تحديث الاشتراكات |
| قاعدة البيانات | Supabase (PostgreSQL) | تخزين بيانات الاشتراكات والمدفوعات والفواتير |
| بوابة الدفع | Moyasar | معالجة عمليات الدفع، تأمين بيانات البطاقات |

## 2. إعداد حساب Moyasar وربط Webhook

### 2.1 إنشاء حساب Moyasar

1. قم بزيارة [موقع Moyasar](https://moyasar.com/ar) وإنشاء حساب جديد
2. أكمل عملية التسجيل وتفعيل الحساب
3. انتقل إلى لوحة التحكم الخاصة بك

### 2.2 الحصول على مفاتيح API

1. في لوحة تحكم Moyasar، انتقل إلى قسم "الإعدادات" > "مفاتيح API"
2. ستجد نوعين من المفاتيح:
   - **مفتاح الاختبار (Test)**: يبدأ بـ `sk_test_` و `pk_test_` للاختبار
   - **مفتاح الإنتاج (Live)**: يبدأ بـ `sk_live_` و `pk_live_` للإنتاج
3. انسخ المفاتيح واحتفظ بها في مكان آمن

### 2.3 إعداد Webhook

1. في لوحة تحكم Moyasar، انتقل إلى قسم "الإعدادات" > "Webhooks"
2. انقر على "إضافة Webhook جديد"
3. أدخل عنوان URL للـ Webhook: `https://api.yourdomain.com/api/v1/payments/webhook`
4. اختر الأحداث التي تريد الاستماع إليها (على الأقل: `payment_paid`, `payment_failed`)
5. انسخ "رمز التحقق" (Verification Token) واحتفظ به في مكان آمن

### 2.4 إضافة المفاتيح إلى ملف .env

أضف المفاتيح التالية إلى ملف `.env` في مشروع Backend:

```
# Moyasar API Keys
MOYASAR_API_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxx
MOYASAR_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxxxxx
MOYASAR_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxx
```

## 3. تكامل واجهة الدفع في Frontend

### 3.1 الملفات المتأثرة في المشروع

- `frontend/src/components/subscription/PaymentModal.tsx` - مكون نافذة الدفع
- `frontend/src/pages/subscription-packages.tsx` - صفحة باقات الاشتراك
- `frontend/src/services/payment-service.ts` - خدمة معالجة الدفع
- `frontend/src/types/payment.ts` - أنواع البيانات المتعلقة بالدفع
- `frontend/.env` - متغيرات البيئة للواجهة الأمامية

### 3.2 متغيرات البيئة المطلوبة

أضف المتغيرات التالية إلى ملف `.env` في مشروع Frontend:

```
VITE_MOYASAR_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxxxxx
```

### 3.3 نقاط التكامل مع API

- `POST /api/v1/payments/initiate` - إنشاء عملية دفع جديدة
- `GET /api/v1/payments/status/:id` - التحقق من حالة عملية دفع
- `GET /api/v1/payments/history` - الحصول على سجل المدفوعات

### 3.4 المكونات المتأثرة

- `SubscriptionCard` - عرض تفاصيل الباقة مع زر الاشتراك
- `PaymentModal` - نافذة منبثقة تعرض نموذج الدفع
- `PaymentStatus` - عرض حالة عملية الدفع
- `PaymentHistory` - عرض سجل المدفوعات السابقة

## 4. نقاط الـ Backend (FastAPI) الخاصة بالدفع

### 4.1 إنشاء عملية دفع

```python
# app/api/routes/payments.py
from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
import requests
import json
import uuid
from datetime import datetime, timedelta

from app.core.config import settings
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.models.payment import Subscription, Payment, Invoice
from app.schemas.payment import PaymentInitiate, PaymentResponse, PaymentWebhook
from app.api.utils.email import send_invoice_email

router = APIRouter()

@router.post("/initiate", response_model=PaymentResponse)
async def initiate_payment(
    payment_data: PaymentInitiate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    إنشاء عملية دفع جديدة باستخدام Moyasar
    """
    # التحقق من صحة الباقة
    valid_plans = ["basic", "premium", "enterprise"]
    if payment_data.plan not in valid_plans:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"باقة غير صالحة. يجب أن تكون إحدى: {', '.join(valid_plans)}",
        )
    
    # الحصول على تفاصيل الباقة
    plan_details = {
        "basic": {"amount": 199, "duration_months": 1},
        "premium": {"amount": 499, "duration_months": 1},
        "enterprise": {"amount": 999, "duration_months": 1}
    }
    
    # تحويل المبلغ إلى هللات (Moyasar يستخدم أصغر وحدة عملة)
    amount_halalas = int(plan_details[payment_data.plan]["amount"] * 100)
    
    # إنشاء سجل دفع في قاعدة البيانات
    payment_id = str(uuid.uuid4())
    db_payment = Payment(
        id=payment_id,
        user_id=current_user.id,
        amount=plan_details[payment_data.plan]["amount"],
        currency="SAR",
        status="pending",
        payment_method=payment_data.payment_method,
        payment_id=f"moyasar_{payment_id}"
    )
    db.add(db_payment)
    db.commit()
    
    # إنشاء عملية دفع في Moyasar
    try:
        moyasar_url = "https://api.moyasar.com/v1/payments"
        callback_url = payment_data.callback_url or f"{settings.API_V1_STR}/payments/callback"
        
        payload = {
            "amount": amount_halalas,
            "currency": "SAR",
            "description": f"اشتراك في باقة {payment_data.plan}",
            "callback_url": callback_url,
            "source": {
                "type": payment_data.payment_method,
            },
            "metadata": {
                "user_id": str(current_user.id),
                "plan": payment_data.plan
            }
        }
        
        headers = {
            "Authorization": f"Basic {settings.MOYASAR_API_KEY_BASE64}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(moyasar_url, json=payload, headers=headers)
        response.raise_for_status()
        moyasar_response = response.json()
        
        # تحديث سجل الدفع بمعرف Moyasar
        db_payment.payment_id = moyasar_response["id"]
        db.commit()
        
        return {
            "payment_id": payment_id,
            "moyasar_id": moyasar_response["id"],
            "amount": plan_details[payment_data.plan]["amount"],
            "currency": "SAR",
            "transaction_url": moyasar_response["source"]["transaction_url"],
            "callback_url": callback_url
        }
        
    except requests.exceptions.RequestException as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في الاتصال ببوابة الدفع: {str(e)}"
        )
```

### 4.2 معالجة Webhook

```python
@router.post("/webhook", status_code=status.HTTP_200_OK)
async def payment_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    معالجة إشعارات Webhook من Moyasar
    """
    # التحقق من صحة الإشعار
    webhook_secret = settings.MOYASAR_WEBHOOK_SECRET
    webhook_signature = request.headers.get("Moyasar-Signature")
    
    if not webhook_signature:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="توقيع Webhook مفقود"
        )
    
    # قراءة محتوى الطلب
    body = await request.body()
    body_str = body.decode("utf-8")
    
    # التحقق من التوقيع
    import hmac
    import hashlib
    
    computed_signature = hmac.new(
        webhook_secret.encode(),
        body_str.encode(),
        hashlib.sha256
    ).hexdigest()
    
    if computed_signature != webhook_signature:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="توقيع Webhook غير صالح"
        )
    
    # معالجة الإشعار
    webhook_data = json.loads(body_str)
    
    # البحث عن الدفعة في قاعدة البيانات
    moyasar_id = webhook_data["id"]
    payment = db.query(Payment).filter(Payment.payment_id == moyasar_id).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الدفعة غير موجودة"
        )
    
    # تحديث حالة الدفع
    payment.status = webhook_data["status"]
    db.commit()
    
    # إذا كانت الدفعة ناجحة، قم بإنشاء أو تحديث الاشتراك
    if webhook_data["status"] == "paid":
        # الحصول على المستخدم
        user = db.query(User).filter(User.id == payment.user_id).first()
        
        # الحصول على الباقة من البيانات الوصفية
        plan = webhook_data["metadata"]["plan"]
        
        # حساب مدة الاشتراك
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=30)  # اشتراك لمدة 30 يوم
        
        # البحث عن اشتراك نشط
        existing_subscription = db.query(Subscription).filter(
            Subscription.user_id == user.id,
            Subscription.status == "active"
        ).first()
        
        if existing_subscription:
            # تحديث الاشتراك الحالي
            existing_subscription.plan = plan
            existing_subscription.end_date = end_date
            existing_subscription.updated_at = datetime.utcnow()
            subscription_id = existing_subscription.id
            db.commit()
        else:
            # إنشاء اشتراك جديد
            subscription_id = str(uuid.uuid4())
            db_subscription = Subscription(
                id=subscription_id,
                user_id=user.id,
                plan=plan,
                status="active",
                start_date=start_date,
                end_date=end_date,
                auto_renew=True
            )
            db.add(db_subscription)
            db.commit()
        
        # تحديث الدفعة بمعرف الاشتراك
        payment.subscription_id = subscription_id
        db.commit()
        
        # إنشاء فاتورة
        invoice_number = f"INV-{datetime.utcnow().strftime('%Y%m%d')}-{payment.id[:8]}"
        tax_amount = payment.amount * 0.15  # 15% ضريبة القيمة المضافة
        
        db_invoice = Invoice(
            id=str(uuid.uuid4()),
            payment_id=payment.id,
            invoice_number=invoice_number,
            invoice_date=datetime.utcnow(),
            due_date=datetime.utcnow(),  # مستحقة فوراً لأنها مدفوعة بالفعل
            total_amount=payment.amount,
            tax_amount=tax_amount,
            status="paid"
        )
        db.add(db_invoice)
        db.commit()
        
        # إرسال بريد إلكتروني بالفاتورة في الخلفية
        background_tasks.add_task(
            send_invoice_email,
            email=user.email,
            invoice_number=invoice_number,
            amount=payment.amount,
            plan=plan
        )
        
        # تحديث دور المستخدم بناءً على الباقة
        if plan == "basic":
            user.role = "subscriber_basic"
        elif plan == "premium":
            user.role = "subscriber_premium"
        elif plan == "enterprise":
            user.role = "subscriber_enterprise"
        
        db.commit()
    
    return {"detail": "تمت معالجة Webhook بنجاح"}
```

### 4.3 التحقق من حالة الدفع

```python
@router.get("/status/{payment_id}", response_model=Dict[str, Any])
async def check_payment_status(
    payment_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    التحقق من حالة عملية دفع
    """
    # البحث عن الدفعة في قاعدة البيانات
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الدفعة غير موجودة"
        )
    
    # إذا كانت الدفعة معلقة، تحقق من حالتها في Moyasar
    if payment.status == "pending":
        try:
            moyasar_url = f"https://api.moyasar.com/v1/payments/{payment.payment_id}"
            headers = {
                "Authorization": f"Basic {settings.MOYASAR_API_KEY_BASE64}",
            }
            
            response = requests.get(moyasar_url, headers=headers)
            response.raise_for_status()
            moyasar_response = response.json()
            
            # تحديث حالة الدفع في قاعدة البيانات
            payment.status = moyasar_response["status"]
            db.commit()
            
            return {
                "payment_id": payment.id,
                "moyasar_id": payment.payment_id,
                "status": payment.status,
                "amount": payment.amount,
                "currency": payment.currency,
                "created_at": payment.created_at.isoformat()
            }
            
        except requests.exceptions.RequestException as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"خطأ في الاتصال ببوابة الدفع: {str(e)}"
            )
    
    return {
        "payment_id": payment.id,
        "moyasar_id": payment.payment_id,
        "status": payment.status,
        "amount": payment.amount,
        "currency": payment.currency,
        "created_at": payment.created_at.isoformat()
    }
```

## 5. جداول Supabase وربطها مع Admin Dashboard

### 5.1 هيكل جداول الدفع

```sql
-- جدول باقات الاشتراك
CREATE TABLE subscription_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  monthly_price FLOAT NOT NULL,
  yearly_price FLOAT,
  max_chats_per_month INTEGER NOT NULL,
  max_tokens_per_month INTEGER NOT NULL,
  features JSONB DEFAULT '[]'::jsonb,
  blocked_features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  is_default BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 1,
  color TEXT DEFAULT 'blue',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الاشتراكات
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  package_id UUID NOT NULL REFERENCES subscription_packages(id),
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'expired', 'past_due')),
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول المدفوعات
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
  amount FLOAT NOT NULL,
  currency TEXT NOT NULL DEFAULT 'SAR',
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method TEXT NOT NULL,
  payment_id TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- جدول الفواتير
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  invoice_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  total_amount FLOAT NOT NULL,
  tax_amount FLOAT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'issued', 'paid', 'void')),
  notes TEXT,
  wafeq_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

### 5.2 سياسات أمان مستوى الصف (RLS)

```sql
-- سياسات جدول باقات الاشتراك
ALTER TABLE subscription_packages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view active subscription packages"
  ON subscription_packages
  FOR SELECT
  USING (is_active = TRUE);

CREATE POLICY "Administrators can manage subscription packages"
  ON subscription_packages
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول الاشتراكات
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own subscriptions"
  ON subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all subscriptions"
  ON subscriptions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

CREATE POLICY "Administrators can manage subscriptions"
  ON subscriptions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول المدفوعات
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own payments"
  ON payments
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Administrators can view all payments"
  ON payments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );

-- سياسات جدول الفواتير
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view invoices for their payments"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM payments
      WHERE payments.id = invoices.payment_id
      AND payments.user_id = auth.uid()
    )
  );

CREATE POLICY "Administrators can view all invoices"
  ON invoices
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'administrator'
    )
  );
```

## 6. الأمان والامتثال

### 6.1 تأمين بيانات الدفع

1. **عدم تخزين بيانات البطاقة**: جميع بيانات البطاقات تتم معالجتها مباشرة بواسطة Moyasar ولا يتم تخزينها في خوادمنا.
2. **تشفير TLS**: جميع الاتصالات مع Moyasar تتم عبر HTTPS مع تشفير TLS 1.2+.
3. **التحقق من توقيع Webhook**: التحقق من صحة إشعارات Webhook باستخدام توقيع HMAC-SHA256.
4. **تخزين آمن للمفاتيح**: تخزين مفاتيح API في متغيرات بيئة آمنة وليس في الكود المصدري.

### 6.2 سياسات أمان مستوى الصف (RLS)

تطبيق سياسات RLS في Supabase لضمان:
- المستخدمون يمكنهم فقط رؤية مدفوعاتهم واشتراكاتهم الخاصة
- المسؤولون فقط يمكنهم إدارة باقات الاشتراك
- عزل بيانات كل مستخدم عن الآخرين

### 6.3 التحقق من الهوية والتفويض

```python
# app/core/security.py
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import get_db
from app.models.user import User

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

async def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    """
    التحقق من المستخدم الحالي من خلال الرمز
    """
    # تنفيذ التحقق من المستخدم
    # ...

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    التحقق من أن المستخدم نشط
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المستخدم غير نشط",
        )
    return current_user

def validate_admin_access(current_user: User = Depends(get_current_active_user)) -> User:
    """
    التحقق من أن المستخدم لديه صلاحيات المسؤول
    """
    if current_user.role != "administrator":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحيات كافية",
        )
    return current_user
```

## 7. تجربة المستخدم (UX/UI)

### 7.1 عرض باقات الاشتراك

- عرض باقات الاشتراك بشكل واضح مع مقارنة بين الميزات
- تمييز الباقة الحالية للمستخدم
- عرض زر "ترقية" أو "تغيير الباقة" حسب الحالة

### 7.2 عملية الدفع

- عرض نافذة منبثقة لنموذج الدفع
- عرض المبلغ والضريبة بوضوح
- توفير خيارات الدفع المختلفة (بطاقة ائتمان، مدى، Apple Pay)
- عرض شريط تقدم أثناء معالجة الدفع

### 7.3 إشعارات الاشتراك

- إشعار المستخدم عند نجاح الاشتراك
- إشعار المستخدم قبل انتهاء الاشتراك
- إشعار المستخدم عند فشل تجديد الاشتراك التلقائي

### 7.4 إدارة الاشتراك

- السماح للمستخدم بعرض تفاصيل اشتراكه الحالي
- توفير خيارات لتغيير الباقة أو إلغاء الاشتراك
- عرض سجل المدفوعات والفواتير

## 8. خطوات التنفيذ السريعة

### 8.1 إعداد البيئة

1. إنشاء حساب Moyasar والحصول على مفاتيح API
2. إضافة المفاتيح إلى ملفات `.env` في المشروع
3. إنشاء جداول الدفع في Supabase

### 8.2 تنفيذ Backend

1. إنشاء نقاط API للدفع في FastAPI
2. تنفيذ معالجة Webhook
3. ربط الدفع بنظام الاشتراكات

### 8.3 تنفيذ Frontend

1. إنشاء صفحة باقات الاشتراك
2. تنفيذ نافذة الدفع
3. ربط واجهة المستخدم بـ API

### 8.4 الاختبار والنشر

1. اختبار عملية الدفع في بيئة الاختبار
2. التحقق من معالجة Webhook بشكل صحيح
3. التحقق من تحديث الاشتراكات بشكل صحيح
4. نشر التغييرات إلى بيئة الإنتاج

## 9. استكشاف الأخطاء وإصلاحها

### 9.1 مشاكل شائعة وحلولها

| المشكلة | السبب المحتمل | الحل |
|---------|---------------|------|
| فشل إنشاء عملية دفع | مفتاح API غير صحيح | التحقق من مفتاح API في ملف `.env` |
| عدم استلام إشعارات Webhook | عنوان URL غير صحيح أو غير متاح | التحقق من إعدادات Webhook في لوحة تحكم Moyasar |
| فشل عملية الدفع | بطاقة غير صالحة أو رصيد غير كافٍ | عرض رسالة خطأ واضحة للمستخدم |
| عدم تحديث الاشتراك بعد الدفع | خطأ في معالجة Webhook | التحقق من سجلات الخطأ ومعالجة Webhook |
| عدم ظهور نموذج الدفع | خطأ في تضمين مكتبة Moyasar | التحقق من تضمين المكتبة في `index.html` |

### 9.2 سجلات وتتبع الأخطاء

```python
# app/api/routes/payments.py
import logging

logger = logging.getLogger(__name__)

@router.post("/webhook", status_code=status.HTTP_200_OK)
async def payment_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    try:
        # معالجة Webhook
        # ...
        
        logger.info(f"تمت معالجة Webhook بنجاح: {moyasar_id}")
        return {"detail": "تمت معالجة Webhook بنجاح"}
    except Exception as e:
        logger.error(f"خطأ في معالجة Webhook: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في معالجة Webhook: {str(e)}"
        )
```

## 10. الخلاصة

تكامل Moyasar مع منصة شُريح يوفر حلاً آمناً وسلساً لمعالجة المدفوعات وإدارة الاشتراكات. من خلال اتباع الخطوات المذكورة في هذا الدليل، يمكنك تنفيذ نظام دفع متكامل يتيح للمستخدمين الاشتراك في الباقات المختلفة وإدارة اشتراكاتهم بسهولة.

المميزات الرئيسية لهذا التكامل:
- معالجة آمنة لبيانات البطاقات وفقاً لمعايير PCI-DSS
- تحديث تلقائي للاشتراكات عند نجاح الدفع
- إدارة شاملة للاشتراكات والمدفوعات من خلال لوحة المسؤول
- تجربة مستخدم سلسة مع إشعارات واضحة

من خلال هذا التكامل، ستتمكن منصة شُريح من تحقيق إيرادات من خلال نموذج الاشتراكات، مع توفير تجربة دفع سهلة وآمنة للمستخدمين.
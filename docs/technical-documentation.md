# شُريح - توثيق فني شامل للوحة تحكم الشات بوت الذكي

## جدول المحتويات

1. [نظرة عامة على المشروع](#نظرة-عامة-على-المشروع)
2. [البنية المعمارية](#البنية-المعمارية)
   - [مخطط المعمارية العامة](#مخطط-المعمارية-العامة)
   - [تدفقات البيانات الرئيسية](#تدفقات-البيانات-الرئيسية)
   - [مكونات النظام](#مكونات-النظام)
3. [واجهة المستخدم الأمامية](#واجهة-المستخدم-الأمامية)
   - [هيكل المكونات](#هيكل-المكونات)
   - [نظام التنقل](#نظام-التنقل)
   - [نظام السمات والألوان](#نظام-السمات-والألوان)
   - [الصفحات الرئيسية](#الصفحات-الرئيسية)
4. [واجهات برمجة التطبيقات (API)](#واجهات-برمجة-التطبيقات-api)
   - [نقاط النهاية الرئيسية](#نقاط-النهاية-الرئيسية)
   - [أمثلة الطلبات والاستجابات](#أمثلة-الطلبات-والاستجابات)
   - [متطلبات المصادقة](#متطلبات-المصادقة)
   - [معالجة الأخطاء](#معالجة-الأخطاء)
5. [هيكل قاعدة البيانات](#هيكل-قاعدة-البيانات)
   - [مخطط العلاقات (ERD)](#مخطط-العلاقات-erd)
   - [جداول قاعدة البيانات](#جداول-قاعدة-البيانات)
   - [العلاقات والقيود](#العلاقات-والقيود)
   - [استعلامات نموذجية](#استعلامات-نموذجية)
6. [نظام المصادقة والتفويض](#نظام-المصادقة-والتفويض)
   - [تدفق المصادقة](#تدفق-المصادقة)
   - [إدارة الجلسات](#إدارة-الجلسات)
   - [نظام الأدوار والصلاحيات](#نظام-الأدوار-والصلاحيات)
   - [سياسات الأمان](#سياسات-الأمان)
7. [تكامل الذكاء الاصطناعي](#تكامل-الذكاء-الاصطناعي)
   - [النماذج المستخدمة](#النماذج-المستخدمة)
   - [إعدادات النماذج](#إعدادات-النماذج)
   - [معالجة الطلبات](#معالجة-الطلبات)
   - [تحسين الأداء](#تحسين-الأداء)
8. [نظام البحث المتجهي (RAG)](#نظام-البحث-المتجهي-rag)
   - [معمارية RAG](#معمارية-rag)
   - [تكوين Qdrant](#تكوين-qdrant)
   - [عمليات الفهرسة والبحث](#عمليات-الفهرسة-والبحث)
   - [تحسين الأداء](#تحسين-الأداء-1)
9. [نظام إدارة الاشتراكات](#نظام-إدارة-الاشتراكات)
   - [هيكل الباقات](#هيكل-الباقات)
   - [آلية التحكم في الميزات](#آلية-التحكم-في-الميزات)
   - [معالجة المدفوعات](#معالجة-المدفوعات)
   - [إدارة دورة حياة الاشتراك](#إدارة-دورة-حياة-الاشتراك)
10. [نظام مراقبة المحتوى](#نظام-مراقبة-المحتوى)
    - [تدفق عمل المراجعة](#تدفق-عمل-المراجعة)
    - [معالجة التقارير](#معالجة-التقارير)
    - [إجراءات المراجعة](#إجراءات-المراجعة)
11. [المراقبة والتحليلات](#المراقبة-والتحليلات)
    - [مقاييس الأداء](#مقاييس-الأداء)
    - [تتبع الأخطاء](#تتبع-الأخطاء)
    - [تحليلات المستخدم](#تحليلات-المستخدم)
12. [الأمان والامتثال](#الأمان-والامتثال)
    - [تشفير البيانات](#تشفير-البيانات)
    - [حماية API](#حماية-api)
    - [سجلات التدقيق](#سجلات-التدقيق)
    - [الامتثال للوائح](#الامتثال-للوائح)
13. [النشر والتشغيل](#النشر-والتشغيل)
    - [متطلبات النظام](#متطلبات-النظام)
    - [إعداد البيئة](#إعداد-البيئة)
    - [عملية النشر](#عملية-النشر)
    - [المراقبة والصيانة](#المراقبة-والصيانة)
14. [تطوير وتوسيع النظام](#تطوير-وتوسيع-النظام)
    - [إضافة ميزات جديدة](#إضافة-ميزات-جديدة)
    - [تخصيص المكونات](#تخصيص-المكونات)
    - [تحديث النماذج](#تحديث-النماذج)
15. [الملحقات](#الملحقات)
    - [مسرد المصطلحات](#مسرد-المصطلحات)
    - [المراجع](#المراجع)
    - [سجل التغييرات](#سجل-التغييرات)

## نظرة عامة على المشروع

**شُريح** هو نظام إدارة متكامل للشات بوت الذكي المتخصص في المجال القانوني. يوفر النظام لوحة تحكم إدارية شاملة تتيح للمسؤولين والمشرفين إدارة المستخدمين، المحادثات، الاستشهادات القانونية، مراقبة المحتوى، وإدارة نظام RAG والاشتراكات.

### الأهداف الرئيسية

- توفير واجهة إدارية متكاملة وسهلة الاستخدام لإدارة الشات بوت الذكي
- دعم النماذج المفتوحة المصدر المحلية مثل Llama 3.2 وQwen 2.5
- تمكين نظام RAG باستخدام Qdrant وLangChain لاسترجاع المعرفة مع الاستشهادات
- إدارة الاشتراكات بمستويات مختلفة وخصائص تفصيلية لكل مستوى
- تطبيق مراقبة محتوى متقدمة تُدخل المراجعين في الحلقة عند ضغط المستخدم على "عدم الإعجاب"
- توفير هوية بصرية قابلة للتخصيص بالكامل

### التقنيات الرئيسية

- **الواجهة الأمامية**: React 18.3.1، TypeScript 5.5.3، Refine.dev 4.47.0، Ant Design 5.14.1، Tailwind CSS 3.4.1
- **الخادم الخلفي**: NestJS، Prisma ORM
- **قاعدة البيانات**: Supabase (PostgreSQL)
- **نماذج الذكاء الاصطناعي**: Llama 3.2، Qwen 2.5، Deepseek-R1، Mistral 7B
- **البحث المتجهي**: Qdrant، LangChain
- **المصادقة**: JWT، Supabase Auth
- **التخزين المؤقت**: Redis
- **المراقبة**: Prometheus، Grafana

## البنية المعمارية

### مخطط المعمارية العامة

يتبع النظام نمط معمارية الطبقات مع فصل واضح بين مكونات الواجهة الأمامية والخدمات الخلفية وطبقة البيانات:

```
┌─────────────────────────────────────────────────────────────────┐
│                      واجهة المستخدم الأمامية                    │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ لوحة التحكم │  │ إدارة       │  │ إدارة       │  │ إعدادات │ │
│  │ الرئيسية   │  │ المستخدمين  │  │ المحادثات  │  │ النظام  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ مراقبة      │  │ إدارة       │  │ إدارة       │  │ إعدادات │ │
│  │ المحتوى    │  │ RAG         │  │ الاشتراكات  │  │ الذكاء  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
                            ▲
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                       طبقة الخدمات الخلفية                      │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ خدمة        │  │ خدمة        │  │ خدمة        │  │ خدمة    │ │
│  │ المستخدمين │  │ المحادثات  │  │ الاشتراكات  │  │ المصادقة│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ خدمة        │  │ خدمة        │  │ خدمة        │  │ خدمة    │ │
│  │ RAG         │  │ المراقبة    │  │ الذكاء      │  │ التحليل │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
                            ▲
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                          طبقة البيانات                          │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ Supabase    │  │ Qdrant      │  │ Redis       │  │ MinIO   │ │
│  │ (PostgreSQL)│  │ (متجهات)    │  │ (تخزين مؤقت)│  │ (ملفات) │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### تدفقات البيانات الرئيسية

#### 1. معالجة استفسار المستخدم

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │────>│ واجهة   │────>│ خدمة    │────>│ نموذج    │
│          │     │ Refine   │     │ الذكاء   │     │ الذكاء   │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
                                       │                │
                                       ▼                │
                                  ┌──────────┐         │
                                  │ خدمة     │<────────┘
                                  │ RAG      │
                                  └──────────┘
                                       │
                                       ▼
┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │<────│ واجهة   │<────│ Supabase │
│          │     │ Refine   │     │          │
└──────────┘     └──────────┘     └──────────┘
```

#### 2. دورة "عدم الإعجاب" والمراجعة البشرية

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │────>│ زر عدم   │────>│ خدمة    │────>│ طابور    │
│          │     │ الإعجاب  │     │ المراقبة │     │ المراجعة │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
                                                        │
                                                        ▼
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │<────│ إشعار    │<────│ خدمة    │<────│ المراجع  │
│          │     │          │     │ الإشعارات│     │ البشري   │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
```

### مكونات النظام

#### 1. الواجهة الأمامية (Frontend)

- **إطار العمل**: React 18.3.1 مع TypeScript 5.5.3
- **مكتبة الإدارة**: Refine.dev 4.47.0
- **مكتبة UI**: Ant Design 5.14.1
- **تنسيق CSS**: Tailwind CSS 3.4.1
- **أدوات إضافية**: Recharts للرسوم البيانية، Lucide React للأيقونات

#### 2. الخدمات الخلفية (Backend Services)

- **إطار العمل**: NestJS
- **ORM**: Prisma
- **المصادقة**: JWT مع Supabase Auth
- **التخزين المؤقت**: Redis
- **معالجة الطوابير**: Bull/BullMQ

#### 3. قواعد البيانات (Databases)

- **قاعدة البيانات الرئيسية**: Supabase (PostgreSQL)
- **قاعدة بيانات المتجهات**: Qdrant
- **التخزين المؤقت**: Redis
- **تخزين الملفات**: MinIO/S3

#### 4. خدمات الذكاء الاصطناعي (AI Services)

- **منصة النماذج المحلية**: Ollama
- **النماذج المدعومة**: Llama 3.2، Qwen 2.5، Deepseek-R1، Mistral 7B
- **إطار عمل RAG**: LangChain
- **تحويل النصوص إلى متجهات**: Sentence Transformers

## واجهة المستخدم الأمامية

### هيكل المكونات

يتبع المشروع هيكل مكونات منظم يفصل بين المكونات المشتركة والصفحات والخدمات:

```
src/
├── components/             # المكونات القابلة لإعادة الاستخدام
│   ├── chat/               # مكونات المحادثة
│   │   ├── ChatInput.tsx   # حقل إدخال المحادثة
│   │   ├── ChatMessage.tsx # عرض الرسائل
│   │   ├── legal-citation.tsx # الاستشهادات القانونية
│   │   └── ...
│   └── layout/             # مكونات التخطيط
│       ├── header.tsx      # رأس الصفحة
│       ├── sider.tsx       # الشريط الجانبي
│       └── ...
├── contexts/               # سياقات React
│   ├── i18n.tsx           # سياق الترجمة
│   ├── theme.tsx          # سياق السمة
│   └── ...
├── hooks/                  # خطافات مخصصة
│   ├── use-theme.ts       # خطاف السمة
│   ├── use-translation.ts # خطاف الترجمة
│   └── ...
├── lib/                    # مكتبات مساعدة
│   └── utils.ts           # دوال مساعدة
├── pages/                  # صفحات التطبيق
│   ├── dashboard.tsx      # لوحة التحكم الرئيسية
│   ├── users.tsx          # صفحة المستخدمين
│   ├── chats.tsx          # صفحة المحادثات
│   └── ...
├── services/               # خدمات البيانات
│   ├── content-moderation-service.ts
│   ├── rag-service.ts
│   ├── subscription-service.ts
│   └── ...
├── App.tsx                # المكون الرئيسي
└── main.tsx              # نقطة الدخول
```

### نظام التنقل

يستخدم النظام مكون `Sider` من Ant Design لعرض قائمة التنقل الرئيسية. يتم تعريف عناصر القائمة في ملف `src/components/layout/sider.tsx`:

```typescript
const menuItems = [
  { key: "dashboard", icon: <DashboardOutlined />, label: t("common.dashboard") },
  { key: "users", icon: <UserOutlined />, label: t("common.users") },
  { key: "chats", icon: <MessageOutlined />, label: t("common.chats") },
  { key: "legal-citations", icon: <FileTextOutlined />, label: t("common.legalCitations") },
  { key: "content-moderation", icon: <FlagOutlined />, label: t("common.contentModeration") },
  { key: "rag-management", icon: <DatabaseOutlined />, label: t("common.ragManagement") },
  { key: "subscription-management", icon: <CrownOutlined />, label: t("common.subscriptionManagement") },
  { key: "subscription-packages", icon: <AppstoreOutlined />, label: "باقات الاشتراك" },
  { key: "ai-settings", icon: <RobotOutlined />, label: t("common.aiSettings") },
  { key: "analytics", icon: <BarChartOutlined />, label: t("common.analytics") },
  { key: "settings", icon: <SettingOutlined />, label: t("common.settings") }
];
```

يتم تعريف مسارات التطبيق في ملف `src/App.tsx` باستخدام React Router:

```typescript
<Routes>
  <Route
    element={
      <ThemedLayoutV2
        Header={Header}
        Sider={Sider}
        Title={Title}
      >
        <Outlet />
      </ThemedLayoutV2>
    }
  >
    <Route index element={<Dashboard />} />
    <Route path="/users" element={<UserList />} />
    <Route path="/chats" element={<ChatList />} />
    <Route path="/legal-citations" element={<LegalCitationList />} />
    <Route path="/content-moderation" element={<ContentModerationPage />} />
    <Route path="/rag-management" element={<RAGManagementPage />} />
    <Route path="/subscription-management" element={<SubscriptionManagementPage />} />
    <Route path="/subscription-packages" element={<SubscriptionPackagesPage />} />
    <Route path="/ai-settings" element={<AISettingsPage />} />
    <Route path="/analytics" element={<AnalyticsDashboard />} />
    <Route path="/settings" element={<SettingsPage />} />
    <Route path="*" element={<ErrorComponent />} />
  </Route>
  <Route path="/login" element={<Login />} />
</Routes>
```

### نظام السمات والألوان

يستخدم النظام متغيرات CSS لتعريف نظام الألوان، مما يسمح بالتبديل بين السمات الفاتحة والداكنة:

#### السمة الفاتحة (Light Theme)

```css
:root {
  --color-background: 255 255 255;           /* أبيض نقي */
  --color-foreground: 15 23 42;              /* أسود داكن للنصوص */
  --color-primary: 38 53 237;                /* #2635ED - اللون الأزرق الرئيسي */
  --color-primary-foreground: 255 255 255;   /* أبيض على الأزرق */
  /* ... المزيد من المتغيرات ... */
}
```

#### السمة الداكنة (Dark Theme)

```css
.dark {
  --color-background: 16 16 16;              /* خلفية داكنة */
  --color-foreground: 224 224 224;           /* نص فاتح */
  --color-primary: 156 163 175;              /* رمادي فاتح بدلاً من الأزرق */
  --color-primary-foreground: 16 16 16;      /* نص داكن على الرمادي الفاتح */
  /* ... المزيد من المتغيرات ... */
}
```

يتم إدارة السمة من خلال سياق `ThemeContext` في `src/contexts/theme.tsx` الذي يوفر وظائف لتغيير السمة والحصول على السمة الحالية.

### الصفحات الرئيسية

#### 1. لوحة التحكم الرئيسية (Dashboard)

توفر نظرة عامة على النظام مع إحصائيات رئيسية:
- إجمالي المستخدمين والمستخدمين النشطين
- إجمالي المحادثات والتوكينز
- وقت الاستجابة ومعدل الأخطاء
- النشاط الأخير

#### 2. إدارة المستخدمين (Users)

تتيح إدارة المستخدمين مع دعم للأدوار المختلفة:
- administrator (مسؤول)
- moderator (مشرف)
- subscriber_enterprise (مشترك مؤسسي)
- subscriber_premium (مشترك متقدم)
- subscriber_basic (مشترك أساسي)
- user (مستخدم عادي)

#### 3. إدارة المحادثات (Chats)

تعرض قائمة المحادثات مع إمكانية:
- عرض تفاصيل المحادثة
- أرشفة المحادثات
- حذف المحادثات
- الإبلاغ عن المحادثات

#### 4. الاستشهادات القانونية (Legal Citations)

تدير قاعدة بيانات الاستشهادات القانونية:
- إضافة وتعديل وحذف الاستشهادات
- البحث والتصفية
- استيراد جماعي للاستشهادات

#### 5. مراقبة المحتوى (Content Moderation)

تدعم مراجعة المحتوى المُبلغ عنه:
- عرض التقارير المعلقة
- مراجعة وتقييم التقارير
- اتخاذ إجراءات (موافقة/رفض)

#### 6. إدارة RAG (RAG Management)

تدير نظام استرجاع المعرفة المعزز:
- رفع ومعالجة المستندات
- إدارة قاعدة بيانات المتجهات
- مراقبة استعلامات البحث

#### 7. إدارة الاشتراكات (Subscription Management)

تدير اشتراكات المستخدمين:
- عرض الاشتراكات النشطة والمنتهية
- ترقية وتخفيض الاشتراكات
- مراقبة استخدام التوكينز

#### 8. باقات الاشتراك (Subscription Packages)

تدير تكوين باقات الاشتراك:
- إنشاء وتعديل الباقات
- تحديد الميزات المتاحة والمحجوبة
- ضبط الأسعار والحدود

#### 9. إعدادات الذكاء الاصطناعي (AI Settings)

تتحكم في إعدادات نماذج الذكاء الاصطناعي:
- النماذج النصية والصوتية
- نظام الترجمة
- التحليل القانوني
- مراقبة الأداء

#### 10. التحليلات (Analytics)

توفر تحليلات شاملة للنظام:
- اتجاهات الاستخدام
- مقاييس الأداء
- سلوك المستخدمين
- تحليل المحتوى

#### 11. الإعدادات (Settings)

تتيح تكوين إعدادات النظام:
- إعدادات عامة
- إعدادات المحادثة
- إعدادات الأمان
- إعدادات الإشعارات والتكامل

## واجهات برمجة التطبيقات (API)

### نقاط النهاية الرئيسية

#### 1. إدارة المستخدمين

```
GET    /api/admin/users                # قائمة المستخدمين
POST   /api/admin/users                # إنشاء مستخدم جديد
GET    /api/admin/users/:id            # تفاصيل مستخدم
PUT    /api/admin/users/:id            # تحديث مستخدم
DELETE /api/admin/users/:id            # حذف مستخدم
POST   /api/admin/users/:id/suspend    # تعليق مستخدم
POST   /api/admin/users/:id/activate   # تفعيل مستخدم
```

#### 2. إدارة المحادثات

```
GET    /api/admin/chats                # قائمة المحادثات
GET    /api/admin/chats/:id            # تفاصيل محادثة
GET    /api/admin/chats/:id/messages   # رسائل محادثة
DELETE /api/admin/chats/:id            # حذف محادثة
POST   /api/admin/chats/:id/archive    # أرشفة محادثة
POST   /api/admin/chats/:id/flag       # الإبلاغ عن محادثة
```

#### 3. إدارة الاستشهادات القانونية

```
GET    /api/admin/legal-citations              # قائمة الاستشهادات
POST   /api/admin/legal-citations              # إنشاء استشهاد
GET    /api/admin/legal-citations/:id          # تفاصيل استشهاد
PUT    /api/admin/legal-citations/:id          # تحديث استشهاد
DELETE /api/admin/legal-citations/:id          # حذف استشهاد
POST   /api/admin/legal-citations/bulk-import  # استيراد جماعي
```

#### 4. مراقبة المحتوى

```
GET    /api/admin/content-reports              # قائمة التقارير
GET    /api/admin/content-reports/:id          # تفاصيل تقرير
PUT    /api/admin/content-reports/:id/review   # مراجعة تقرير
GET    /api/admin/content-reports/stats        # إحصائيات التقارير
```

#### 5. إدارة RAG

```
GET    /api/admin/rag/documents                # قائمة المستندات
POST   /api/admin/rag/documents                # رفع مستند
GET    /api/admin/rag/documents/:id            # تفاصيل مستند
DELETE /api/admin/rag/documents/:id            # حذف مستند
POST   /api/admin/rag/documents/:id/process    # معالجة مستند
GET    /api/admin/rag/stats                    # إحصائيات RAG
GET    /api/admin/rag/queries                  # استعلامات البحث
```

#### 6. إدارة الاشتراكات

```
GET    /api/admin/subscriptions                # قائمة الاشتراكات
GET    /api/admin/subscriptions/:id            # تفاصيل اشتراك
PUT    /api/admin/subscriptions/:id            # تحديث اشتراك
POST   /api/admin/subscriptions/:id/upgrade    # ترقية اشتراك
POST   /api/admin/subscriptions/:id/downgrade  # تخفيض اشتراك
POST   /api/admin/subscriptions/:id/cancel     # إلغاء اشتراك
POST   /api/admin/subscriptions/:id/renew      # تجديد اشتراك
```

#### 7. إدارة باقات الاشتراك

```
GET    /api/admin/subscription-packages        # قائمة الباقات
POST   /api/admin/subscription-packages        # إنشاء باقة
GET    /api/admin/subscription-packages/:id    # تفاصيل باقة
PUT    /api/admin/subscription-packages/:id    # تحديث باقة
DELETE /api/admin/subscription-packages/:id    # حذف باقة
PUT    /api/admin/subscription-packages/:id/status # تغيير حالة باقة
```

#### 8. إعدادات الذكاء الاصطناعي

```
GET    /api/admin/ai-settings                  # الحصول على الإعدادات
PUT    /api/admin/ai-settings                  # تحديث الإعدادات
GET    /api/admin/ai-settings/models           # قائمة النماذج
GET    /api/admin/ai-settings/performance      # مقاييس الأداء
```

#### 9. التحليلات

```
GET    /api/admin/analytics/dashboard          # إحصائيات لوحة التحكم
GET    /api/admin/analytics/usage              # إحصائيات الاستخدام
GET    /api/admin/analytics/performance        # إحصائيات الأداء
GET    /api/admin/analytics/users              # تحليلات المستخدمين
GET    /api/admin/analytics/content            # تحليلات المحتوى
GET    /api/admin/analytics/revenue            # تحليلات الإيرادات
```

### أمثلة الطلبات والاستجابات

#### مثال 1: الحصول على قائمة المستخدمين

**طلب:**

```http
GET /api/admin/users?page=1&limit=10&status=active
Authorization: Bearer {token}
```

**استجابة:**

```json
{
  "data": [
    {
      "id": "1",
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "status": "active",
      "role": "administrator",
      "joinDate": "2023-01-15T00:00:00.000Z",
      "lastActive": "2024-02-20T00:00:00.000Z",
      "chatCount": 45,
      "totalTokens": 12500,
      "subscriptionTier": "enterprise"
    },
    // ... المزيد من المستخدمين
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 45
  }
}
```

#### مثال 2: إنشاء استشهاد قانوني جديد

**طلب:**

```http
POST /api/admin/legal-citations
Authorization: Bearer {token}
Content-Type: application/json

{
  "articleNumber": "149",
  "title": "المادة 149 من نظام الشركات",
  "content": "يجب أن يكون رأس مال الشركة ذات المسؤولية المحدودة كافياً لتحقيق غرضها، وفي جميع الأحوال لا يجوز أن يقل عن خمسمائة ألف ريال.",
  "source": "نظام الشركات، الصادر بالمرسوم الملكي رقم (م/3) وتاريخ 1437/01/28هـ",
  "section": "الباب السابع - الشركة ذات المسؤولية المحدودة",
  "category": "الشركات",
  "tags": ["شركة ذات مسؤولية محدودة", "رأس المال", "تأسيس"]
}
```

**استجابة:**

```json
{
  "id": "5",
  "articleNumber": "149",
  "title": "المادة 149 من نظام الشركات",
  "content": "يجب أن يكون رأس مال الشركة ذات المسؤولية المحدودة كافياً لتحقيق غرضها، وفي جميع الأحوال لا يجوز أن يقل عن خمسمائة ألف ريال.",
  "source": "نظام الشركات، الصادر بالمرسوم الملكي رقم (م/3) وتاريخ 1437/01/28هـ",
  "section": "الباب السابع - الشركة ذات المسؤولية المحدودة",
  "category": "الشركات",
  "tags": ["شركة ذات مسؤولية محدودة", "رأس المال", "تأسيس"],
  "usageCount": 0,
  "lastUsed": null,
  "createdAt": "2024-03-01T12:34:56.789Z",
  "updatedAt": "2024-03-01T12:34:56.789Z"
}
```

### متطلبات المصادقة

يستخدم النظام مصادقة JWT مع Supabase Auth:

1. **تسجيل الدخول**: يتم إرسال بيانات الاعتماد (البريد الإلكتروني وكلمة المرور) إلى `/api/auth/login` للحصول على رمز JWT.
2. **التحقق من الرمز**: يتم إرفاق الرمز في رأس `Authorization` لجميع الطلبات اللاحقة.
3. **تجديد الرمز**: يتم تجديد الرمز تلقائيًا عند انتهاء صلاحيته باستخدام رمز التحديث.
4. **تسجيل الخروج**: يتم إرسال طلب إلى `/api/auth/logout` لإبطال الرمز الحالي.

### معالجة الأخطاء

يستخدم النظام تنسيق موحد للأخطاء:

```json
{
  "statusCode": 400,
  "message": "رسالة الخطأ",
  "error": "نوع الخطأ",
  "details": {
    // تفاصيل إضافية عن الخطأ
  }
}
```

أكواد الحالة الشائعة:
- `400`: طلب غير صالح
- `401`: غير مصرح
- `403`: محظور
- `404`: غير موجود
- `422`: خطأ في التحقق
- `500`: خطأ في الخادم

## هيكل قاعدة البيانات

### مخطط العلاقات (ERD)

```
┌────────────┐       ┌────────────┐       ┌────────────┐
│   users    │       │   chats    │       │  messages  │
├────────────┤       ├────────────┤       ├────────────┤
│ id         │       │ id         │       │ id         │
│ name       │       │ user_id    │◄──────┤ chat_id    │
│ email      │       │ title      │       │ sender     │
│ password   │       │ created_at │       │ content    │
│ role       │◄──────┤ last_active│       │ created_at │
│ status     │       │ status     │       │ tokens     │
│ created_at │       └────────────┘       └────────────┘
└────────────┘
      ▲
      │
┌────────────┐       ┌────────────┐       ┌────────────┐
│subscriptions│       │subscription│       │  features  │
├────────────┤       │  packages  │       ├────────────┤
│ id         │       ├────────────┤       │ id         │
│ user_id    │       │ id         │◄──────┤ package_id │
│ package_id │◄──────┤ name       │       │ name       │
│ start_date │       │ price      │       │ description│
│ end_date   │       │ features   │       │ category   │
│ status     │       │ is_active  │       └────────────┘
└────────────┘       └────────────┘
                            ▲
                            │
┌────────────┐       ┌────────────┐       ┌────────────┐
│ legal_     │       │ content_   │       │   rag_     │
│ citations  │       │ reports    │       │ documents  │
├────────────┤       ├────────────┤       ├────────────┤
│ id         │       │ id         │       │ id         │
│ article_num│       │ message_id │       │ name       │
│ title      │       │ reporter_id│       │ type       │
│ content    │       │ reason     │       │ status     │
│ source     │       │ status     │       │ vectors    │
│ category   │       │ reviewed_by│       │ category   │
└────────────┘       └────────────┘       └────────────┘
```

### جداول قاعدة البيانات

#### 1. جدول المستخدمين (users)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| name | VARCHAR(100) | اسم المستخدم |
| email | VARCHAR(100) | البريد الإلكتروني (فريد) |
| password | VARCHAR(100) | كلمة المرور المشفرة |
| role | VARCHAR(50) | دور المستخدم (administrator, moderator, subscriber_enterprise, subscriber_premium, subscriber_basic, user) |
| status | VARCHAR(20) | حالة المستخدم (active, inactive, suspended) |
| created_at | TIMESTAMP | تاريخ الإنشاء |
| last_active | TIMESTAMP | آخر نشاط |

#### 2. جدول المحادثات (chats)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| user_id | UUID | معرف المستخدم (مفتاح خارجي) |
| title | VARCHAR(200) | عنوان المحادثة |
| created_at | TIMESTAMP | تاريخ الإنشاء |
| last_active | TIMESTAMP | آخر نشاط |
| status | VARCHAR(20) | حالة المحادثة (active, archived, flagged) |

#### 3. جدول الرسائل (messages)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| chat_id | UUID | معرف المحادثة (مفتاح خارجي) |
| sender | VARCHAR(10) | المرسل (user, bot) |
| content | TEXT | محتوى الرسالة |
| created_at | TIMESTAMP | تاريخ الإنشاء |
| tokens | INTEGER | عدد التوكينز المستخدمة |
| has_citations | BOOLEAN | هل تحتوي على استشهادات |

#### 4. جدول الاشتراكات (subscriptions)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| user_id | UUID | معرف المستخدم (مفتاح خارجي) |
| package_id | UUID | معرف الباقة (مفتاح خارجي) |
| start_date | TIMESTAMP | تاريخ البداية |
| end_date | TIMESTAMP | تاريخ الانتهاء |
| status | VARCHAR(20) | حالة الاشتراك (active, expired, cancelled) |
| tokens_used | INTEGER | عدد التوكينز المستخدمة |
| tokens_limit | INTEGER | حد التوكينز |
| auto_renew | BOOLEAN | التجديد التلقائي |
| last_payment | TIMESTAMP | آخر دفعة |
| next_billing | TIMESTAMP | الفاتورة التالية |

#### 5. جدول باقات الاشتراك (subscription_packages)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| name | VARCHAR(100) | اسم الباقة |
| monthly_price | DECIMAL(10,2) | السعر الشهري |
| yearly_price | DECIMAL(10,2) | السعر السنوي |
| max_chats | INTEGER | الحد الأقصى للمحادثات |
| max_tokens | INTEGER | الحد الأقصى للتوكينز |
| features | JSONB | الميزات المتاحة |
| blocked_features | JSONB | الميزات المحجوبة |
| is_active | BOOLEAN | هل الباقة مفعلة |
| is_default | BOOLEAN | هل الباقة افتراضية |
| sort_order | INTEGER | ترتيب العرض |
| color | VARCHAR(20) | لون الباقة |

#### 6. جدول الاستشهادات القانونية (legal_citations)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| article_number | VARCHAR(20) | رقم المادة |
| title | VARCHAR(200) | عنوان الاستشهاد |
| content | TEXT | محتوى الاستشهاد |
| source | VARCHAR(200) | مصدر الاستشهاد |
| section | VARCHAR(200) | قسم الاستشهاد |
| category | VARCHAR(50) | فئة الاستشهاد |
| tags | JSONB | وسوم الاستشهاد |
| usage_count | INTEGER | عدد الاستخدام |
| last_used | TIMESTAMP | آخر استخدام |
| created_at | TIMESTAMP | تاريخ الإنشاء |
| updated_at | TIMESTAMP | تاريخ التحديث |

#### 7. جدول تقارير المحتوى (content_reports)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| conversation_id | UUID | معرف المحادثة |
| message_id | UUID | معرف الرسالة |
| reporter_id | UUID | معرف المبلغ |
| reason | VARCHAR(100) | سبب الإبلاغ |
| description | TEXT | وصف الإبلاغ |
| status | VARCHAR(20) | حالة التقرير (pending, approved, rejected) |
| priority | VARCHAR(20) | أولوية التقرير (low, medium, high) |
| created_at | TIMESTAMP | تاريخ الإنشاء |
| reviewed_by | UUID | معرف المراجع |
| reviewed_at | TIMESTAMP | تاريخ المراجعة |

#### 8. جدول مستندات RAG (rag_documents)

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | UUID | المعرف الفريد (المفتاح الأساسي) |
| name | VARCHAR(200) | اسم المستند |
| type | VARCHAR(20) | نوع المستند |
| size | DECIMAL(10,2) | حجم المستند (MB) |
| status | VARCHAR(20) | حالة المستند (processed, processing, failed) |
| upload_date | TIMESTAMP | تاريخ الرفع |
| processed_date | TIMESTAMP | تاريخ المعالجة |
| chunks | INTEGER | عدد المقاطع |
| vectors | INTEGER | عدد المتجهات |
| category | VARCHAR(50) | فئة المستند |
| language | VARCHAR(10) | لغة المستند |

### العلاقات والقيود

- **users - chats**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة محادثات)
- **chats - messages**: علاقة واحد إلى متعدد (محادثة واحدة تحتوي على عدة رسائل)
- **users - subscriptions**: علاقة واحد إلى واحد (مستخدم واحد له اشتراك واحد)
- **subscription_packages - subscriptions**: علاقة واحد إلى متعدد (باقة واحدة يمكن أن تكون لعدة اشتراكات)
- **subscription_packages - features**: علاقة واحد إلى متعدد (باقة واحدة لها عدة ميزات)

### استعلامات نموذجية

#### 1. الحصول على المستخدمين النشطين مع تفاصيل الاشتراك

```sql
SELECT u.id, u.name, u.email, u.role, u.status, 
       s.id as subscription_id, s.status as subscription_status,
       p.name as package_name, p.monthly_price
FROM users u
LEFT JOIN subscriptions s ON u.id = s.user_id
LEFT JOIN subscription_packages p ON s.package_id = p.id
WHERE u.status = 'active'
ORDER BY u.created_at DESC
LIMIT 10 OFFSET 0;
```

#### 2. الحصول على المحادثات مع عدد الرسائل والتوكينز

```sql
SELECT c.id, c.title, c.created_at, c.status,
       u.name as user_name,
       COUNT(m.id) as message_count,
       SUM(m.tokens) as total_tokens
FROM chats c
JOIN users u ON c.user_id = u.id
LEFT JOIN messages m ON c.id = m.chat_id
GROUP BY c.id, u.name
ORDER BY c.last_active DESC
LIMIT 10 OFFSET 0;
```

#### 3. الحصول على الاستشهادات القانونية الأكثر استخدامًا

```sql
SELECT id, article_number, title, content, source, category, usage_count
FROM legal_citations
ORDER BY usage_count DESC
LIMIT 10;
```

#### 4. الحصول على تقارير المحتوى المعلقة ذات الأولوية العالية

```sql
SELECT cr.id, cr.reason, cr.description, cr.created_at, cr.priority,
       u.name as reporter_name,
       c.title as conversation_title
FROM content_reports cr
JOIN users u ON cr.reporter_id = u.id
JOIN chats c ON cr.conversation_id = c.id
WHERE cr.status = 'pending' AND cr.priority = 'high'
ORDER BY cr.created_at ASC;
```

## نظام المصادقة والتفويض

### تدفق المصادقة

يستخدم النظام Supabase Auth لإدارة المصادقة:

1. **تسجيل الدخول**:
   - المستخدم يدخل البريد الإلكتروني وكلمة المرور
   - يتم إرسال الطلب إلى Supabase Auth
   - يتم إنشاء رمز JWT وإرجاعه للعميل
   - يتم تخزين الرمز في localStorage

```typescript
// src/authProvider.ts
const authProvider: AuthProvider = {
  login: async ({ username, password }) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: username,
      password
    });

    if (error) throw error;
    
    localStorage.setItem('auth', JSON.stringify(data.session));
    return Promise.resolve();
  },
  // ... وظائف أخرى
};
```

2. **التحقق من الجلسة**:
   - يتم التحقق من وجود رمز JWT صالح في localStorage
   - إذا كان الرمز غير موجود أو منتهي الصلاحية، يتم توجيه المستخدم إلى صفحة تسجيل الدخول

```typescript
// src/authProvider.ts
const authProvider: AuthProvider = {
  // ... وظائف أخرى
  check: () => 
    localStorage.getItem('auth') ? Promise.resolve() : Promise.reject(),
  // ... وظائف أخرى
};
```

3. **تسجيل الخروج**:
   - يتم إرسال طلب لإبطال الرمز
   - يتم حذف الرمز من localStorage
   - يتم توجيه المستخدم إلى صفحة تسجيل الدخول

```typescript
// src/authProvider.ts
const authProvider: AuthProvider = {
  // ... وظائف أخرى
  logout: async () => {
    await supabase.auth.signOut();
    localStorage.removeItem('auth');
    return Promise.resolve();
  },
  // ... وظائف أخرى
};
```

### إدارة الجلسات

- **مدة الجلسة**: يتم تكوين مدة صلاحية رمز JWT في إعدادات Supabase (الافتراضي: 60 دقيقة)
- **تجديد الرمز**: يتم استخدام رمز التحديث لتجديد رمز JWT تلقائيًا عند انتهاء صلاحيته
- **إدارة الجلسات المتعددة**: يمكن للمستخدم تسجيل الدخول من عدة أجهزة في نفس الوقت

### نظام الأدوار والصلاحيات

يستخدم النظام 6 أدوار رئيسية مع صلاحيات محددة لكل دور:

#### 1. المسؤول (Administrator)

```typescript
{
  role: "administrator",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: -1,       // غير محدود
    maxFileSize: -1,          // غير محدود
    maxTokensPerChat: -1,     // غير محدود
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true,   // يمكنه مراجعة المحتوى
    adminAccess: true         // يمكنه إدارة النظام
  }
}
```

#### 2. المشرف (Moderator)

```typescript
{
  role: "moderator",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: 50,
    maxFileSize: 100,         // 100 MB
    maxTokensPerChat: 6000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: true,   // يمكنه مراجعة المحتوى
    adminAccess: false
  }
}
```

#### 3. المشترك المؤسسي (Enterprise Subscriber)

```typescript
{
  role: "subscriber_enterprise",
  permissions: {
    maxDailyQueries: -1,      // غير محدود
    maxFileUploads: 100,
    maxFileSize: 500,         // 500 MB
    maxTokensPerChat: 8000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### 4. المشترك المتقدم (Premium Subscriber)

```typescript
{
  role: "subscriber_premium",
  permissions: {
    maxDailyQueries: 500,
    maxFileUploads: 20,
    maxFileSize: 50,          // 50 MB
    maxTokensPerChat: 4000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: true,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### 5. المشترك الأساسي (Basic Subscriber)

```typescript
{
  role: "subscriber_basic",
  permissions: {
    maxDailyQueries: 100,
    maxFileUploads: 5,
    maxFileSize: 10,          // 10 MB
    maxTokensPerChat: 2000,
    voiceEnabled: true,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  }
}
```

#### 6. المستخدم العادي (Regular User)

```typescript
{
  role: "user",
  permissions: {
    maxDailyQueries: 10,
    maxFileUploads: 0,
    maxFileSize: 0,
    maxTokensPerChat: 1000,
    voiceEnabled: false,
    citationsEnabled: true,
    customModelsEnabled: false,
    moderationAccess: false,
    adminAccess: false
  }
}
```

### سياسات الأمان

#### 1. سياسات أمان مستوى الصف (Row Level Security)

يستخدم النظام سياسات RLS في Supabase لضمان أن المستخدمين يمكنهم فقط الوصول إلى البيانات المصرح بها:

```sql
-- سياسة الوصول للاشتراكات
CREATE POLICY subscription_access_policy 
ON subscriptions 
FOR SELECT USING (
  auth.uid() = user_id
  OR current_user = 'admin'
);

-- سياسة الوصول للمحادثات
CREATE POLICY chat_access_policy 
ON chats 
FOR SELECT USING (
  auth.uid() = user_id
  OR current_user = 'admin'
  OR current_user = 'moderator'
);
```

#### 2. سياسة كلمات المرور

- الحد الأدنى لطول كلمة المرور: 8 أحرف
- تتطلب حروف كبيرة وصغيرة
- تتطلب أرقام
- تتطلب رموز خاصة
- تخزين كلمات المرور مشفرة باستخدام bcrypt

#### 3. حماية API

- تطبيق Rate Limiting لمنع هجمات DDoS
- تطبيق CORS لتقييد الوصول من المواقع غير المصرح بها
- تطبيق التحقق من صحة المدخلات لمنع هجمات الحقن

## تكامل الذكاء الاصطناعي

### النماذج المستخدمة

يدعم النظام عدة نماذج مفتوحة المصدر تعمل محليًا باستخدام Ollama:

#### 1. Llama 3.2 (7B/13B)

- **الاستخدام**: للمهام العامة والمحادثات المتنوعة
- **المعاملات المثلى**:
  - temperature: 0.7
  - top_p: 0.95
  - max_tokens: 2000

#### 2. Qwen 2.5

- **الاستخدام**: محسن خصيصًا للغة العربية والمحتوى متعدد اللغات
- **المعاملات المثلى**:
  - temperature: 0.5
  - top_p: 0.9
  - max_tokens: 1000

#### 3. Deepseek-R1

- **الاستخدام**: للمنطق والاستدلال المتقدم
- **المعاملات المثلى**:
  - temperature: 0.2
  - top_p: 0.85
  - max_tokens: 1500

#### 4. Mistral 7B

- **الاستخدام**: للأداء المتوازن والكفاءة العالية
- **المعاملات المثلى**:
  - temperature: 0.6
  - top_p: 0.9
  - max_tokens: 1200

### إعدادات النماذج

يمكن تكوين إعدادات النماذج من خلال صفحة إعدادات الذكاء الاصطناعي:

```typescript
// services/ai_model.py
function selectModel(taskType: string) {
  if (taskType === "legal") {
    return Ollama(model="legal-llama:7b");
  } else if (taskType === "creative") {
    return Ollama(model="mixtral:8x7b");
  } else {
    return Ollama(model="llama3:8b");
  }
}
```

### معالجة الطلبات

تتم معالجة طلبات الذكاء الاصطناعي من خلال خدمة وسيطة تقوم بتوجيه الطلب إلى النموذج المناسب:

```typescript
// services/ai_service.ts
async function generateResponse(prompt: string, options: AIOptions) {
  // 1. اختيار النموذج المناسب
  const model = selectModel(options.taskType);
  
  // 2. إذا كان RAG مفعل، استرجاع السياق
  let context = "";
  if (options.enableRAG) {
    const relevantDocs = await ragService.retrieveRelevantDocuments(prompt);
    context = relevantDocs.map(doc => doc.content).join("\n\n");
  }
  
  // 3. بناء الاستعلام النهائي
  const finalPrompt = context ? `${context}\n\n${prompt}` : prompt;
  
  // 4. توليد الاستجابة
  const response = await model.generate(finalPrompt, {
    temperature: options.temperature,
    top_p: options.top_p,
    max_tokens: options.max_tokens
  });
  
  // 5. إذا كان الاستشهاد مفعل، إضافة الاستشهادات
  if (options.enableCitations && context) {
    return citationService.addCitations(response, relevantDocs);
  }
  
  return response;
}
```

### تحسين الأداء

لتحسين أداء النماذج، يتم تطبيق عدة تقنيات:

1. **التخزين المؤقت للاستجابات**: تخزين الاستجابات المتكررة في Redis
2. **تقسيم الطلبات الكبيرة**: تقسيم الطلبات الكبيرة إلى أجزاء أصغر
3. **توزيع الأحمال**: توزيع الطلبات على عدة نسخ من النماذج
4. **التبديل التلقائي للنماذج**: التبديل بين النماذج بناءً على الأداء والحمل

## نظام البحث المتجهي (RAG)

### معمارية RAG

يستخدم النظام معمارية Retrieval-Augmented Generation (RAG) لتحسين دقة الاستجابات:

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  المستندات   │────>│  معالجة      │────>│  تحويل إلى   │
│  القانونية   │     │  النصوص     │     │  متجهات      │
└──────────────┘     └──────────────┘     └──────────────┘
                                                 │
                                                 ▼
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  استجابة     │<────│  توليد       │<────│  استرجاع     │
│  مع استشهادات│     │  النص        │     │  المتجهات    │
└──────────────┘     └──────────────┘     └──────────────┘
```

### تكوين Qdrant

يستخدم النظام Qdrant كقاعدة بيانات متجهية لتخزين واسترجاع المتجهات:

```python
from qdrant_client import QdrantClient
from sentence_transformers import SentenceTransformer

class RAGService:
    def __init__(self):
        self.qdrant = QdrantClient("localhost", port=6333)
        self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
        
    async def add_documents(self, documents):
        vectors = self.encoder.encode(documents)
        
        self.qdrant.upsert(
            collection_name="knowledge_base",
            points=[
                {
                    "id": i,
                    "vector": vector.tolist(),
                    "payload": {"text": doc}
                }
                for i, (doc, vector) in enumerate(zip(documents, vectors))
            ]
        )
    
    async def retrieve_context(self, query, limit=5):
        query_vector = self.encoder.encode([query])
        
        results = self.qdrant.search(
            collection_name="knowledge_base",
            query_vector=query_vector[0].tolist(),
            limit=limit
        )
        
        return [hit.payload["text"] for hit in results]
```

### عمليات الفهرسة والبحث

#### 1. فهرسة المستندات

1. **تحميل المستند**: يتم رفع المستند (PDF، DOCX، TXT) عبر واجهة إدارة RAG
2. **استخراج النص**: يتم استخراج النص من المستند
3. **تقسيم النص**: يتم تقسيم النص إلى مقاطع صغيرة (chunks)
4. **تحويل المقاطع إلى متجهات**: يتم تحويل المقاطع إلى متجهات باستخدام نموذج Sentence Transformer
5. **تخزين المتجهات**: يتم تخزين المتجهات في Qdrant مع البيانات الوصفية

#### 2. البحث في المتجهات

1. **تحويل الاستعلام إلى متجه**: يتم تحويل استعلام المستخدم إلى متجه
2. **البحث عن المتجهات المشابهة**: يتم البحث عن المتجهات الأقرب في Qdrant
3. **استرجاع المقاطع**: يتم استرجاع المقاطع المرتبطة بالمتجهات
4. **دمج المقاطع مع الاستعلام**: يتم دمج المقاطع مع استعلام المستخدم
5. **توليد الاستجابة**: يتم إرسال الاستعلام المدمج إلى نموذج الذكاء الاصطناعي

### تحسين الأداء

لتحسين أداء نظام RAG، يتم تطبيق عدة تقنيات:

1. **تحسين حجم المقاطع**: تجربة أحجام مختلفة للمقاطع (256، 512، 1024 حرف)
2. **تحسين استراتيجية التقسيم**: استخدام تقسيم ذكي يحافظ على سياق النص
3. **تحسين نماذج التحويل إلى متجهات**: استخدام نماذج متخصصة للغة العربية
4. **تحسين استراتيجية الاسترجاع**: تجربة خوارزميات مختلفة للاسترجاع (MMR، Top-K، Hybrid)

## نظام إدارة الاشتراكات

### هيكل الباقات

يدعم النظام 4 مستويات من الاشتراكات:

#### 1. الباقة المجانية (Free)

- **السعر**: 0 ريال
- **الحدود**:
  - 10 استفسارات يومية
  - 5,000 توكن شهرياً
  - لا توجد محادثة صوتية
  - لا يمكن رفع ملفات
- **الميزات**:
  - الوصول للنماذج الأساسية فقط
  - دعم المجتمع

#### 2. الباقة الأساسية (Basic)

- **السعر**: 199 ريال/شهر أو 1990 ريال/سنة
- **الحدود**:
  - 100 استفسار يومي
  - 50,000 توكن شهرياً
  - رفع ملفات حتى 10 MB
- **الميزات**:
  - المحادثة الصوتية
  - نماذج متوسطة
  - دعم البريد الإلكتروني

#### 3. الباقة المتقدمة (Premium)

- **السعر**: 499 ريال/شهر أو 4990 ريال/سنة
- **الحدود**:
  - 500 استفسار يومي
  - 200,000 توكن شهرياً
  - رفع ملفات حتى 50 MB
- **الميزات**:
  - جميع ميزات الباقة الأساسية
  - الوصول لجميع النماذج
  - نظام RAG
  - دعم مباشر

#### 4. الباقة المؤسسية (Enterprise)

- **السعر**: 999 ريال/شهر أو 9990 ريال/سنة
- **الحدود**:
  - استفسارات غير محدودة
  - توكينز غير محدودة
  - رفع ملفات حتى 500 MB
- **الميزات**:
  - جميع الميزات
  - نماذج مخصصة
  - دعم مخصص 24/7

### آلية التحكم في الميزات

يستخدم النظام آلية تحكم ديناميكية في الميزات بناءً على مستوى الاشتراك:

```typescript
// src/utils/featureToggle.js
export const checkFeatureAccess = (userSubscription, feature) => {
  const PLAN_FEATURES = {
    free: ["basic_models", "community_support"],
    basic: ["file_upload_10mb", "medium_models", ...PLAN_FEATURES.free],
    premium: ["voice", "advanced_models", "rag", ...PLAN_FEATURES.basic],
    enterprise: ["custom_models", "priority_support", ...PLAN_FEATURES.premium]
  };
  return PLAN_FEATURES[userSubscription.plan]?.includes(feature);
};
```

في واجهة المستخدم، يتم استخدام مكون `FeatureGuard` للتحكم في الوصول إلى الميزات:

```jsx
// src/components/FeatureGuard.jsx
const FeatureGuard = ({ feature, children }) => {
  const { subscription } = useUser();
  
  if (!checkFeatureAccess(subscription, feature)) {
    return (
      <div className="border border-dashed border-gray-300 rounded-lg p-6 text-center">
        <LockOutlined className="text-3xl text-gray-400 mb-2" />
        <h3 className="text-lg font-medium">هذه الميزة غير متاحة في باقتك الحالية</h3>
        <p className="text-gray-500 mb-4">قم بالترقية إلى باقة أعلى للوصول إلى هذه الميزة</p>
        <Button type="primary">ترقية الباقة</Button>
      </div>
    );
  }
  return children;
};
```

### معالجة المدفوعات

يدعم النظام عدة بوابات دفع:

1. **مدى**: للبطاقات المحلية
2. **فيزا/ماستركارد**: للبطاقات العالمية
3. **STC Pay**: للدفع عبر المحفظة الإلكترونية

تدفق عملية الدفع:

```
المستخدم → نقرت "ترقية الباقة"
  ↓
الواجهة → طلب فاتورة (199 ريال)
  ↓
بوابة الدفع → صفحة الدفع
  ↓
المستخدم → إتمام الدفع
  ↓
بوابة الدفع → webhook تأكيد الدفع
  ↓
الخادم → تفعيل الباقة الجديدة
  ↓
الخادم → إرسال إشعار الترقية
```

### إدارة دورة حياة الاشتراك

#### 1. إنشاء الاشتراك

عند إنشاء اشتراك جديد، يتم:
- إنشاء سجل في جدول `subscriptions`
- تحديث دور المستخدم في جدول `users`
- إنشاء فاتورة في نظام الفواتير
- إرسال بريد إلكتروني ترحيبي

#### 2. تجديد الاشتراك

قبل انتهاء الاشتراك، يتم:
- إرسال إشعار تذكير قبل 7 أيام من الانتهاء
- محاولة تجديد تلقائي إذا كان مفعلاً
- تحديث تاريخ انتهاء الاشتراك

```sql
-- إجراء إشعار التجديد
CREATE FUNCTION notify_renewal()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.expires_at < NOW() + INTERVAL '7 days' AND NEW.expires_at > NOW() THEN
    INSERT INTO notifications(user_id, message)
    VALUES (NEW.user_id, 'اشتراكك ينتهي خلال 7 أيام');
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تشغيل الإجراء عند تحديث الاشتراك
CREATE TRIGGER subscription_renewal_check
AFTER INSERT OR UPDATE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION notify_renewal();
```

#### 3. إلغاء الاشتراك

عند إلغاء الاشتراك، يتم:
- تحديث حالة الاشتراك إلى `cancelled`
- تعطيل التجديد التلقائي
- جمع ملاحظات حول سبب الإلغاء
- إرسال عرض استرجاع

## نظام مراقبة المحتوى

### تدفق عمل المراجعة

يتبع نظام مراقبة المحتوى تدفق عمل محدد:

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │────>│ زر عدم   │────>│ نموذج    │────>│ قائمة    │
│          │     │ الإعجاب  │     │ الإبلاغ  │     │ الانتظار │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
                                                        │
                                                        ▼
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│ المستخدم │<────│ إشعار    │<────│ إجراء    │<────│ المراجع  │
│          │     │ بالنتيجة │     │ المراجع  │     │ البشري   │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
```

### معالجة التقارير

عندما يضغط المستخدم على زر "عدم الإعجاب"، يتم تنفيذ الخطوات التالية:

```typescript
// services/reporting_service.py
function handleDislike(conversation_id, reason) {
  // إضافة للتقييم البشري
  supabase.table('reported_content').insert({
    'conversation_id': conversation_id,
    'reason': reason,
    'status': 'pending'
  }).execute();
  
  // إشعار المشرفين
  redis.publish('review-channel', JSON.stringify({
    'type': 'new_report',
    'data': conversation_id
  }));
}
```

### إجراءات المراجعة

يمكن للمراجعين اتخاذ إجراءات مختلفة:

1. **الموافقة على التقرير**:
   - تحديث حالة التقرير إلى `approved`
   - تحديث المحادثة المبلغ عنها
   - إضافة ملاحظات المراجع
   - إرسال إشعار للمستخدم

2. **رفض التقرير**:
   - تحديث حالة التقرير إلى `rejected`
   - إضافة ملاحظات المراجع
   - إرسال إشعار للمستخدم (اختياري)

3. **طلب مراجعة إضافية**:
   - تحديث أولوية التقرير
   - إضافة ملاحظات للمراجع التالي

## المراقبة والتحليلات

### مقاييس الأداء

يتم جمع وعرض عدة مقاييس أداء رئيسية:

#### 1. مقاييس الاستخدام

- عدد المستخدمين النشطين (يوميًا/أسبوعيًا/شهريًا)
- عدد المحادثات الجديدة
- عدد الرسائل المتبادلة
- استخدام التوكينز

#### 2. مقاييس الأداء التقني

- وقت استجابة API
- وقت توليد النصوص
- معدل الأخطاء
- وقت التشغيل

#### 3. مقاييس جودة المحتوى

- معدل الإبلاغ عن المحتوى
- نسبة الإعجاب/عدم الإعجاب
- دقة الاستشهادات
- معدل الاستخدام المتكرر

### تتبع الأخطاء

يستخدم النظام آلية شاملة لتتبع الأخطاء:

1. **تسجيل الأخطاء**: يتم تسجيل جميع الأخطاء في سجلات مركزية
2. **تصنيف الأخطاء**: يتم تصنيف الأخطاء حسب الشدة والمصدر
3. **إشعارات الأخطاء**: يتم إرسال إشعارات للمسؤولين عند حدوث أخطاء حرجة
4. **تحليل الأخطاء**: يتم تحليل الأخطاء لتحديد الأسباب الجذرية

### تحليلات المستخدم

يوفر النظام تحليلات شاملة لسلوك المستخدمين:

1. **أنماط الاستخدام**: تحليل أوقات وتكرار استخدام النظام
2. **تحليل المحتوى**: تحليل موضوعات المحادثات والاستفسارات الشائعة
3. **تحليل المسار**: تتبع مسار المستخدم خلال النظام
4. **تحليل الاحتفاظ**: قياس معدلات الاحتفاظ بالمستخدمين

## الأمان والامتثال

### تشفير البيانات

يتم تطبيق تشفير البيانات على عدة مستويات:

1. **تشفير أثناء النقل**: يتم استخدام HTTPS لجميع الاتصالات
2. **تشفير أثناء التخزين**: يتم تشفير البيانات الحساسة في قاعدة البيانات
3. **تشفير كلمات المرور**: يتم تخزين كلمات المرور مشفرة باستخدام bcrypt

### حماية API

يتم تطبيق عدة تقنيات لحماية واجهات برمجة التطبيقات:

1. **Rate Limiting**: تقييد عدد الطلبات لكل مستخدم
2. **JWT Authentication**: التحقق من صحة الرمز لكل طلب
3. **Input Validation**: التحقق من صحة جميع المدخلات
4. **CORS**: تقييد الوصول من المواقع غير المصرح بها

```typescript
// Rate Limiting
@UseGuards(ThrottlerGuard)
@Throttle(100, 60) // 100 requests per minute
export class AdminController {
  // ...
}

// Input Validation
@Post('users')
async createUser(@Body() createUserDto: CreateUserDto) {
  // التحقق من صحة البيانات
  await this.userService.create(createUserDto);
}

// Authorization
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin', 'super_admin')
@Get('sensitive-data')
async getSensitiveData() {
  // ...
}
```

### سجلات التدقيق

يتم تسجيل جميع الأنشطة الإدارية في سجلات التدقيق:

```typescript
// تسجيل جميع الأنشطة الإدارية
@Injectable()
export class AuditLogService {
  async logActivity(
    adminId: string,
    action: string,
    resourceType: string,
    resourceId: string,
    details: object
  ) {
    await this.prisma.adminActivityLog.create({
      data: {
        adminId,
        action,
        resourceType,
        resourceId,
        details,
        ipAddress: this.getClientIP(),
        userAgent: this.getUserAgent()
      }
    });
  }
}
```

### الامتثال للوائح

يتوافق النظام مع عدة لوائح وأنظمة:

1. **GDPR**: حماية البيانات الشخصية للمستخدمين الأوروبيين
2. **CCPA**: حماية خصوصية المستهلكين في كاليفورنيا
3. **PDPL**: نظام حماية البيانات الشخصية السعودي
4. **HIPAA**: حماية المعلومات الصحية (إذا كان ينطبق)

## النشر والتشغيل

### متطلبات النظام

#### متطلبات الخادم

- **المعالج**: Intel Xeon أو AMD EPYC (8+ أنوية)
- **الذاكرة**: 64 GB RAM (128 GB مفضل)
- **التخزين**: 1 TB NVMe SSD
- **معالج الرسوميات**: NVIDIA RTX 4080/4090 أو A100

#### متطلبات البرمجيات

- **نظام التشغيل**: Ubuntu 22.04 LTS
- **Docker & Docker Compose**: لتشغيل الخدمات
- **Node.js 18+**: للواجهة الأمامية
- **Python 3.10+**: للخدمات الخلفية
- **PostgreSQL 15**: قاعدة البيانات الرئيسية

### إعداد البيئة

#### 1. إعداد Docker Compose

```yaml
version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://api:4000
      - REACT_APP_SUPABASE_URL=${SUPABASE_URL}
      - REACT_APP_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - api

  api:
    build: ./backend
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - qdrant

  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  qdrant:
    image: qdrant/qdrant
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  ollama_data:
  qdrant_data:
  redis_data:
```

#### 2. إعداد متغيرات البيئة

```
# .env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DATABASE_URL=postgresql://postgres:password@localhost:5432/shuraih
JWT_SECRET=your-jwt-secret
REDIS_URL=redis://localhost:6379
```

### عملية النشر

#### 1. بناء التطبيق

```bash
# بناء الواجهة الأمامية
cd frontend
npm install
npm run build

# بناء الخدمات الخلفية
cd backend
npm install
npm run build
```

#### 2. نشر التطبيق

```bash
# نشر باستخدام Docker Compose
docker-compose up -d
```

#### 3. تهيئة قاعدة البيانات

```bash
# تشغيل الهجرات
npx prisma migrate deploy
```

#### 4. تحميل النماذج

```bash
# تحميل نماذج Ollama
docker exec -it ollama ollama pull llama3:8b
docker exec -it ollama ollama pull qwen:7b
docker exec -it ollama ollama pull deepseek-r1:8b
docker exec -it ollama ollama pull mistral:7b
```

### المراقبة والصيانة

#### 1. مراقبة الأداء

- استخدام Prometheus لجمع مقاييس الأداء
- استخدام Grafana لعرض لوحات المراقبة
- إعداد تنبيهات للمشاكل الحرجة

#### 2. النسخ الاحتياطي

- نسخ احتياطي يومي لقاعدة البيانات
- نسخ احتياطي أسبوعي للملفات
- الاحتفاظ بالنسخ الاحتياطية لمدة 30 يومًا

#### 3. التحديثات

- تحديث النماذج بانتظام
- تحديث المكتبات والتبعيات
- اختبار التحديثات في بيئة مرحلية قبل النشر

## تطوير وتوسيع النظام

### إضافة ميزات جديدة

لإضافة ميزة جديدة، اتبع الخطوات التالية:

1. **إضافة صفحة جديدة**:
   - إنشاء ملف جديد في مجلد `src/pages`
   - إضافة المسار في `src/App.tsx`
   - إضافة عنصر قائمة في `src/components/layout/sider.tsx`

2. **إضافة خدمة جديدة**:
   - إنشاء ملف جديد في مجلد `src/services`
   - تنفيذ الوظائف اللازمة
   - تصدير الخدمة لاستخدامها في الصفحات

3. **إضافة ترجمات**:
   - إضافة مفاتيح الترجمة في `src/contexts/i18n.tsx`
   - إضافة الترجمات باللغتين العربية والإنجليزية

### تخصيص المكونات

يمكن تخصيص مكونات الواجهة من خلال:

1. **تعديل السمة**:
   - تعديل متغيرات CSS في `src/index.css`
   - تعديل تكوين Tailwind في `tailwind.config.js`

2. **تخصيص مكونات Ant Design**:
   - تعديل تكوين ConfigProvider في `src/App.tsx`

```typescript
<ConfigProvider
  theme={{
    algorithm: actualTheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      colorPrimary: getPrimaryColor(),
      borderRadius: 8,
      fontFamily: "Cairo, Roboto, sans-serif",
    },
  }}
>
  {/* ... */}
</ConfigProvider>
```

### تحديث النماذج

لتحديث أو إضافة نماذج جديدة:

1. **تحميل النموذج الجديد**:
   ```bash
   docker exec -it ollama ollama pull new-model:tag
   ```

2. **تحديث خدمة اختيار النموذج**:
   ```typescript
   // services/ai_model.ts
   function selectModel(taskType: string) {
     // إضافة النموذج الجديد
     if (taskType === "new-task") {
       return Ollama(model="new-model:tag");
     }
     // ... باقي المنطق
   }
   ```

3. **تحديث واجهة إعدادات الذكاء الاصطناعي**:
   - إضافة النموذج الجديد في قائمة النماذج المتاحة
   - إضافة إعدادات خاصة بالنموذج الجديد

## الملحقات

### مسرد المصطلحات

- **RAG (Retrieval-Augmented Generation)**: تقنية تجمع بين استرجاع المعلومات وتوليد النصوص لتحسين دقة الاستجابات.
- **Embedding**: تمثيل النصوص كمتجهات رقمية في فضاء متعدد الأبعاد.
- **Vector Database**: قاعدة بيانات متخصصة في تخزين واسترجاع المتجهات.
- **Token**: وحدة معالجة النصوص في نماذج الذكاء الاصطناعي.
- **Fine-tuning**: عملية تحسين نموذج ذكاء اصطناعي عام لمهمة محددة.
- **Prompt Engineering**: تقنية صياغة المدخلات لنماذج الذكاء الاصطناعي للحصول على أفضل النتائج.
- **Row Level Security (RLS)**: تقنية أمان تتيح التحكم في الوصول إلى البيانات على مستوى الصف.

### المراجع

1. [Refine.dev Documentation](https://refine.dev/docs/)
2. [Supabase Documentation](https://supabase.com/docs)
3. [Qdrant Documentation](https://qdrant.tech/documentation/)
4. [LangChain Documentation](https://python.langchain.com/docs/get_started/introduction)
5. [Ollama Documentation](https://ollama.ai/docs)
6. [Ant Design Documentation](https://ant.design/docs/react/introduce)
7. [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### سجل التغييرات

#### الإصدار 1.0.0 (2024-03-01)

- الإصدار الأولي للنظام
- دعم المستخدمين والمحادثات والاستشهادات القانونية
- دعم الأدوار المختلفة (مسؤول، مشرف، مشترك، مستخدم)
- دعم السمات الفاتحة والداكنة
- دعم اللغتين العربية والإنجليزية

#### الإصدار 1.1.0 (2024-03-15)

- إضافة صفحة مراقبة المحتوى
- إضافة صفحة إدارة RAG
- إضافة صفحة إدارة الاشتراكات
- تحسين نظام الألوان في السمة الداكنة

#### الإصدار 1.2.0 (2024-04-01)

- إضافة صفحة إعدادات الذكاء الاصطناعي
- إضافة صفحة باقات الاشتراك
- تحسين نظام الأدوار والصلاحيات
- تحسين واجهة المستخدم وتجربة المستخدم
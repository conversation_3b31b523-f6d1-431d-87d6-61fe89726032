<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# دليل ربط الدفع والاشتراكات في منظومة Refine.dev — Chat Interface

يستعرض هذا الدليل أفضل الممارسات لدمج بوابة **Moyasar** ومعالجة الاشتراكات داخل بنية تجمع بين React (Vite + TypeScript + Tailwind) للواجهة، و Refine.dev للـ Admin Dashboard، و FastAPI لخدمات الـ Backend، و Supabase للقاعدة والمصادقة، مع تشغيل نماذج الذكاء على RunPod. الهدف هو إنشاء مسار مالي آمن وسلس للمستخدمين والمشرفين مع أقل جهد صيانة وأقصى قابلية توسّع.

## 1. نظرة معمارية رفيعة

| الطبقة | التقنية | دورها في مسار الدفع |
| :-- | :-- | :-- |
| Chat Interface (React) | React 18 + Vite + Tailwind | عرض باقات الاشتراك، إظهار نموذج Moyasar، إرسال `paymentId` إلى API |
| Admin Dashboard | إنشاء الباقات، ضبط الأسعار والحدود، رؤية تقارير الدفع |
| Backend API | FastAPI | -  إنشاء جلسة دفع <br>-  التحقق من الدفع <br>-  تحديث Supabase <br>-  استقبال Webhooks |
| Data Store | Supabase (Postgres + Auth) | تخزين المستخدمين، الخطط، المدفوعات، تطبيق RLS |
| ML Serving | RunPod | لا علاقة مباشرة بالدفع لكنه يقرأ حدود الخطة من Supabase |

```mermaid
sequenceDiagram
    participant UI as Chat UI
    participant FE as Refine Admin
    participant BE as FastAPI
    participant MS as Moyasar
    participant SB as Supabase
    UI->>BE: POST /payments (plan_id)
    BE->>MS: إنشاء Payment (Secret Key)
    MS-->>UI: Form Config (Publishable Key)
    UI->>MS: بيانات البطاقة
    MS-->>BE: Webhook payment_paid
    BE->>SB: UPDATE subscriptions SET active=true
    SB-->>FE: إظهار حالة الاشتراك
```


## 2. إعداد حساب Moyasar

1. أنشئ حساباً على لوحة **Moyasar**.
2. احصل على مفاتيح **Publishable** و **Secret** من قسم *API Keys* [^1].
3. أضف عنوان **Webhook** لخادمك مع *Secret Token* للتحقق [^2].

## 3. دمج نموذج الدفع في Chat Interface

### 3.1 تضمين مكتبة Moyasar

```html
<!-- داخل index.html -->
<link rel="stylesheet" href="https://unpkg.com/[email protected]/dist/moyasar.css" />
<script src="https://unpkg.com/[email protected]/dist/moyasar.umd.js"></script>
```


### 3.2 مكوّن React جاهز

```tsx
// src/components/payments/Checkout.tsx
import { useEffect } from "react";

interface CheckoutProps {
  amount: number;         // بالهللة
  description: string;
  callbackUrl: string;
  publishableKey: string; // pk_test_xxx
}

export const Checkout = (props: CheckoutProps) => {
  useEffect(() => {
    if ((window as any).Moyasar) {
      (window as any).Moyasar.init({
        element: ".mysr-form",
        amount: props.amount,
        currency: "SAR",
        description: props.description,
        publishable_api_key: props.publishableKey,
        callback_url: props.callbackUrl,
        methods: ["creditcard"],
        on_completed: (payment: any) => {
          fetch("/api/verify-payment", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ paymentId: payment.id }),
          });
        }
      });
    }
  }, []);

  return <div className="mysr-form w-full" />;
};
```

> **نصائح:**
> * `amount` يجب أن يكون بالهللة (مثل 199 ريال = 19900) [^3].
> * استدعاء `on_completed` قبل التحويل لـ 3-D Secure يقلل فقد البيانات في حال انقطاع الاتصال [^1].

## 4. طبقة FastAPI — إنشاء والتحقق من المدفوعات

### 4.1 إنشاء جلسة دفع

```python
# routes/payments.py
from fastapi import APIRouter, Depends
import httpx, os, supabase

router = APIRouter()
MS_SECRET = os.getenv("MOYASAR_SECRET")
SB = supabase.create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_KEY"))

@router.post("/payments")
async def create_payment(plan_id: str, user=Depends(current_user)):
    plan = SB.table("plans").select("*").eq("id", plan_id).execute().data[^0]
    payload = {
        "amount": plan["price_halala"],
        "currency": "SAR",
        "description": f"{plan['name']} plan",
        "callback_url": f"{os.getenv('PUBLIC_URL')}/payment-callback",
        "source": {"type": "creditcard", "3ds": True}
    }
    async with httpx.AsyncClient(auth=(MS_SECRET, "")) as client:
        r = await client.post("https://api.moyasar.com/v1/payments", json=payload)
    payment = r.json()
    SB.table("payments").insert({"id": payment["id"], "user_id": user.id, "plan_id": plan_id, "status": payment["status"]}).execute()
    return {"payment_id": payment["id"], "publishable_key": os.getenv("MOYASAR_PK")}
```


### 4.2 Webhook التحقق النهائي

```python
@router.post("/webhook/moyasar")
async def moyasar_webhook(request: Request):
    token = request.headers.get("Moyasar-Token")
    assert token == os.getenv("MOYASAR_WEBHOOK_TOKEN")
    body = await request.json()
    if body["status"] == "paid":
        SB.table("subscriptions").upsert({
            "user_id": body["metadata"]["user_id"],
            "plan_id": body["metadata"]["plan_id"],
            "expires_at": get_expiry(body["metadata"]["plan_id"])
        }).execute()
    return {"received": True}
```


## 5. إدارة الاشتراكات في Supabase

### 5.1 مخطط الجداول

```sql
create table plans (
  id uuid primary key,
  name text,
  price_halala int,
  token_limit int,
  voice_minutes int
);

create table subscriptions (
  user_id uuid references auth.users(id),
  plan_id uuid references plans(id),
  expires_at timestamptz,
  primary key (user_id, plan_id)
);

create policy "User can select own subs"
  on subscriptions for select using (auth.uid() = user_id);
```


### 5.2 من Admin Dashboard

```tsx
// resources/subscriptions.tsx
import { List, Datagrid, TextField, DateField, NumberField } from "@refinedev/antd";
export const SubscriptionList = () => (
  <List>
    <Datagrid rowKey="user_id">
      <TextField source="user_email" label="المستخدم"/>
      <TextField source="plan_name" label="الخطة"/>
      <DateField source="expires_at" label="تاريخ الانتهاء"/>
      <NumberField source="token_used" label="توكن مستهلك"/>
    </Datagrid>
  </List>
);
```


## 6. الأمان والامتثال

| إجراء | الغرض |
| :-- | :-- |
| **TLS 1.3** | تشفير كل الاتصالات مع Moyasar وواجهة المستخدم |
| **عدم تخزين بيانات البطاقة** | جميع بيانات البطاقات تبقى في إطار مكتبة Moyasar (امتثال PCI-DSS) [^1] |
| **توقيع Webhook** | مقارنة `Moyasar-Token` مع متغير بيئة آمن [^2] |
| **Row-Level Security** | عزل بيانات المدفوعات لكل مستخدم في Supabase [^4] |
| **قياس المعدل** | حد ٥ محاولات دفع/دقيقة لكل IP على FastAPI |

## 7. تصميم تجربة مستخدم سلسة

1. **صفحة خطط واضحة**: بيّن حدود التوكن والصوت لكل خطة.
2. **زر ترقية مباشر**: يستدعي `Checkout` داخل Modal لمنع الخروج من المحادثة.
3. **معالجة الأخطاء لحظياً**: في حالة فشل الدفع أظهر سبب الفشل القادم من Moyasar [^3].
4. **تحديث فوري للحالة**: استمع لـ subscription channel من Supabase واحدث الـ UI لحظياً بعد نجاح الدفع.
5. **إشعار انتهاء الاشتراك**: Cron في Supabase Functions يرسل بريد/Toast قبل ٧ أيام من الانتهاء.

## 8. خطوات تنفيذ سريعة

| الخطوة | وصف مختصر |
| :-- | :-- |
| 1 | تفعيل مفاتيح Moyasar وربط Webhook |
| 2 | إنشاء جداول `plans`, `subscriptions`, `payments` في Supabase مع RLS |
| 3 | بناء نقاط `/payments` و`/verify-payment` و`/webhook/moyasar` في FastAPI |
| 4 | إضافة مكوّن `Checkout` في Chat Interface وربطه بـ API |
| 5 | إنشاء موارد Refine.dev لإدارة الخطط والمدفوعات |
| 6 | اختبار سيناريوهات النجاح/الفشل في بيئة Moyasar Sandbox |
| 7 | تهيئة مراقبة Grafana لعدد المدفوعات وفشلها |

## 9. خاتمة

باتباع هذا المخطط ستتمكن من:

* قبول مدفوعات بطاقات مدى/فيزا/ماستركارد عبر واجهة المحادثة بدون مغادرة التطبيق [^1][^3].
* تفعيل الاشتراكات وتحديث حدود الاستخدام فورياً بفضل Webhooks وSupabase RLS [^4][^2].
* إدارة الخطط والأسعار بسهولة من لوحة Refine.dev مع إمكان التوسع إلى خطط جديدة أو بوابات دفع إضافية دون تغييرات جذرية في الكود.

بهذا تضمن تجربة دفع موثوقة للمستخدم، وأماناً متوافقاً مع معايير PCI-DSS، وهيكلاً قابلاً للصيانة والنمو.

<div style="text-align: center">⁂</div>

[^1]: https://docs.moyasar.com/credit-card

[^2]: https://help.moyasar.com/en/article/moyasar-dashboard-webhooks-44p8en/

[^3]: https://docs.moyasar.com/guides/card-payments/basic-integration/

[^4]: https://supabase.com/docs/guides/auth

[^5]: architecture-plan.md

[^6]: https://www.dhiwise.com/post/the-ultimate-tutorial-for-integration-of-react-stripe-checkout

[^7]: https://app.studyraid.com/en/read/13578/455443/understanding-subscription-lifecycle

[^8]: https://www.make.com/en/integrations/payment-rails/refiner?fromImt=1

[^9]: https://www.ignek.com/blog/building-secure-payment-gateways-with-node-js/

[^10]: https://www.reddit.com/r/reactjs/comments/uf0lr4/best_payment_api_for_reactjs_which_is_it/

[^11]: https://www.npmjs.com/package/@noxy/react-subscription-hook

[^12]: https://ecommpay.com/core-functionalities/integrations/api-sdk/

[^13]: https://help.trustpayments.com/hc/en-us/sections/************

[^14]: https://www.angularminds.com/blog/integrating-secure-payment-gateways-in-react

[^15]: https://www.webdevtutor.net/blog/typescript-subscription-complete

[^16]: https://github.com/rakeshpuppala2590/FastAPI-Cloud-Service-Management

[^17]: https://react.dev/learn/typescript

[^18]: https://flowxo.com/features/accept-payments-via-your-chatbot/

[^19]: https://supabase.com/docs/guides/functions/auth

[^20]: https://getlazy.ai/templates/stripe-api-create-subscription-c018d7d6-1280-432b-8bf7-2973780ee8fc

[^21]: https://stackoverflow.com/questions/57494592/can-i-use-third-party-libraries-with-react-and-typescript/57494803

[^22]: https://www.codementor.io/@dhananjaykumar/pms-part-3-payment-gateway-integration-15fercrfvy

[^23]: https://www.reddit.com/r/FlutterFlow/comments/16srocw/supabase_payment_methods/

[^24]: https://github.com/kirillzhosul/subscriby

[^25]: https://stackoverflow.com/questions/46767120/how-to-structure-multiple-react-apps-and-still-share-code-assets

[^26]: https://blog.cricpayz.io/payment-gateway-react/payment-gateway/

[^27]: https://dev.to/moayad523/implementing-a-pub-sub-based-state-management-tool-in-react-3e2l

[^28]: https://www.chatcompose.com/payments.html

[^29]: https://www.uxpin.com/studio/blog/integrating-react-components-with-design-patterns/

[^30]: https://www.bacancytechnology.com/blog/integrate-paytm-payment-gateway-using-reactjs

[^31]: https://www.packtpub.com/en-us/product/micro-state-management-with-react-hooks-9781801812375/chapter/chapter-5-sharing-component-state-with-context-and-subscription-7/section/implementing-the-context-and-subscription-pattern-ch07lvl1sec36

[^32]: https://stackoverflow.com/questions/50677333/integrating-payments-into-chatbot

[^33]: https://martinfowler.com/articles/modularizing-react-apps.html

[^34]: https://innostax.com/stripe-payment-gateway-integration-with-reactjs-and-nodejs/

[^35]: https://docs.moyasar.com/hosted-checkout

[^36]: https://github.com/hamedov93/omnipay-moyasar

[^37]: https://wordpress.org/plugins/moyasar/

[^38]: https://docs.moyasar.com/api/authentication/

[^39]: https://announcekit.app/moyasar/announcements/payment-webhooks-JaHbG

[^40]: https://docs.moyasar.com/guides/apple-pay/basic-integration/

[^41]: https://docs.moyasar.com/sdk/react-native/basic-integration/


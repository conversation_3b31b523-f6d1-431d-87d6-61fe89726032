<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# نظام إدارة الشات بوت الذكي باستخدام Refine.dev

## دليل تنفيذي شامل للمكونات والتقنيات الحديثة

### 1. المقدمة والأهداف

نظام متكامل لإدارة شات بوتات الذكاء الاصطناعي يعتمد على:

- **Refine.dev** كإطار عمل رئيسي للواجهة
- نماذج مفتوحة المصدر (Llama 3.2، Qwen 2.5)
- بنية RAG مع Qdrant وLangChain
- نظام اشتراكات متعدد المستويات

```mermaid
graph TD
    A[واجهة Refine.dev] --> B[خدمات الذكاء الاصطناعي]
    B --> C[إدارة الاشتراكات]
    B --> D[مراقبة الجودة]
    A --> E[التخصيص البصري]
```


### 2. المكونات الأساسية

#### 2.1 واجهة الإدارة (React/Refine)

```jsx
// src/components/Dashboard.jsx
export default function Dashboard() {
  return (
    <Refine 
      authProvider={authProvider}
      dataProvider={dataProvider}
      resources={[
        { name: "conversations", list: ConversationList },
        { name: "subscriptions", list: SubscriptionManager },
        { name: "content-review", list: ContentReviewQueue }
      ]}
    />
  )
}
```


#### 2.2 نظام الذكاء الاصطناعي

**النماذج المحلية (Ollama):**

```python
# services/ai_model.py
from langchain_community.llms import Ollama

llama_model = Ollama(model="llama3:8b")
qwen_model = Ollama(model="qwen:7b")

def generate_response(prompt):
    # دمج RAG مع Qdrant
    context = qdrant_retriever.retrieve(prompt)
    augmented_prompt = f"{context}\n\n{prompt}"
    return llama_model.invoke(augmented_prompt)
```


#### 2.3 نظام الاستشهادات

```python
# services/citation_service.py
def generate_citations(response, sources):
    citations = []
    for source in sources:
        citations.append({
            "id": source.metadata['doc_id'],
            "title": source.metadata['title'],
            "url": source.metadata['url'],
            "excerpt": source.text[:200] + "..."
        })
    return {
        "response": response,
        "citations": citations
    }
```


### 3. البنية التحتية

#### 3.1 مخطط معماري

```mermaid
graph LR
    A[المستخدم] --> B[واجهة Refine]
    B --> C[NestJS BFF]
    C --> D[خدمة RAG]
    D --> E[Qdrant VectorDB]
    D --> F[Ollama LLMs]
    C --> G[Supabase]
    G --> H[بيانات الاشتراكات]
    G --> I[سجل المحادثات]
```


#### 3.2 تكامل Supabase

```sql
-- جدول إدارة الاشتراكات
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    plan VARCHAR(20) CHECK(plan IN ('free','basic','pro','enterprise')),
    tokens_used INT DEFAULT 0,
    tokens_limit INT,
    expires_at TIMESTAMPTZ
);

-- إشعار التجديد
CREATE FUNCTION check_subscription_expiry()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.expires_at < NOW() + INTERVAL '7 days' THEN
        INSERT INTO notifications(user_id, message)
        VALUES (NEW.user_id, 'اشتراكك ينتهي خلال 7 أيام');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```


### 4. إدارة الجودة

#### 4.1 نظام المراجعة

```jsx
// src/pages/ContentReview.jsx
const ContentReviewQueue = () => {
  const { data: reports } = useList({
    resource: "reported-content",
    filters: [{ field: "status", operator: "eq", value: "pending" }]
  });

  return (
    <Table dataSource={reports}>
      <Column title="المستخدم" dataIndex="user_name" />
      <Column title="السبب" dataIndex="reason" />
      <Column title="الإجراء" render={(_, record) => (
        <Space>
          <Button onClick={() => handleApprove(record.id)}>موافقة</Button>
          <Button danger onClick={() => handleReject(record.id)}>رفض</Button>
        </Space>
      )} />
    </Table>
  )
}
```


#### 4.2 مسار "عدم الإعجاب"

```python
# services/reporting_service.py
def handle_dislike(conversation_id, reason):
    # إضافة للتقييم البشري
    supabase.table('reported_content').insert({
        'conversation_id': conversation_id,
        'reason': reason,
        'status': 'pending'
    }).execute()
    
    # إشعار المشرفين
    redis.publish('review-channel', json.dumps({
        'type': 'new_report',
        'data': conversation_id
    }))
```


### 5. إدارة الاشتراكات

#### 5.1 نماذج الاشتراكات

```jsx
// src/components/SubscriptionPlans.jsx
const PLANS = [
  {
    name: "المجاني",
    features: [
      "٥٠٠٠ توكن شهرياً",
      "لا يوجد محادثة صوتية",
      "نماذج أساسية فقط"
    ]
  },
  {
    name: "المتقدم",
    features: [
      "٢٠٠٠٠٠ توكن شهرياً",
      "محادثة صوتية متكاملة",
      "جميع النماذج المتاحة"
    ]
  }
];

const SubscriptionCard = ({ plan }) => (
  <Card title={plan.name}>
    <ul>
      {plan.features.map((feat, idx) => (
        <li key={idx}>{feat}</li>
      ))}
    </ul>
  </Card>
);
```


#### 5.2 نظام الفلترة

```sql
-- استعلام المشتركين المنتهية
SELECT * FROM subscriptions 
WHERE expires_at < NOW() 
  AND status = 'active';

-- المشتركين القريبين للانتهاء
SELECT * FROM subscriptions 
WHERE expires_at BETWEEN NOW() AND NOW() + INTERVAL '7 days'
  AND status = 'active';
```


### 6. التخصيص البصري

#### 6.1 نظام الثيمات

```jsx
// src/context/ThemeContext.jsx
const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState({
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    fontFamily: 'Segoe UI, Tahoma'
  });

  const updateTheme = (newTheme) => {
    setTheme({ ...theme, ...newTheme });
    // تطبيق التغييرات فورياً
    document.documentElement.style.setProperty('--primary', newTheme.primaryColor);
    document.documentElement.style.setProperty('--secondary', newTheme.secondaryColor);
    document.body.style.fontFamily = newTheme.fontFamily;
  };

  return (
    <ThemeContext.Provider value={{ theme, updateTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```


### 7. الأمان والامتثال

#### 7.1 طبقة المصادقة

```ts
// src/authProvider.ts
const authProvider: AuthProvider = {
  login: async ({ username, password }) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: username,
      password
    });

    if (error) throw error;
    
    localStorage.setItem('auth', JSON.stringify(data.session));
    return Promise.resolve();
  },
  check: () => 
    localStorage.getItem('auth') ? Promise.resolve() : Promise.reject(),
  // ...وظائف أخرى
};
```


#### 7.2 سياسات الوصول

```sql
-- سياسات أمان مستوى الصف (Supabase RLS)
CREATE POLICY subscription_access_policy 
ON subscriptions 
FOR SELECT USING (
  auth.uid() = user_id
  OR current_user = 'admin'
);
```


### 8. خارطة التنفيذ

| المرحلة | المدة | المخرجات |
| :-- | :-- | :-- |
| البنية التحتية | أسبوعان | Ollama، Qdrant، Supabase |
| النواة الرئيسية | 3 أسابيع | واجهة الإدارة، RAG، نظام الاشتراكات |
| أنظمة الجودة | أسبوعان | المراقبة، الاستشهادات، التقييم |
| التخصيص | أسبوع | الثيمات، الإعدادات |

### 9. إدارة المخاطر

```mermaid
graph TD
    A[مخاطر الأداء] --> B[Autoscaling للنماذج]
    C[مخاطر الأمان] --> D[تدوير المفاتيح الشهري]
    E[محتوى غير مناسب] --> F[التدقيق البشري + تحديث النماذج]
```


### 10. المراقبة والتحليلات

```python
# monitoring/dashboard.py
def generate_performance_report():
    return {
        "active_users": supabase.query("SELECT COUNT(*) FROM sessions WHERE active"),
        "avg_response_time": logs_service.calculate_avg_response(),
        "subscription_renewals": payment_service.get_renewal_stats()
    }
```

**ملاحظات التنفيذ النهائية:**

1. جميع المكونات متكاملة مع Refine.dev عبر وحدات `dataProvider` و `authProvider`
2. النماذج تعمل محلياً عبر Ollama مع عزل كامل
3. نظام RAG يستخدم Qdrant للفهرسة المتجهية وLangChain للسلاسل
4. إدارة الاشتراكات تعتمد على Supabase مع سياسات أمان RLS
5. التدقيق البشري يتم عبر طابور Redis مع واجهة مراقبة مخصصة

يُعد هذا الدليل مرجعاً تنفيذياً كاملاً جاهزاً للتسليم لفرق التطوير في bolt.new.


# 🎨 دليل مكونات التصميم - شُريح

<div align="center">
  <img src="public/ShuraihAIUI.svg" alt="شُريح" width="120" height="120">
  
  **دليل شامل لمكونات واجهة المستخدم القابلة لإعادة الاستخدام**
  
  **الإصدار 2.0.0 | يناير 2025**
</div>

---

## 📋 جدول المحتويات

- [نظرة عامة](#-نظرة-عامة)
- [الهوية البصرية](#-الهوية-البصرية)
- [نظام المكونات](#-نظام-المكونات)
- [مكونات المحادثة](#-مكونات-المحادثة)
- [مكونات الشريط الجانبي](#-مكونات-الشريط-الجانبي)
- [مكونات الإعدادات](#-مكونات-الإعدادات)
- [مكونات الاستشهادات القانونية](#-مكونات-الاستشهادات-القانونية)
- [مكونات واجهة المستخدم الأساسية](#-مكونات-واجهة-المستخدم-الأساسية)
- [دمج المكونات في لوحة التحكم](#-دمج-المكونات-في-لوحة-التحكم)
- [أفضل الممارسات](#-أفضل-الممارسات)
- [استكشاف الأخطاء](#-استكشاف-الأخطاء)

---

## 🌟 نظرة عامة

هذا الدليل يوثق جميع مكونات التصميم المستخدمة في واجهة شُريح، ويشرح كيفية إعادة استخدامها في لوحة التحكم (Admin Dashboard) أو أي تطبيقات أخرى. المكونات مصممة لتكون:

- **قابلة لإعادة الاستخدام**: يمكن استخدامها في أي جزء من التطبيق
- **قابلة للتخصيص**: تقبل خصائص مختلفة لتغيير المظهر والسلوك
- **متوافقة مع السمات**: تعمل مع المظهر الفاتح والداكن
- **متعددة اللغات**: تدعم العربية والإنجليزية
- **متجاوبة**: تعمل على جميع أحجام الشاشات

---

## 🎨 الهوية البصرية

### نظام الألوان

```css
/* اللون الأزرق الرئيسي */
--color-primary: 38 53 237;  /* #2635ED */

/* المظهر الفاتح */
--color-background: 255 255 255;  /* أبيض نقي */
--color-foreground: 15 23 42;     /* أسود داكن للنصوص */
--color-card: 255 255 255;        /* أبيض للكروت */
--color-muted: 241 245 249;       /* رمادي فاتح */
--color-border: 226 232 240;      /* رمادي فاتح للحدود */

/* المظهر الداكن - استبدال الأزرق بالرمادي */
.dark {
  --color-background: 16 16 16;    /* خلفية داكنة */
  --color-foreground: 224 224 224; /* نص فاتح */
  --color-card: 23 23 23;          /* كروت داكنة */
  --color-primary: 38 38 38;       /* رمادي بدلاً من الأزرق */
  --color-border: 38 38 38;        /* حدود داكنة */
}
```

### الخطوط والتايبوغرافي

```css
/* الخطوط */
--font-family: 'Cairo', 'Roboto', sans-serif;

/* أحجام الخطوط */
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-base: 1rem;    /* 16px */
--font-size-lg: 1.125rem;  /* 18px */
--font-size-xl: 1.25rem;   /* 20px */
--font-size-2xl: 1.5rem;   /* 24px */

/* أوزان الخطوط */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### الحدود والظلال

```css
/* الحدود */
--border-radius-sm: 0.25rem;   /* 4px */
--border-radius-md: 0.375rem;  /* 6px */
--border-radius-lg: 0.5rem;    /* 8px */
--border-radius-xl: 0.75rem;   /* 12px */
--border-radius-2xl: 1rem;     /* 16px */
--border-radius-3xl: 1.5rem;   /* 24px */
--border-radius-full: 9999px;

/* الظلال */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
```

---

## 🧩 نظام المكونات

### هيكل المكونات

```typescript
// نموذج لبنية المكون
interface ComponentProps {
  className?: string;       // للتخصيص الإضافي
  children?: React.ReactNode; // المحتوى الداخلي
  variant?: string;         // متغيرات المكون
  size?: 'sm' | 'md' | 'lg' | 'xl'; // الحجم
  disabled?: boolean;       // حالة التعطيل
  onClick?: () => void;     // معالج الأحداث
}

// استخدام cn() لدمج الفئات
import { cn } from '@/lib/utils';

export const Component: React.FC<ComponentProps> = ({
  className,
  children,
  variant = 'default',
  size = 'md',
  disabled = false,
  onClick
}) => {
  return (
    <div 
      className={cn(
        "base-classes",
        variant === 'primary' && "primary-classes",
        size === 'lg' && "large-classes",
        disabled && "disabled-classes",
        className
      )}
      onClick={disabled ? undefined : onClick}
    >
      {children}
    </div>
  );
};
```

### استخدام المكونات مع السمات

```typescript
// مثال لمكون يدعم السمات
import { useTheme } from '@/lib/theme';

export const ThemedComponent: React.FC<Props> = (props) => {
  const { actualTheme } = useTheme();
  
  return (
    <div className={cn(
      "base-component",
      actualTheme === 'dark' ? "dark-variant" : "light-variant"
    )}>
      {/* محتوى المكون */}
    </div>
  );
};
```

### استخدام المكونات مع الترجمة

```typescript
// مثال لمكون يدعم الترجمة
import { useTranslation } from '@/lib/i18n';

export const TranslatedComponent: React.FC<Props> = (props) => {
  const { t, isRTL } = useTranslation();
  
  return (
    <div dir={isRTL ? 'rtl' : 'ltr'} className="component">
      <h2>{t('component.title')}</h2>
      <p>{t('component.description')}</p>
    </div>
  );
};
```

---

## 💬 مكونات المحادثة

### 1. منطقة المحادثة (ChatArea)

```typescript
// src/components/ChatArea.tsx
interface ChatAreaProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  sendError?: boolean;
}

export const ChatArea: React.FC<ChatAreaProps> = ({ 
  onSendMessage, 
  isLoading = false,
  sendError = false 
}) => {
  // منطق المكون
  
  return (
    <div className="flex-1 flex flex-col bg-background relative">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-background border-b border-border">
        {/* قائمة النماذج المنسدلة */}
        <ModelsDropdown onModelChange={handleModelChange} />
        
        {/* أزرار الإجراءات */}
        <div className="flex items-center gap-3">
          <ThemeToggle variant="button" />
          <LanguageSwitcher variant="toggle" showLabel={false} />
          <button className="px-4 py-2 text-sm text-muted-foreground border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors">
            {t('common.share')}
          </button>
          <button className="px-4 py-2 text-sm text-primary-foreground bg-primary rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 shadow-sm">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            {t('navigation.newChat')}
          </button>
        </div>
      </div>
      
      {/* محتوى المحادثة */}
      <div className="flex-1 flex flex-col items-center justify-center p-8 bg-background relative">
        {/* رسالة الترحيب */}
        <GreetingMessage userName="المستخدم" />
        
        {/* شعار وترحيب */}
        <div className="text-center mb-12">
          <LogoComponent size="xl" className="w-32 h-32 mx-auto mb-6" />
          <h1 className="text-2xl font-medium text-foreground mb-3">
            {t('chat.welcome')}, (user)
          </h1>
          <p className="text-xl text-primary font-medium">
            {t('chat.howCanIHelp')}
          </p>
        </div>
        
        {/* حقل الإدخال */}
        <div className="w-full max-w-4xl mb-8">
          <ChatInput 
            onSendMessage={onSendMessage}
            isLoading={isLoading}
            isError={sendError}
            placeholder={t('chat.typeMessage')}
          />
        </div>
        
        {/* الأسئلة المقترحة */}
        <SuggestedQuestions onQuestionClick={onSendMessage} />
      </div>
    </div>
  );
};
```

### 2. حقل الإدخال (ChatInput)

```typescript
// src/components/ChatInput.tsx
interface ChatInputProps {
  onSendMessage: (message: string, attachments?: File[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
  isError?: boolean;
  placeholder?: string;
  maxLength?: number;
  onAttachFile?: () => void;
  onVoiceRecord?: () => void;
  supportedFileTypes?: string[];
  maxFileSize?: number;
  maxFiles?: number;
}

export const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false,
  isLoading = false,
  isError = false,
  placeholder,
  maxLength = 2000,
  onAttachFile,
  onVoiceRecord,
  supportedFileTypes = ['image/*', 'text/*', 'application/pdf'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 5
}) => {
  // منطق المكون
  
  return (
    <form onSubmit={handleSubmit} className="relative">
      {/* عرض الملفات المرفقة */}
      {attachedFiles.length > 0 && (
        <div className="mb-6 p-5 bg-muted/50 rounded-2xl border border-border">
          {/* محتوى الملفات المرفقة */}
        </div>
      )}
      
      {/* حقل الإدخال الرئيسي */}
      <div className="relative bg-card rounded-3xl shadow-md transition-all duration-300 border border-border hover:shadow-lg min-h-[120px]">
        {/* منطقة النص */}
        <div className="flex-1 px-6 pt-6 pb-4">
          <AutoResizeTextarea
            ref={textareaRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder || t('chat.typeMessage')}
            disabled={disabled}
            maxLength={maxLength}
            minHeight={80}
            maxHeight={200}
            className="w-full bg-transparent text-lg resize-none border-0 p-0 text-foreground"
          />
        </div>
        
        {/* شريط الأدوات */}
        <div className="flex items-center justify-between px-6 py-4">
          {/* أزرار الإجراءات اليسرى */}
          <div className="flex items-center gap-3">
            {/* زر إرفاق الملفات */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled || attachedFiles.length >= maxFiles}
                className="p-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed"
                title={t('chat.file')}
              >
                <Paperclip className="w-5 h-5" />
              </button>
              <span className="text-xs text-muted-foreground">{t('chat.file')}</span>
            </div>
            
            {/* زر التسجيل الصوتي */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="button"
                onClick={handleVoiceRecord}
                disabled={disabled}
                className="p-3 rounded-xl transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed"
                title={isRecording ? t('chat.stopRecording') : t('chat.voice')}
              >
                <Mic className="w-5 h-5" />
              </button>
              <span className="text-xs text-muted-foreground">
                {isRecording ? t('chat.stop') : t('chat.voice')}
              </span>
            </div>
          </div>
          
          {/* عداد الأحرف وزر الإرسال */}
          <div className="flex items-center gap-4">
            {/* عداد الأحرف */}
            {showCharacterCount && (
              <div className="text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full">
                {inputValue.length}/{maxLength}
              </div>
            )}
            
            {/* زر الإرسال */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="submit"
                disabled={!canSend}
                className="p-3 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed shadow-sm bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md active:scale-95"
                title={isLoading ? t('chat.sending') : t('chat.send')}
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
              <span className="text-xs text-muted-foreground">
                {isLoading ? t('chat.sending') : t('chat.send')}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* حقل إدخال الملفات المخفي */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={supportedFileTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />
      
      {/* رسائل الحالة */}
      <div className="mt-4 space-y-3">
        {isError && (
          <div className="text-sm text-destructive flex items-start gap-3 bg-destructive/10 p-4 rounded-xl border border-destructive/20">
            <span>{t('chat.error')}</span>
          </div>
        )}
      </div>
    </form>
  );
};
```

### 3. رسالة المحادثة (ChatMessage)

```typescript
// src/components/ChatMessage.tsx
interface ChatMessageProps {
  message: Message;
  isAnimating?: boolean;
  onRegenerate?: (messageId: string) => void;
  onLike?: (messageId: string) => void;
  onDislike?: (messageId: string) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  isAnimating = false,
  onRegenerate,
  onLike,
  onDislike
}) => {
  const [copiedMessage, setCopiedMessage] = useState<string | null>(null);
  const [likedMessages, setLikedMessages] = useState<Set<string>>(new Set());
  const [dislikedMessages, setDislikedMessages] = useState<Set<string>>(new Set());
  const [isHovered, setIsHovered] = useState(false);
  
  const isBot = message.sender === 'bot';
  
  // منطق المكون
  
  return (
    <div 
      className={`group mb-6 ${isAnimating ? 'animate-fade-in-up' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* حاوية الرسالة */}
      <div className="flex items-start gap-4">
        {/* الصورة الرمزية */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full flex items-center justify-center">
            {isBot ? (
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center shadow-md">
                <LogoComponent size="md" className="w-6 h-6" />
              </div>
            ) : (
              <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                <span className="text-muted-foreground text-sm font-medium">U</span>
              </div>
            )}
          </div>
        </div>
        
        {/* محتوى الرسالة */}
        <div className="flex-1 min-w-0">
          {/* الرأس */}
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium text-foreground text-sm">
              {isBot ? 'شُريح' : 'المستخدم'}
            </span>
            <span className="text-xs text-muted-foreground">
              {formatTime(message.timestamp, language)}
            </span>
          </div>
          
          {/* فقاعة الرسالة */}
          <div className="bg-card rounded-2xl p-5 shadow-sm border border-border hover:border-border/60 transition-colors chat-bubble">
            <div className="text-card-foreground text-base leading-relaxed whitespace-pre-line">
              {renderMessageContent(message.text, message.has_citations)}
            </div>
          </div>
          
          {/* شريط الأدوات */}
          <div className="flex items-center justify-between mt-3 px-1">
            {/* أزرار الإجراءات */}
            <div className={`flex items-center gap-1 transition-all duration-300 ease-in-out ${
              isHovered 
                ? 'opacity-100 translate-y-0' 
                : 'opacity-0 translate-y-2 pointer-events-none'
            }`}>
              {/* زر النسخ */}
              <button
                onClick={() => handleCopy(message.id, message.text)}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="نسخ"
              >
                <Copy className="h-4 w-4" />
              </button>
              
              {/* أزرار خاصة برسائل البوت */}
              {isBot && (
                <>
                  {/* إعادة التوليد */}
                  <button
                    onClick={handleRegenerate}
                    className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                    title="إعادة إنشاء"
                  >
                    <Undo className="h-4 w-4" />
                  </button>
                  
                  {/* إعجاب */}
                  <button
                    onClick={() => handleLike(message.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      likedMessages.has(message.id)
                        ? 'text-success bg-success/10 hover:bg-success/20'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                    title="إعجاب"
                  >
                    <ThumbsUp className="h-4 w-4" />
                  </button>
                  
                  {/* عدم إعجاب */}
                  <button
                    onClick={() => handleDislike(message.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      dislikedMessages.has(message.id)
                        ? 'text-destructive bg-destructive/10 hover:bg-destructive/20'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                    title="عدم إعجاب"
                  >
                    <ThumbsDown className="h-4 w-4" />
                  </button>
                </>
              )}
              
              {/* مشاركة */}
              <button
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="مشاركة"
              >
                <Share className="h-4 w-4" />
              </button>
            </div>
            
            {/* عداد التوكينز */}
            {isBot && message.tokens && (
              <div className={`text-xs text-muted-foreground font-medium transition-all duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-60'
              }`}>
                {message.tokens} توكين
              </div>
            )}
          </div>
        </div>
      </div>

      {/* إشعار النسخ */}
      {copiedMessage === message.id && (
        <div className="fixed bottom-4 left-4 bg-success text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in">
          <span className="text-sm">تم نسخ الرسالة ✓</span>
        </div>
      )}
    </div>
  );
};
```

### 4. الأسئلة المقترحة (SuggestedQuestions)

```typescript
// src/components/SuggestedQuestions.tsx
interface SuggestedQuestionsProps {
  onQuestionClick: (question: string) => void;
}

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({ onQuestionClick }) => {
  const { t } = useTranslation();
  
  // الحصول على الأسئلة من ملف الترجمة
  const questions = t('suggestions.questions') as string[];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-4xl">
      {questions.map((question, index) => (
        <button
          key={index}
          onClick={() => onQuestionClick(question)}
          className="p-6 text-right bg-card border border-border rounded-2xl hover:border-primary hover:shadow-md transition-all duration-200 text-card-foreground text-base leading-relaxed hover:bg-accent/50"
        >
          {question}
        </button>
      ))}
    </div>
  );
};
```

### 5. رسالة الترحيب (GreetingMessage)

```typescript
// src/components/GreetingMessage.tsx
interface GreetingMessageProps {
  userName?: string;
  className?: string;
}

export const GreetingMessage: React.FC<GreetingMessageProps> = ({ 
  userName = 'المستخدم',
  className 
}) => {
  const { t } = useTranslation();
  const [showGreeting, setShowGreeting] = useState(false);

  // منطق عرض الرسالة مرة واحدة في اليوم
  useEffect(() => {
    const lastShown = localStorage.getItem('lastGreetingDate');
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    if (lastShown !== today) {
      localStorage.setItem('lastGreetingDate', today);
      setShowGreeting(true);
    }
  }, []);

  // الحصول على التحية حسب الوقت
  const getTimeBasedGreeting = (): string => {
    const hour = new Date().getHours();
    
    if (hour >= 5 && hour < 12) {
      return t('greetings.goodMorning');
    } else if (hour >= 12 && hour < 17) {
      return t('greetings.goodAfternoon');
    } else if (hour >= 17 && hour < 21) {
      return t('greetings.goodEvening');
    } else {
      return t('greetings.lateNight');
    }
  };

  if (!showGreeting) return null;

  return (
    <div 
      className={cn(
        "mb-6 p-6 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-2xl shadow-sm animate-fade-in-up",
        className
      )}
    >
      <div className="flex items-start justify-between gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-md">
              <LogoComponent size="lg" className="w-7 h-7" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">
                {getTimeBasedGreeting()}، {userName} 👋
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('greetings.welcomeBack')}
              </p>
            </div>
          </div>
          
          <p className="text-base text-foreground leading-relaxed">
            {t('greetings.dailyMessage')}
          </p>
          
          <div className="mt-4 flex flex-wrap gap-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
              ✨ {t('greetings.features.legal')}
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success/10 text-success">
              🎯 {t('greetings.features.accurate')}
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info/10 text-info">
              ⚡ {t('greetings.features.fast')}
            </span>
          </div>
        </div>
        
        <button
          onClick={() => setShowGreeting(false)}
          className="flex-shrink-0 p-2 text-muted-foreground hover:text-foreground hover:bg-background rounded-lg transition-colors"
          aria-label={t('common.close')}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};
```

### 6. مؤشر الكتابة (TypingIndicator)

```typescript
// src/components/TypingIndicator.tsx
export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-start gap-4 mb-6 animate-fade-in">
      <div className="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-md">
        <LogoComponent size="lg" className="w-7 h-7" />
      </div>
      
      <div className="bg-card px-6 py-4 rounded-2xl border border-border shadow-sm">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
    </div>
  );
};
```

---

## 🧭 مكونات الشريط الجانبي

### 1. الشريط الجانبي (Sidebar)

```typescript
// src/components/Sidebar.tsx
interface SidebarProps {
  isVisible: boolean;
  isMobile: boolean;
  onClose?: () => void;
  onNewChat?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  isVisible, 
  isMobile, 
  onClose,
  onNewChat
}) => {
  const { t } = useTranslation();
  const { expandedSection, isExpanded, toggleSection, closeExpansion } = useSidebarExpansion();

  // عناصر التنقل
  const navigationItems = [
    { icon: Plus, label: t('navigation.newChat'), key: 'newChat', action: 'newChat' },
    { icon: Clock, label: t('navigation.history'), key: 'history', action: 'expand' },
    { icon: Folder, label: t('navigation.folders'), key: 'folders', action: 'expand' },
    { icon: Search, label: t('navigation.search'), key: 'search', action: 'expand' },
  ];

  // عناصر أسفل الشريط
  const bottomItems = [
    { icon: HelpCircle, label: t('navigation.help'), key: 'help', action: 'expand' },
    { icon: Shield, label: 'Admin', key: 'admin', action: 'expand' },
  ];

  // منطق المكون
  
  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && (
        <div 
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40 md:hidden"
          onClick={onClose}
        />
      )}
      
      {/* حاوية الشريط الجانبي */}
      <div className={`
        ${isMobile ? 'fixed right-0 top-0 h-full z-50' : 'relative'} 
        ${isExpanded ? 'w-80' : 'w-20'} bg-card border-l border-border h-full flex sidebar
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0' : 'translate-x-full'}
      `}>
        
        {/* الشريط الجانبي الرئيسي */}
        <div className="w-20 flex flex-col bg-card border-r border-border">
          {/* القسم العلوي */}
          <div className="p-4 flex flex-col items-center">
            {/* الشعار */}
            <div className="w-12 h-12 rounded-xl flex items-center justify-center mb-6">
              <LogoComponent size="lg" className="w-7 h-7" />
            </div>
            
            {/* أيقونات التنقل */}
            <div className="space-y-4">
              {navigationItems.map((item) => (
                <button 
                  key={item.key}
                  onClick={() => handleItemClick(item)}
                  className={cn(
                    "w-12 h-12 flex items-center justify-center rounded-xl transition-all sidebar-item relative",
                    expandedSection === item.key 
                      ? "text-primary bg-primary/10 shadow-sm" 
                      : "text-muted-foreground hover:text-foreground hover:bg-accent"
                  )}
                  title={item.label}
                >
                  <item.icon className="w-6 h-6" />
                  
                  {/* مؤشر النشاط */}
                  {expandedSection === item.key && (
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-primary rounded-r-full" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* مساحة فارغة */}
          <div className="flex-1"></div>

          {/* القسم السفلي */}
          <div className="p-4 flex flex-col items-center space-y-4">
            {bottomItems.map((item) => (
              <button 
                key={item.key}
                onClick={() => handleItemClick(item)}
                className={cn(
                  "w-12 h-12 flex items-center justify-center rounded-xl transition-all sidebar-item relative",
                  expandedSection === item.key 
                    ? "text-primary bg-primary/10 shadow-sm" 
                    : "text-muted-foreground hover:text-foreground hover:bg-accent"
                )}
                title={item.label}
              >
                <item.icon className="w-6 h-6" />
                
                {/* مؤشر النشاط */}
                {expandedSection === item.key && (
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-primary rounded-r-full" />
                )}
              </button>
            ))}
            
            {/* قائمة المستخدم */}
            <UserMenu />
          </div>
        </div>

        {/* منطقة المحتوى الموسعة */}
        {isExpanded && (
          <div className="flex-1 flex flex-col bg-card">
            {/* رأس الموسع */}
            <div className="flex items-center justify-between p-4 border-b border-border bg-muted/20">
              <h2 className="text-lg font-semibold text-foreground">
                {expandedSection === 'history' && t('navigation.history')}
                {expandedSection === 'folders' && t('navigation.folders')}
                {expandedSection === 'search' && t('navigation.search')}
                {expandedSection === 'help' && t('navigation.help')}
                {expandedSection === 'admin' && 'Admin'}
              </h2>
              
              <button
                onClick={closeExpansion}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title={t('common.close')}
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
            </div>

            {/* محتوى الموسع */}
            <div className="flex-1 overflow-hidden">
              {renderExpandedContent()}
            </div>
          </div>
        )}
        
        {/* زر الإغلاق للجوال */}
        {isMobile && (
          <button 
            onClick={onClose}
            className="absolute top-4 left-4 p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg z-10"
            title={t('common.close')}
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>
    </>
  );
};
```

### 2. سجل المحادثات (ChatHistory)

```typescript
// src/components/sidebar/ChatHistory.tsx
interface ChatHistoryProps {
  className?: string;
  onChatSelect?: (chatId: string) => void;
  selectedChatId?: string;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  className,
  onChatSelect,
  selectedChatId
}) => {
  const { t, language } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [showArchived, setShowArchived] = useState(false);
  const [showMoveModal, setShowMoveModal] = useState(false);
  const [selectedChatForMove, setSelectedChatForMove] = useState<ChatHistoryItem | null>(null);
  
  // حالة توسع الأقسام الزمنية
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    today: true,
    yesterday: true,
    last7Days: false,
    older: false
  });

  // منطق المكون
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* الرأس */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-foreground">
            {t('history.title')}
          </h2>
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowArchived(!showArchived)}
              className={cn(
                "p-2 rounded-lg transition-colors text-xs font-medium",
                showArchived 
                  ? "bg-primary/10 text-primary" 
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              )}
              title={showArchived ? t('history.actions.hideArchived') : t('history.actions.showArchived')}
            >
              <Archive className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* شريط البحث */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder={t('history.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-3 pr-10 py-2 text-sm bg-muted/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
          />
        </div>
      </div>

      {/* قائمة المحادثات */}
      <div className="flex-1 overflow-y-auto">
        {/* محتوى المحادثات مقسم حسب الوقت */}
        {/* ... */}
      </div>

      {/* إحصائيات الذيل */}
      <div className="p-4 border-t border-border bg-muted/20">
        <div className="text-xs text-muted-foreground text-center">
          {totalFilteredChats} {t('history.stats.ofTotal')} {totalAvailableChats} {t('history.stats.totalChats')}
        </div>
      </div>

      {/* نافذة النقل إلى مجلد */}
      <MoveToFolderModal
        isOpen={showMoveModal}
        onClose={() => {
          setShowMoveModal(false);
          setSelectedChatForMove(null);
        }}
        onMoveToFolder={handleMoveToFolderConfirm}
        onCreateNewFolder={handleCreateNewFolderAndMove}
        folders={folders}
        chatTitle={selectedChatForMove?.title || ''}
      />
    </div>
  );
};
```

### 3. إدارة المجلدات (FoldersManager)

```typescript
// src/components/sidebar/FoldersManager.tsx
interface FoldersManagerProps {
  className?: string;
  onChatSelect?: (chatId: string) => void;
  selectedChatId?: string;
}

export const FoldersManager: React.FC<FoldersManagerProps> = ({
  className,
  onChatSelect,
  selectedChatId
}) => {
  const { t, language } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  // حالة توسع المجلدات
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});

  // منطق المكون
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* الرأس */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-foreground">
            {t('navigation.folders')}
          </h2>
          <button
            onClick={() => setShowCreateModal(true)}
            className="p-2 text-primary hover:bg-primary/10 rounded-lg transition-colors"
            title={t('folders.createNew')}
          >
            <FolderPlus className="w-5 h-5" />
          </button>
        </div>

        {/* شريط البحث */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder={t('folders.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-3 pr-10 py-2 text-sm bg-muted/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
          />
        </div>
      </div>

      {/* قائمة المجلدات */}
      <div className="flex-1 overflow-y-auto">
        {/* محتوى المجلدات */}
        {/* ... */}
      </div>

      {/* إحصائيات الذيل */}
      <div className="p-4 border-t border-border bg-muted/20">
        <div className="text-xs text-muted-foreground text-center">
          {folders.length} {t('folders.folderCount')} • {totalChats} {t('folders.chatCount')}
        </div>
      </div>

      {/* نافذة إنشاء مجلد */}
      <CreateFolderModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreateFolder={handleCreateFolder}
      />
    </div>
  );
};
```

### 4. البحث الشامل (SearchManager)

```typescript
// src/components/sidebar/SearchManager.tsx
interface SearchManagerProps {
  className?: string;
  onChatSelect?: (chatId: string) => void;
  onMessageSelect?: (chatId: string, messageIndex: number) => void;
  onFolderSelect?: (folderId: string) => void;
}

export const SearchManager: React.FC<SearchManagerProps> = ({
  className,
  onChatSelect,
  onMessageSelect,
  onFolderSelect
}) => {
  const { t, language } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'all',
    dateRange: 'all'
  });

  // منطق المكون
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* الرأس */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-foreground">
            {t('navigation.search')}
          </h2>
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
              title={t('search.clear')}
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* شريط البحث */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder={t('search.placeholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-3 pr-10 py-3 text-sm bg-muted/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
            autoFocus
          />
          {isSearching && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* مؤشر الفلاتر النشطة */}
        {hasActiveFilters && (
          <div className="mt-2 flex items-center gap-2 text-xs">
            <span className="text-muted-foreground">{t('search.activeFilters')}:</span>
            {filters.type !== 'all' && (
              <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">
                {t(`search.filterTypes.${filters.type}`)}
              </span>
            )}
            {filters.dateRange !== 'all' && (
              <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">
                {t(`search.filterDates.${filters.dateRange}`)}
              </span>
            )}
          </div>
        )}
      </div>

      {/* الفلاتر */}
      <SearchFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        isOpen={showFilters}
        onToggle={() => setShowFilters(!showFilters)}
      />

      {/* النتائج */}
      <div className="flex-1 overflow-y-auto">
        {/* محتوى نتائج البحث */}
        {/* ... */}
      </div>

      {/* إحصائيات الذيل */}
      {searchQuery && searchResults.length > 0 && (
        <div className="p-4 border-t border-border bg-muted/20">
          <div className="text-xs text-muted-foreground text-center">
            {searchResults.length} {t('search.results')} • {t('search.searchedIn')} {mockData.chats.length} {t('search.chats')} {t('search.and')} {mockData.folders.length} {t('search.folders')}
          </div>
        </div>
      )}
    </div>
  );
};
```

### 5. نظام المساعدة (HelpManager)

```typescript
// src/components/sidebar/HelpManager.tsx
interface HelpManagerProps {
  className?: string;
}

export const HelpManager: React.FC<HelpManagerProps> = ({ className }) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    general: true
  });
  const [activeTab, setActiveTab] = useState<'faq' | 'contact' | 'resources'>('faq');

  // منطق المكون
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* الرأس */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-foreground">
            {t('navigation.help')}
          </h2>
          <HelpCircle className="w-5 h-5 text-primary" />
        </div>

        {/* التبويبات */}
        <div className="flex items-center gap-1 p-1 bg-muted rounded-lg mb-4">
          <button
            onClick={() => setActiveTab('faq')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all",
              activeTab === 'faq'
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            {t('help.faq')}
          </button>
          <button
            onClick={() => setActiveTab('contact')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all",
              activeTab === 'contact'
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            {t('help.contact')}
          </button>
          <button
            onClick={() => setActiveTab('resources')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all",
              activeTab === 'resources'
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            {t('help.resources')}
          </button>
        </div>

        {/* شريط البحث - فقط لتبويب الأسئلة الشائعة */}
        {activeTab === 'faq' && (
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder={t('help.searchFAQ')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-3 pr-10 py-2 text-sm bg-muted/50 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
            />
          </div>
        )}
      </div>

      {/* المحتوى */}
      <div className="flex-1 overflow-y-auto">
        {/* محتوى التبويب النشط */}
        {/* ... */}
      </div>

      {/* الذيل */}
      <div className="p-4 border-t border-border bg-muted/20">
        <div className="text-xs text-muted-foreground text-center">
          {activeTab === 'faq' && `${totalFAQs} ${t('help.faqCount')} ${t('help.in')} ${helpCategories.length} ${t('help.categories')}`}
          {activeTab === 'contact' && t('help.contactFooter')}
          {activeTab === 'resources' && t('help.resourcesFooter')}
        </div>
      </div>
    </div>
  );
};
```

---

## ⚙️ مكونات الإعدادات

### 1. صفحة الإعدادات (SettingsPage)

```typescript
// src/components/settings/SettingsPage.tsx
interface SettingsPageProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsPage: React.FC<SettingsPageProps> = ({ isOpen, onClose }) => {
  const { t, language, setLanguage } = useTranslation();
  const { theme, setTheme } = useTheme();
  const [activeSection, setActiveSection] = useState('profile');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // حالات الإعدادات
  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: 'المستخدم',
    email: '<EMAIL>',
    phone: '+966501234567',
    location: 'الرياض، المملكة العربية السعودية',
    joinDate: '2024-01-15'
  });

  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    soundEnabled: true,
    newMessageSound: true,
    systemUpdates: true,
    legalUpdates: true,
    weeklyDigest: false
  });

  // ... باقي حالات الإعدادات

  // أقسام الإعدادات
  const settingsSections: SettingsSection[] = [
    {
      id: 'profile',
      title: t('settings.sections.profile.title'),
      icon: <User className="w-4 h-4" />,
      description: t('settings.sections.profile.description')
    },
    {
      id: 'notifications',
      title: t('settings.sections.notifications.title'),
      icon: <Bell className="w-4 h-4" />,
      description: t('settings.sections.notifications.description')
    },
    // ... باقي الأقسام
  ];

  // منطق المكون
  
  return (
    createPortal(
      <div 
        className="fixed inset-0 z-[99999] flex items-center justify-center p-4"
        style={{ 
          background: 'rgba(0, 0, 0, 0.5)',
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)'
        }}
      >
        {/* Backdrop */}
        <div 
          className="absolute inset-0" 
          onClick={handleClose}
          aria-label={t('common.close')}
        />
        
        {/* النافذة الرئيسية */}
        <div 
          className="relative bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col animate-fade-in-up"
          onClick={(e) => e.stopPropagation()}
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{t('settings.title')}</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">{t('settings.description')}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2 text-sm"
                >
                  {isSaving ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {isSaving ? t('settings.saving') : t('settings.saveChanges')}
                </button>
              )}
              
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                aria-label={t('common.close')}
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* المحتوى */}
          <div className="flex-1 flex overflow-hidden">
            {/* الشريط الجانبي */}
            <div className="w-80 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 overflow-y-auto">
              <div className="p-4 space-y-2">
                {settingsSections.map((section) => (
                  <SettingsSectionComponent
                    key={section.id}
                    section={section}
                    isActive={activeSection === section.id}
                    onClick={() => setActiveSection(section.id)}
                  />
                ))}
              </div>
            </div>

            {/* المحتوى الرئيسي */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-6">
                {renderSectionContent()}
              </div>
            </div>
          </div>

          {/* الذيل */}
          {hasUnsavedChanges && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-yellow-50 dark:bg-yellow-900/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm">{t('settings.unsavedChanges')}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setHasUnsavedChanges(false)}
                    className="px-3 py-1.5 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                  >
                    {t('settings.discardChanges')}
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="px-4 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors text-sm"
                  >
                    {t('settings.save')}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>,
      document.body
    )
  );
};
```

### 2. مكونات الإعدادات الفرعية

```typescript
// مكون قسم الإعدادات
interface SettingsSectionComponentProps {
  section: SettingsSection;
  isActive: boolean;
  onClick: () => void;
}

const SettingsSectionComponent: React.FC<SettingsSectionComponentProps> = ({
  section,
  isActive,
  onClick
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-right",
        isActive 
          ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800" 
          : "hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
      )}
    >
      <div className={cn(
        "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0",
        isActive ? "bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-400" : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
      )}>
        {section.icon}
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className="text-sm font-medium mb-1">{section.title}</h3>
        <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
          {section.description}
        </p>
      </div>
      
      <ChevronRight className={cn(
        "w-4 h-4 flex-shrink-0 transition-transform",
        isActive ? "rotate-90 text-blue-600 dark:text-blue-400" : "text-gray-400"
      )} />
    </button>
  );
};

// مكون تبديل الإعدادات
interface SettingsToggleProps {
  label: string;
  description?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

const SettingsToggle: React.FC<SettingsToggleProps> = ({
  label,
  description,
  checked,
  onChange,
  disabled = false
}) => {
  return (
    <div className="flex items-start justify-between gap-4 py-3">
      <div className="flex-1">
        <label className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
          {label}
        </label>
        {description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 leading-relaxed">
            {description}
          </p>
        )}
      </div>
      
      <div className="switch-container" style={{ direction: 'ltr' }}>
        <Switch
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          className="switch-button"
        />
      </div>
    </div>
  );
};

// مكون اختيار الإعدادات
interface SettingsSelectProps {
  label: string;
  description?: string;
  value: string;
  options: { value: string; label: string }[];
  onChange: (value: string) => void;
}

const SettingsSelect: React.FC<SettingsSelectProps> = ({
  label,
  description,
  value,
  options,
  onChange
}) => {
  return (
    <div className="py-3">
      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
        {label}
      </label>
      {description && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-3 leading-relaxed">
          {description}
        </p>
      )}
      
      <div className="grid grid-cols-1 gap-2">
        {options.map((option) => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={cn(
              "w-full p-3 text-sm rounded-lg border transition-all duration-200 text-right",
              value === option.value
                ? "bg-white dark:bg-gray-800 border-blue-300 dark:border-blue-600 text-gray-900 dark:text-gray-100 ring-2 ring-blue-100 dark:ring-blue-900"
                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:hover:border-gray-500"
            )}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
};
```

---

## 📚 مكونات الاستشهادات القانونية

### 1. الاستشهاد القانوني (LegalCitation)

```typescript
// src/components/chat/legal-citation.tsx
export interface LegalReference {
  id: string;
  title: string;
  content: string;
  source: string;
  section?: string;
  articleNumber: string;
}

interface LegalCitationProps {
  articleNumber: string;
  children: React.ReactNode;
  className?: string;
}

interface CitationPopupState {
  show: boolean;
  title: string;
  content: string;
  position: { top: number; left: number };
  source: string;
  section: string;
  loading: boolean;
  error?: string;
}

export function LegalCitation({ articleNumber, children, className }: LegalCitationProps) {
  const { t } = useTranslation();
  const [citationPopup, setCitationPopup] = useState<CitationPopupState>({
    show: false,
    title: '',
    content: '',
    position: { top: 0, left: 0 },
    source: '',
    section: '',
    loading: false
  });
  
  const popupRef = useRef<HTMLDivElement>(null);
  
  // منطق المكون
  
  return (
    <>
      <button
        onClick={handleCitationClick}
        className={cn(
          "inline font-medium transition-colors duration-200 cursor-pointer border-none bg-transparent p-0 underline decoration-2 underline-offset-2",
          "text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-sm legal-citation-button",
          className
        )}
        title={`عرض تفاصيل المادة ${articleNumber}`}
        aria-label={`عرض تفاصيل المادة ${articleNumber}`}
      >
        {children}
      </button>
      
      {/* النافذة المنبثقة للاستشهاد */}
      {citationPopup.show && (
        <div 
          ref={popupRef}
          className="fixed z-50 bg-popover rounded-lg shadow-xl overflow-hidden transform transition-all duration-300 ease-out citation-popup-enter citation-popup"
          dir="rtl"
          style={{
            top: `${citationPopup.position.top}px`,
            left: `${citationPopup.position.left}px`,
            width: window.innerWidth < 768 ? '90vw' : '500px',
            maxWidth: '90vw',
            maxHeight: '80vh'
          }}
        >
          {/* رأس النافذة */}
          <div 
            className="flex justify-between items-center px-4 py-3 bg-muted/50 border-b border-border citation-header"
          >
            <div className="font-semibold text-base flex items-center gap-2 text-primary">
              {citationPopup.loading && <Loader2 className="w-4 h-4 animate-spin" />}
              <span className="truncate">{citationPopup.title}</span>
            </div>
            <div className="flex gap-2 flex-shrink-0">
              {!citationPopup.loading && !citationPopup.error && (
                <button
                  onClick={copyCitationToClipboard}
                  className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                  title="نسخ النص"
                  aria-label="نسخ النص"
                >
                  <Copy className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={() => setCitationPopup(prev => ({ ...prev, show: false }))}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="إغلاق"
                aria-label="إغلاق"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* محتوى النافذة */}
          <div className="px-6 py-5 text-right overflow-y-auto" style={{ maxHeight: 'calc(80vh - 80px)' }}>
            {citationPopup.loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
                <span className="mr-2 text-muted-foreground">جاري التحميل...</span>
              </div>
            ) : citationPopup.error ? (
              <div className="py-4">
                <div className="text-destructive text-center">
                  {citationPopup.content}
                </div>
              </div>
            ) : (
              <>
                <div 
                  className="text-popover-foreground leading-relaxed text-base mb-6" 
                  style={{ lineHeight: '1.8' }}
                >
                  {citationPopup.content}
                </div>
                
                {/* معلومات المصدر */}
                {citationPopup.source && (
                  <div className="pt-4 border-t border-border">
                    <div className="text-sm text-muted-foreground">
                      <strong>المصدر:</strong> {citationPopup.source}
                    </div>
                    {citationPopup.section && (
                      <div className="text-sm text-muted-foreground mt-1">
                        <strong>القسم:</strong> {citationPopup.section}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}
```

### 2. تحويل النصوص تلقائياً (parseTextWithCitations)

```typescript
// src/components/chat/legal-citation.tsx
export function parseTextWithCitations(text: string): React.ReactNode[] {
  const citationRegex = /المادة\s*\((\d+)\)/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;
  let key = 0;
  
  while ((match = citationRegex.exec(text)) !== null) {
    // إضافة النص قبل الاستشهاد
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    
    // إضافة الاستشهاد
    const articleNumber = match[1];
    parts.push(
      <LegalCitation key={key++} articleNumber={articleNumber}>
        المادة ({articleNumber})
      </LegalCitation>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // إضافة باقي النص
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  
  return parts;
}
```

---

## 🧰 مكونات واجهة المستخدم الأساسية

### 1. مبدل السمات (ThemeToggle)

```typescript
// src/components/ThemeToggle.tsx
interface ThemeToggleProps {
  className?: string;
  variant?: 'button' | 'dropdown' | 'switch';
  showLabel?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className,
  variant = 'button',
  showLabel = false
}) => {
  const { theme, actualTheme, setTheme, toggleTheme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  const themes: { value: Theme; label: string; icon: React.ReactNode }[] = [
    { value: 'light', label: 'فاتح', icon: <Sun className="w-4 h-4" /> },
    { value: 'dark', label: 'داكن', icon: <Moon className="w-4 h-4" /> },
    { value: 'system', label: 'النظام', icon: <Monitor className="w-4 h-4" /> },
  ];

  // منطق المكون حسب الـ variant
  
  // Default button variant
  return (
    <button
      onClick={toggleTheme}
      className={cn(
        "p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200",
        className
      )}
      title={actualTheme === 'dark' ? 'تبديل للمظهر الفاتح' : 'تبديل للمظهر الداكن'}
    >
      {actualTheme === 'dark' ? (
        <Sun className="w-5 h-5" />
      ) : (
        <Moon className="w-5 h-5" />
      )}
    </button>
  );
};
```

### 2. مبدل اللغة (LanguageSwitcher)

```typescript
// src/components/LanguageSwitcher.tsx
interface LanguageSwitcherProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'dropdown' | 'toggle' | 'buttons';
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  className,
  showLabel = true,
  variant = 'dropdown'
}) => {
  const { language, setLanguage, t, isRTL } = useTranslation();
  const [isOpen, setIsOpen] = React.useState(false);

  const languages: Language[] = ['ar', 'en'];

  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang);
    setIsOpen(false);
  };

  // منطق المكون حسب الـ variant
  
  // Default dropdown variant
  return (
    <div className={cn("relative", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        title={t('user.language')}
      >
        <Globe className="w-4 h-4" />
        {showLabel && (
          <>
            <span>{languageConfig[language].flag}</span>
            <span>{languageConfig[language].nativeName}</span>
          </>
        )}
        <svg 
          className={cn("w-4 h-4 transition-transform", isOpen && "rotate-180")} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className={cn(
            "absolute z-20 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1",
            isRTL ? "right-0" : "left-0"
          )}>
            {languages.map((lang) => (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className={cn(
                  "w-full flex items-center gap-3 px-4 py-2 text-sm hover:bg-gray-50 transition-colors",
                  isRTL ? "text-right" : "text-left"
                )}
              >
                <span className="text-lg">{languageConfig[lang].flag}</span>
                <span className="flex-1">{languageConfig[lang].nativeName}</span>
                <span className="text-xs text-gray-500">{languageConfig[lang].name}</span>
                {language === lang && (
                  <Check className="w-4 h-4 text-green-600" />
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
```

### 3. مكون الشعار (LogoComponent)

```typescript
// src/components/LogoComponent.tsx
interface LogoComponentProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  alt?: string;
}

export const LogoComponent: React.FC<LogoComponentProps> = ({ 
  className = '',
  size = 'md',
  alt = 'شُريح'
}) => {
  const { actualTheme } = useTheme();
  
  // تحديد الشعار المناسب حسب السمة
  const logoSrc = actualTheme === 'dark' 
    ? '/Shuraih.Logo_DarkMode.svg'  // للسمة الداكنة
    : '/ShuraihAIUI.svg';           // للسمة الفاتحة
  
  // أحجام الشعار
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-32 h-32'
  };
  
  return (
    <img 
      src={logoSrc}
      alt={alt}
      className={`logo-component ${sizeClasses[size]} ${className}`}
      style={{ 
        filter: 'none !important',
        opacity: 1,
        background: 'transparent'
      }}
    />
  );
};
```

### 4. قائمة النماذج (ModelsDropdown)

```typescript
// src/components/ModelsDropdown.tsx
interface ModelsDropdownProps {
  className?: string;
  selectedModelId?: string;
  onModelChange?: (modelId: string) => void;
}

interface Model {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  badge?: string;
  isAvailable: boolean;
  isPro?: boolean;
}

export const ModelsDropdown: React.FC<ModelsDropdownProps> = ({ 
  className,
  selectedModelId = 'shuraih-v1',
  onModelChange
}) => {
  const { t, isRTL } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const arrowRef = React.useRef(null);

  // قائمة النماذج المتاحة
  const models: Model[] = [
    {
      id: 'shuraih-v1',
      name: 'شُريح v1.0',
      description: 'النموذج الأساسي للاستشارات القانونية',
      icon: <LogoComponent size="sm" className="w-4 h-4" />,
      badge: 'الحالي',
      isAvailable: true,
      isPro: false
    },
    {
      id: 'shuraih-pro',
      name: 'شُريح Pro',
      description: 'نموذج متقدم مع تحليل أعمق',
      icon: <Sparkles className="w-4 h-4" />,
      badge: 'قريباً',
      isAvailable: false,
      isPro: true
    },
    {
      id: 'shuraih-turbo',
      name: 'شُريح Turbo',
      description: 'استجابة فائقة السرعة',
      icon: <Zap className="w-4 h-4" />,
      badge: 'قريباً',
      isAvailable: false,
      isPro: true
    }
  ];

  const selectedModel = models.find(model => model.id === selectedModelId) || models[0];

  // إعداد Floating UI
  const { refs, floatingStyles, context, middlewareData } = useFloating({
    // ... إعدادات Floating UI
  });

  // منطق المكون
  
  return (
    <>
      <button
        ref={refs.setReference}
        {...getReferenceProps()}
        className={cn(
          "flex items-center gap-3 px-4 py-2.5 text-sm font-medium",
          "bg-card text-card-foreground border border-border rounded-xl",
          "hover:bg-accent hover:text-accent-foreground hover:border-primary/30",
          "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary",
          "transition-all duration-200 shadow-sm hover:shadow-md",
          "min-w-[160px]",
          className
        )}
        aria-expanded={isOpen}
        aria-haspopup="true"
        title="اختيار النموذج"
      >
        {/* أيقونة النموذج مع خلفية دائرية */}
        <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
          {selectedModel.icon}
        </div>
        
        {/* معلومات النموذج */}
        <div className="flex-1 text-right">
          <div className="flex items-center gap-2 justify-end">
            {/* اسم النموذج */}
            <span className="font-semibold text-foreground">
              {selectedModel.name}
            </span>
            
            {/* شارة النموذج */}
            {selectedModel.badge && (
              <span className={cn(
                "px-2 py-0.5 text-xs font-medium rounded-full",
                selectedModel.isAvailable 
                  ? "bg-success/10 text-success border border-success/20" 
                  : "bg-warning/10 text-warning border border-warning/20"
              )}>
                {selectedModel.badge}
              </span>
            )}
          </div>
        </div>
        
        {/* سهم القائمة */}
        <ChevronDown className={cn(
          "w-4 h-4 text-muted-foreground transition-transform duration-200 flex-shrink-0",
          isOpen && "rotate-180 text-primary"
        )} />
      </button>

      {isOpen && (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={{
              ...floatingStyles,
              visibility: isReady ? 'visible' : 'hidden',
              opacity: isReady ? 1 : 0,
              transform: isReady 
                ? floatingStyles.transform 
                : 'translate3d(-9999px, -9999px, 0)',
              transition: isReady ? 'opacity 0.15s ease-out, transform 0.15s ease-out' : 'none',
              zIndex: 9999,
              position: 'fixed'
            }}
            {...getFloatingProps()}
            className="w-80 bg-popover border border-border rounded-xl shadow-xl py-2"
          >
            {/* محتوى القائمة المنسدلة */}
            {/* ... */}
          </div>
        </FloatingPortal>
      )}
    </>
  );
};
```

### 5. قائمة المستخدم (UserMenu)

```typescript
// src/components/UserMenu.tsx
interface UserMenuProps {
  className?: string;
}

export const UserMenu: React.FC<UserMenuProps> = ({ className }) => {
  const { t, isRTL } = useTranslation();
  const { actualTheme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);
  const [isReady, setIsReady] = React.useState(false);
  const [showSettings, setShowSettings] = React.useState(false);
  const arrowRef = React.useRef(null);

  // إعداد Floating UI
  const { refs, floatingStyles, context, middlewareData } = useFloating({
    // ... إعدادات Floating UI
  });

  // منطق المكون
  
  return (
    <>
      <button
        ref={refs.setReference}
        {...getReferenceProps()}
        className={cn(
          "w-12 h-12 bg-muted rounded-xl flex items-center justify-center hover:bg-accent transition-all focus:outline-none focus:ring-2 focus:ring-primary/20",
          className
        )}
        title={t('user.profile')}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <User className="w-6 h-6 text-muted-foreground" />
      </button>

      {isOpen && (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={{
              ...floatingStyles,
              visibility: isReady ? 'visible' : 'hidden',
              opacity: isReady ? 1 : 0,
              transform: isReady 
                ? floatingStyles.transform 
                : 'translate3d(-9999px, -9999px, 0)',
              transition: isReady ? 'opacity 0.15s ease-out, transform 0.15s ease-out' : 'none',
              zIndex: 9999,
              position: 'fixed'
            }}
            {...getFloatingProps()}
            className="w-64 bg-popover border border-border rounded-xl shadow-xl py-2"
          >
            {/* محتوى قائمة المستخدم */}
            {/* ... */}
          </div>
        </FloatingPortal>
      )}

      {/* صفحة الإعدادات */}
      <SettingsPage 
        isOpen={showSettings} 
        onClose={() => setShowSettings(false)} 
      />
    </>
  );
};
```

### 6. مكون Textarea تلقائي التمدد (AutoResizeTextarea)

```typescript
// src/components/ui/auto-resize-textarea.tsx
interface AutoResizeTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  minHeight?: number;
  maxHeight?: number;
}

export const AutoResizeTextarea = forwardRef<HTMLTextAreaElement, AutoResizeTextareaProps>(
  ({ className, minHeight = 60, maxHeight = 200, ...props }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const combinedRef = ref || textareaRef;

    useEffect(() => {
      const textarea = typeof combinedRef === 'function' ? null : combinedRef?.current;
      if (!textarea) return;

      const adjustHeight = () => {
        textarea.style.height = 'auto';
        const scrollHeight = textarea.scrollHeight;
        const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
        textarea.style.height = `${newHeight}px`;
      };

      adjustHeight();
      textarea.addEventListener('input', adjustHeight);
      
      return () => textarea.removeEventListener('input', adjustHeight);
    }, [minHeight, maxHeight, combinedRef]);

    return (
      <textarea
        ref={combinedRef}
        className={cn(
          "resize-none overflow-y-auto transition-all duration-200",
          className
        )}
        style={{ minHeight: `${minHeight}px`, maxHeight: `${maxHeight}px` }}
        {...props}
      />
    );
  }
);

AutoResizeTextarea.displayName = "AutoResizeTextarea";
```

### 7. مكون Switch (مفتاح التبديل)

```typescript
// src/components/ui/switch.tsx
interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  disabled = false,
  className
}) => {
  const { isRTL } = useTranslation();

  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      onClick={() => !disabled && onChange(!checked)}
      className={cn(
        "relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200",
        "focus:outline-none focus:ring-2 focus:ring-offset-2",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        checked 
          ? "bg-blue-600 focus:ring-blue-500" 
          : "bg-gray-200 dark:bg-gray-700 focus:ring-gray-300",
        className
      )}
      style={{
        direction: 'ltr'
      }}
    >
      {/* الدائرة المتحركة */}
      <span
        className={cn(
          "inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm"
        )}
        style={{
          transform: checked 
            ? 'translateX(1.5rem)' // 24px = 6 * 4px
            : 'translateX(0.25rem)' // 4px = 1 * 4px
        }}
      />
    </button>
  );
};
```

---

## 🔄 دمج المكونات في لوحة التحكم

### 1. استيراد المكونات في لوحة التحكم

```typescript
// admin/src/components/shared/index.ts
// استيراد المكونات المشتركة من التطبيق الرئيسي

// مكونات الواجهة الأساسية
export { LogoComponent } from '@/components/LogoComponent';
export { ThemeToggle } from '@/components/ThemeToggle';
export { LanguageSwitcher } from '@/components/LanguageSwitcher';
export { Switch } from '@/components/ui/switch';
export { AutoResizeTextarea } from '@/components/ui/auto-resize-textarea';

// مكونات المحادثة
export { SuggestedQuestions } from '@/components/SuggestedQuestions';
export { ChatInput } from '@/components/ChatInput';
export { ChatMessage } from '@/components/ChatMessage';

// مكونات الاستشهادات
export { LegalCitation, parseTextWithCitations } from '@/components/chat/legal-citation';

// مزودي الحالة
export { ThemeProvider, useTheme } from '@/lib/theme';
export { I18nProvider, useTranslation } from '@/lib/i18n';
```

### 2. استخدام المكونات في لوحة التحكم

```typescript
// admin/src/pages/ChatPreview.tsx
import { 
  ChatInput, 
  ChatMessage, 
  SuggestedQuestions,
  ThemeToggle,
  LanguageSwitcher,
  LogoComponent
} from '../components/shared';

const ChatPreviewPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const handleSendMessage = (text: string) => {
    // منطق إرسال الرسالة
  };
  
  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-xl font-semibold">معاينة المحادثة</h1>
        <div className="flex items-center gap-2">
          <ThemeToggle />
          <LanguageSwitcher />
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <LogoComponent size="xl" />
            <h2 className="text-xl mt-4 mb-6">ابدأ محادثة جديدة</h2>
            <SuggestedQuestions onQuestionClick={handleSendMessage} />
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map(message => (
              <ChatMessage 
                key={message.id} 
                message={message} 
                onLike={() => {}} 
                onDislike={() => {}}
              />
            ))}
            {isTyping && <TypingIndicator />}
          </div>
        )}
      </div>
      
      <div className="p-4 border-t">
        <ChatInput 
          onSendMessage={handleSendMessage}
          isLoading={isTyping}
        />
      </div>
    </div>
  );
};
```

### 3. إدارة الأسئلة المقترحة في لوحة التحكم

```typescript
// admin/src/pages/SuggestedQuestionsManager.tsx
import { SuggestedQuestions } from '../components/shared';

const SuggestedQuestionsManager: React.FC = () => {
  const [questions, setQuestions] = useState<string[]>([
    'ما شروط التعويض في حال الإخلال بالعقد؟',
    'ما خطوات تأسيس شركة ذات مسؤولية محدودة؟',
    'ما هي شروط رفع دعوى أمام ديوان المظالم؟',
    'هل يحق للمرأة فسخ النكاح بسبب الضرر؟'
  ]);
  const [newQuestion, setNewQuestion] = useState('');
  
  const handleAddQuestion = () => {
    if (newQuestion.trim()) {
      setQuestions([...questions, newQuestion.trim()]);
      setNewQuestion('');
    }
  };
  
  const handleRemoveQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };
  
  const handlePreview = (question: string) => {
    console.log('Preview question:', question);
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-6">إدارة الأسئلة المقترحة</h1>
      
      <div className="mb-8">
        <h2 className="text-lg font-medium mb-4">معاينة</h2>
        <div className="border p-6 rounded-lg bg-gray-50">
          <SuggestedQuestions onQuestionClick={handlePreview} />
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-4">الأسئلة الحالية</h2>
        <div className="space-y-2">
          {questions.map((question, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <span>{question}</span>
              <button
                onClick={() => handleRemoveQuestion(index)}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
      
      <div>
        <h2 className="text-lg font-medium mb-4">إضافة سؤال جديد</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={newQuestion}
            onChange={(e) => setNewQuestion(e.target.value)}
            placeholder="أدخل سؤالاً جديداً..."
            className="flex-1 p-2 border rounded-lg"
          />
          <button
            onClick={handleAddQuestion}
            disabled={!newQuestion.trim()}
            className="px-4 py-2 bg-primary text-white rounded-lg disabled:opacity-50"
          >
            إضافة
          </button>
        </div>
      </div>
    </div>
  );
};
```

### 4. إدارة المظهر في لوحة التحكم

```typescript
// admin/src/pages/ThemeManager.tsx
import { ThemeToggle, LogoComponent } from '../components/shared';
import { useTheme } from '../components/shared';

const ThemeManager: React.FC = () => {
  const { theme, setTheme, actualTheme } = useTheme();
  const [primaryColor, setPrimaryColor] = useState('#2635ED');
  
  const handleColorChange = (color: string) => {
    setPrimaryColor(color);
    document.documentElement.style.setProperty('--color-primary', hexToRgb(color));
  };
  
  const hexToRgb = (hex: string): string => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? `${parseInt(result[1], 16)} ${parseInt(result[2], 16)} ${parseInt(result[3], 16)}`
      : '38 53 237';
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-6">إدارة المظهر</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-lg font-medium mb-4">السمة</h2>
          <div className="p-6 border rounded-lg">
            <div className="flex items-center justify-between mb-6">
              <span>المظهر الحالي: {theme === 'light' ? 'فاتح' : theme === 'dark' ? 'داكن' : 'النظام'}</span>
              <ThemeToggle variant="dropdown" showLabel />
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <button
                onClick={() => setTheme('light')}
                className={`p-4 border rounded-lg ${theme === 'light' ? 'ring-2 ring-primary' : ''}`}
              >
                <div className="w-full h-24 bg-white border mb-2 rounded"></div>
                <span>فاتح</span>
              </button>
              
              <button
                onClick={() => setTheme('dark')}
                className={`p-4 border rounded-lg ${theme === 'dark' ? 'ring-2 ring-primary' : ''}`}
              >
                <div className="w-full h-24 bg-gray-900 border mb-2 rounded"></div>
                <span>داكن</span>
              </button>
              
              <button
                onClick={() => setTheme('system')}
                className={`p-4 border rounded-lg ${theme === 'system' ? 'ring-2 ring-primary' : ''}`}
              >
                <div className="w-full h-24 bg-gradient-to-r from-white to-gray-900 border mb-2 rounded"></div>
                <span>النظام</span>
              </button>
            </div>
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-medium mb-4">اللون الرئيسي</h2>
          <div className="p-6 border rounded-lg">
            <div className="mb-4">
              <label className="block mb-2">اختر اللون الرئيسي:</label>
              <input
                type="color"
                value={primaryColor}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-full h-10 rounded"
              />
            </div>
            
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">معاينة:</h3>
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-4 mb-4">
                  <LogoComponent size="lg" />
                  <div>
                    <h4 className="font-medium">شُريح</h4>
                    <p className="text-sm text-muted-foreground">المساعد القانوني الذكي</p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <button className="w-full py-2 px-4 bg-primary text-primary-foreground rounded-lg">
                    زر رئيسي
                  </button>
                  <div className="p-3 border border-primary rounded-lg text-primary">
                    نص بلون رئيسي
                  </div>
                  <div className="p-3 bg-primary/10 rounded-lg">
                    خلفية بلون رئيسي مخفف
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

---

## 🔗 دمج المكونات في لوحة التحكم

### 1. استخدام المكونات في صفحات لوحة التحكم

```typescript
// admin/src/pages/ChatSimulator.tsx
import { 
  ChatInput, 
  ChatMessage, 
  SuggestedQuestions,
  TypingIndicator
} from '../components/shared';

const ChatSimulator: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const handleSendMessage = (text: string) => {
    // إضافة رسالة المستخدم
    const userMessage: Message = {
      id: Date.now().toString(),
      text,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);
    
    // محاكاة رد البوت
    setTimeout(() => {
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: `هذا رد تجريبي على: "${text}"`,
        sender: 'bot',
        timestamp: new Date(),
        tokens: Math.floor(Math.random() * 100) + 50
      };
      
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1500);
  };
  
  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-xl font-semibold">محاكي المحادثة</h1>
        <div className="flex gap-2">
          <button 
            onClick={() => setMessages([])}
            className="px-3 py-1 text-sm border rounded-lg"
          >
            مسح المحادثة
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <h2 className="text-xl mb-6">ابدأ محادثة جديدة</h2>
            <SuggestedQuestions onQuestionClick={handleSendMessage} />
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map(message => (
              <ChatMessage 
                key={message.id} 
                message={message} 
                onLike={() => {}} 
                onDislike={() => {}}
              />
            ))}
            {isTyping && <TypingIndicator />}
          </div>
        )}
      </div>
      
      <div className="p-4 border-t">
        <ChatInput 
          onSendMessage={handleSendMessage}
          isLoading={isTyping}
        />
      </div>
    </div>
  );
};
```

### 2. إدارة الاستشهادات القانونية في لوحة التحكم

```typescript
// admin/src/pages/LegalCitationsManager.tsx
import { 
  LegalCitation, 
  parseTextWithCitations 
} from '../components/shared';

const LegalCitationsManager: React.FC = () => {
  const [citations, setCitations] = useState<LegalReference[]>([
    {
      id: 'legal_57',
      title: 'المادة 57 من نظام المرافعات الشرعية',
      content: 'إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة، فإن المحكمة تنظر في الدعوى وتصدر حكمها غيابياً.',
      source: 'نظام المرافعات الشرعية، الصادر بالمرسوم الملكي رقم (م/1) وتاريخ 1435/01/22هـ',
      section: 'الباب الخامس - الأحكام الغيابية',
      articleNumber: '57'
    },
    // ... المزيد من الاستشهادات
  ]);
  
  const [newCitation, setNewCitation] = useState<Partial<LegalReference>>({
    title: '',
    content: '',
    source: '',
    section: '',
    articleNumber: ''
  });
  
  const [previewText, setPreviewText] = useState(
    'يعد الحكم الصادر عن المحكمة غيابياً إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة استناداً إلى المادة (57) من نظام المرافعات الشرعية.'
  );
  
  const handleAddCitation = () => {
    if (newCitation.articleNumber && newCitation.title && newCitation.content) {
      const citation: LegalReference = {
        id: `legal_${newCitation.articleNumber}`,
        title: newCitation.title,
        content: newCitation.content,
        source: newCitation.source || '',
        section: newCitation.section || '',
        articleNumber: newCitation.articleNumber
      };
      
      setCitations([...citations, citation]);
      setNewCitation({
        title: '',
        content: '',
        source: '',
        section: '',
        articleNumber: ''
      });
    }
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-6">إدارة الاستشهادات القانونية</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-lg font-medium mb-4">الاستشهادات الحالية</h2>
          <div className="space-y-4">
            {citations.map(citation => (
              <div key={citation.id} className="p-4 border rounded-lg">
                <div className="flex justify-between mb-2">
                  <h3 className="font-medium">{citation.title}</h3>
                  <span className="text-sm text-muted-foreground">المادة {citation.articleNumber}</span>
                </div>
                <p className="text-sm mb-2 line-clamp-2">{citation.content}</p>
                <div className="text-xs text-muted-foreground">
                  المصدر: {citation.source}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-medium mb-4">إضافة استشهاد جديد</h2>
          <div className="space-y-4 p-4 border rounded-lg">
            <div>
              <label className="block text-sm mb-1">رقم المادة</label>
              <input
                type="text"
                value={newCitation.articleNumber || ''}
                onChange={(e) => setNewCitation({...newCitation, articleNumber: e.target.value})}
                className="w-full p-2 border rounded-lg"
              />
            </div>
            
            <div>
              <label className="block text-sm mb-1">العنوان</label>
              <input
                type="text"
                value={newCitation.title || ''}
                onChange={(e) => setNewCitation({...newCitation, title: e.target.value})}
                className="w-full p-2 border rounded-lg"
              />
            </div>
            
            <div>
              <label className="block text-sm mb-1">المحتوى</label>
              <textarea
                value={newCitation.content || ''}
                onChange={(e) => setNewCitation({...newCitation, content: e.target.value})}
                className="w-full p-2 border rounded-lg h-24"
              />
            </div>
            
            <div>
              <label className="block text-sm mb-1">المصدر</label>
              <input
                type="text"
                value={newCitation.source || ''}
                onChange={(e) => setNewCitation({...newCitation, source: e.target.value})}
                className="w-full p-2 border rounded-lg"
              />
            </div>
            
            <div>
              <label className="block text-sm mb-1">القسم</label>
              <input
                type="text"
                value={newCitation.section || ''}
                onChange={(e) => setNewCitation({...newCitation, section: e.target.value})}
                className="w-full p-2 border rounded-lg"
              />
            </div>
            
            <button
              onClick={handleAddCitation}
              disabled={!newCitation.articleNumber || !newCitation.title || !newCitation.content}
              className="w-full py-2 bg-primary text-white rounded-lg disabled:opacity-50"
            >
              إضافة استشهاد
            </button>
          </div>
          
          <h2 className="text-lg font-medium mt-8 mb-4">معاينة الاستشهادات</h2>
          <div className="p-4 border rounded-lg">
            <div className="mb-4">
              <label className="block text-sm mb-1">نص للمعاينة</label>
              <textarea
                value={previewText}
                onChange={(e) => setPreviewText(e.target.value)}
                className="w-full p-2 border rounded-lg h-24"
              />
            </div>
            
            <div className="p-4 bg-card rounded-lg border">
              <div className="text-card-foreground">
                {parseTextWithCitations(previewText)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

---

## 🔧 أفضل الممارسات

### 1. إعادة استخدام المكونات

```typescript
// استخدام المكونات في سياقات مختلفة
import { ChatInput, SuggestedQuestions } from '@/components';

// في صفحة المحادثة
<ChatInput onSendMessage={handleSendMessage} />

// في صفحة الدعم
<ChatInput 
  onSendMessage={handleSupportMessage}
  placeholder="كيف يمكننا مساعدتك؟"
  maxLength={500}
/>

// في صفحة الأسئلة الشائعة
<SuggestedQuestions onQuestionClick={handleFaqClick} />

// في صفحة الترحيب
<SuggestedQuestions 
  onQuestionClick={handleOnboardingClick}
  className="grid-cols-1"
/>
```

### 2. تخصيص المكونات

```typescript
// تخصيص المكونات باستخدام الخصائص
<ChatInput
  placeholder="اكتب سؤالك القانوني هنا..."
  maxLength={1000}
  supportedFileTypes={['application/pdf', 'image/jpeg', 'image/png']}
  maxFileSize={5 * 1024 * 1024} // 5MB
  onAttachFile={handleCustomAttachment}
  className="border-2 border-primary"
/>

// تخصيص المكونات باستخدام الأطفال
<SuggestedQuestions onQuestionClick={handleClick}>
  <h3 className="text-lg font-medium mb-4">أسئلة مخصصة</h3>
  <p className="text-sm text-muted-foreground mb-4">
    اختر من الأسئلة المخصصة التالية:
  </p>
</SuggestedQuestions>
```

### 3. استخدام السمات

```typescript
// استخدام السمات في المكونات المخصصة
import { useTheme } from '@/lib/theme';

const CustomComponent: React.FC = () => {
  const { actualTheme } = useTheme();
  
  return (
    <div className={`
      p-4 rounded-lg
      ${actualTheme === 'dark' 
        ? 'bg-gray-800 text-gray-100' 
        : 'bg-white text-gray-900'}
    `}>
      <h2>مكون مخصص</h2>
      <p>يدعم السمات الفاتحة والداكنة</p>
    </div>
  );
};
```

### 4. استخدام الترجمة

```typescript
// استخدام الترجمة في المكونات المخصصة
import { useTranslation } from '@/lib/i18n';

const CustomComponent: React.FC = () => {
  const { t, language, isRTL } = useTranslation();
  
  return (
    <div dir={isRTL ? 'rtl' : 'ltr'} className="p-4">
      <h2>{t('custom.title')}</h2>
      <p>{t('custom.description')}</p>
      <button className="mt-2 px-4 py-2 bg-primary text-white rounded-lg">
        {t('custom.button')}
      </button>
    </div>
  );
};
```

---

## 🔍 استكشاف الأخطاء

### 1. مشاكل السمات

```typescript
// مشكلة: المكون لا يستجيب لتغيير السمة
// الحل: التأكد من استخدام useTheme وتطبيق الفئات الصحيحة

// خطأ
const Component = () => {
  return <div className="bg-white text-black">محتوى ثابت</div>;
};

// صحيح
const Component = () => {
  const { actualTheme } = useTheme();
  
  return (
    <div className={cn(
      "bg-background text-foreground",
      // يمكن إضافة فئات إضافية حسب السمة
      actualTheme === 'dark' && "border-gray-700"
    )}>
      محتوى متكيف مع السمة
    </div>
  );
};
```

### 2. مشاكل RTL

```typescript
// مشكلة: عناصر UI لا تعمل بشكل صحيح مع RTL
// الحل: استخدام direction: ltr للعناصر التي تحتاج ذلك

// خطأ
const RTLComponent = () => {
  return <input type="text" className="text-right" />;
};

// صحيح
const RTLComponent = () => {
  const { isRTL } = useTranslation();
  
  return (
    <div dir={isRTL ? 'rtl' : 'ltr'} className="text-right">
      <input 
        type="text" 
        style={{ textAlign: isRTL ? 'right' : 'left' }}
      />
      
      {/* لبعض المكونات مثل Switch، نحتاج إلى إجبار LTR */}
      <div style={{ direction: 'ltr' }}>
        <Switch checked={true} onChange={() => {}} />
      </div>
    </div>
  );
};
```

### 3. مشاكل الأداء

```typescript
// مشكلة: إعادة رسم متكررة للمكونات
// الحل: استخدام React.memo و useMemo و useCallback

// خطأ
const ExpensiveComponent = ({ data }) => {
  const processedData = processData(data); // عملية ثقيلة
  
  return <div>{processedData}</div>;
};

// صحيح
const ExpensiveComponent = React.memo(({ data }) => {
  const processedData = useMemo(() => {
    return processData(data);
  }, [data]);
  
  return <div>{processedData}</div>;
});

// للدوال
const ParentComponent = () => {
  // خطأ
  const handleClick = () => {
    console.log('Clicked');
  };
  
  // صحيح
  const handleClick = useCallback(() => {
    console.log('Clicked');
  }, []);
  
  return <ChildComponent onClick={handleClick} />;
};
```

---

## 🔄 تحديثات المكونات

### 1. تحديث مكون موجود

```typescript
// 1. تحديد المكون المراد تحديثه
// src/components/ChatInput.tsx

// 2. نسخ المكون الحالي واحتفظ به كنسخة احتياطية
// src/components/ChatInput.backup.tsx

// 3. تحديث المكون مع الحفاظ على الواجهة البرمجية
export const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false,
  isLoading = false,
  // ... باقي الخصائص
}) => {
  // تحديث المنطق الداخلي
  
  return (
    // تحديث JSX مع الحفاظ على الهيكل العام
  );
};
```

### 2. إضافة مكون جديد

```typescript
// 1. إنشاء ملف جديد للمكون
// src/components/NewComponent.tsx

import React from 'react';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/lib/i18n';
import { useTheme } from '@/lib/theme';

interface NewComponentProps {
  title: string;
  description?: string;
  className?: string;
  onClick?: () => void;
}

export const NewComponent: React.FC<NewComponentProps> = ({
  title,
  description,
  className,
  onClick
}) => {
  const { t } = useTranslation();
  const { actualTheme } = useTheme();
  
  return (
    <div 
      className={cn(
        "p-4 rounded-lg border transition-all duration-200",
        actualTheme === 'dark' ? "bg-card border-border" : "bg-white border-gray-200",
        className
      )}
      onClick={onClick}
    >
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
    </div>
  );
};

// 2. تصدير المكون من index.ts
// src/components/index.ts
export { NewComponent } from './NewComponent';
```

---

## 🎯 خاتمة

هذا الدليل يوفر نظرة شاملة على مكونات التصميم المستخدمة في واجهة شُريح، وكيفية إعادة استخدامها في لوحة التحكم أو أي تطبيقات أخرى. المكونات مصممة لتكون قابلة للتخصيص والتوسعة، مع دعم كامل للسمات والترجمة.

عند تطوير لوحة التحكم، يمكن استيراد هذه المكونات واستخدامها مباشرة، أو تخصيصها حسب الحاجة. كما يمكن استخدامها كمرجع لبناء مكونات جديدة تتبع نفس الأنماط والمعايير.

للمزيد من المعلومات حول كيفية استخدام هذه المكونات، يرجى الرجوع إلى الوثائق التقنية الشاملة في ملف `interface-documentation.md`.

---

<div align="center">
  
  **تم إعداد هذا الدليل بواسطة فريق شُريح**
  
  **© 2025 جَدالة للذكاء الاصطناعي. جميع الحقوق محفوظة.**
  
  [الموقع الرسمي](https://shuraih.ai) • [التوثيق](https://docs.shuraih.ai) • [GitHub](https://github.com/your-repo/shuraih-ai-platform) • [الدعم](mailto:<EMAIL>)
  
</div>
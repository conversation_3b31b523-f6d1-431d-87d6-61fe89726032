import { Refine } from "@refinedev/core";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
import {
  ErrorComponent,
  ThemedLayoutV2,
  useNotificationProvider,
} from "@refinedev/antd";
import routerBindings, {
  DocumentTitleHandler,
  NavigateToResource,
  UnsavedChangesNotifier,
} from "@refinedev/react-router-v6";
import dataProvider from "@refinedev/simple-rest";
import { BrowserRouter, Routes, Route, Outlet } from "react-router-dom";
import { ConfigProvider, App as AntdApp, theme } from "antd";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme";
import { I18nProvider } from "./contexts/i18n";
import { AuthProvider } from "./contexts/auth-context";
import { AISettingsProvider } from "./contexts/ai-settings-context";
import { Header } from "./components/layout/header";
import { Sider } from "./components/layout/sider";
import { Title } from "./components/layout/title";
import { RequireAuth } from "./components/auth/require-auth";
import { Dashboard } from "./pages/dashboard";
import { UserList } from "./pages/users";
import { ChatList } from "./pages/chats";
import { LegalCitationList } from "./pages/legal-citations";
import { AnalyticsDashboard } from "./pages/analytics";
import { SettingsPage } from "./pages/settings";
import { ContentModerationPage } from "./pages/content-moderation";
import { SubscriptionManagementPage } from "./pages/subscription-management";
import { SubscriptionPackagesPage } from "./pages/subscription-packages";
import { RAGManagementPage } from "./pages/rag-management";
import { AISettingsPage } from "./pages/ai-settings";
import { LLMServicesPage } from "./pages/llm-services";
import { Login } from "./pages/login";
import { ForgotPassword } from "./pages/forgot-password";
import { ResetPassword } from "./pages/reset-password";
import { AuthCallback } from "./pages/auth-callback";
import { ProfilePage } from "./pages/account/profile";
import { LinkedAccountsPage } from "./pages/account/linked-accounts";
import { UnauthorizedPage } from "./pages/unauthorized";
import { useTheme } from "./hooks/use-theme";
import { useTranslation } from "./hooks/use-translation";
import PaymentCallbackPage from "./pages/payment-callback";
import "./index.css";

const API_URL = "http://localhost:8000/api";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
});

function App() {
  const { actualTheme } = useTheme();
  const { language } = useTranslation();
  const notificationProvider = useNotificationProvider();
  
  // Configure Ant Design theme based on application theme
  const { defaultAlgorithm, darkAlgorithm } = theme;
  
  // Define theme tokens for light and dark modes
  const lightThemeTokens = {
    colorPrimary: '#2635ED',
    borderRadius: 8,
    fontFamily: "Cairo, Roboto, sans-serif",
  };
  
  const darkThemeTokens = {
    colorPrimary: '#9CA3AF', // Gray-400 in Tailwind
    borderRadius: 8,
    fontFamily: "Cairo, Roboto, sans-serif",
  };

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <RefineKbarProvider>
          <ConfigProvider
            theme={{
              algorithm: actualTheme === "dark" ? darkAlgorithm : defaultAlgorithm,
              token: actualTheme === "dark" ? darkThemeTokens : lightThemeTokens,
              components: {
                Menu: {
                  colorItemBgSelected: actualTheme === "dark" ? '#4B5563' : undefined, // Gray-600 for dark mode
                  colorItemTextSelected: actualTheme === "dark" ? '#FFFFFF' : undefined,
                },
                Card: {
                  boxShadowTertiary: actualTheme === "dark" ? '0 1px 2px 0 rgba(0, 0, 0, 0.3)' : undefined,
                },
                Layout: {
                  headerBg: actualTheme === "dark" ? '#171717' : '#ffffff',
                  headerBoxShadow: 'none', // Remove header shadow
                },
              },
            }}
            direction={language === "ar" ? "rtl" : "ltr"}
          >
            <AntdApp>
              <AuthProvider>
                <AISettingsProvider>
                  <Refine
                    dataProvider={dataProvider(API_URL)}
                    notificationProvider={notificationProvider}
                    routerProvider={routerBindings}
                    resources={[
                      {
                        name: "dashboard",
                        list: "/",
                        meta: {
                          label: "لوحة التحكم",
                          icon: "DashboardOutlined",
                        },
                      },
                      {
                        name: "users",
                        list: "/users",
                        meta: {
                          label: "المستخدمين",
                          icon: "UserOutlined",
                        },
                      },
                      {
                        name: "chats",
                        list: "/chats",
                        meta: {
                          label: "المحادثات",
                          icon: "MessageOutlined",
                        },
                      },
                      {
                        name: "legal-citations",
                        list: "/legal-citations",
                        meta: {
                          label: "الاستشهادات القانونية",
                          icon: "FileTextOutlined",
                        },
                      },
                      {
                        name: "content-moderation",
                        list: "/content-moderation",
                        meta: {
                          label: "مراقبة المحتوى",
                          icon: "FlagOutlined",
                        },
                      },
                      {
                        name: "rag-management",
                        list: "/rag-management",
                        meta: {
                          label: "إدارة RAG",
                          icon: "DatabaseOutlined",
                        },
                      },
                      {
                        name: "subscription-management",
                        list: "/subscription-management",
                        meta: {
                          label: "إدارة الاشتراكات",
                          icon: "CrownOutlined",
                        },
                      },
                      {
                        name: "subscription-packages",
                        list: "/subscription-packages",
                        meta: {
                          label: "باقات الاشتراك",
                          icon: "AppstoreOutlined",
                        },
                      },
                      {
                        name: "ai-settings",
                        list: "/ai-settings",
                        meta: {
                          label: "إعدادات الذكاء الاصطناعي",
                          icon: "RobotOutlined",
                        },
                      },
                      {
                        name: "llm-services",
                        list: "/llm-services",
                        meta: {
                          label: "واجهات نماذج الذكاء",
                          icon: "ApiOutlined",
                        },
                      },
                      {
                        name: "analytics",
                        list: "/analytics",
                        meta: {
                          label: "التحليلات",
                          icon: "BarChartOutlined",
                        },
                      },
                      {
                        name: "settings",
                        list: "/settings",
                        meta: {
                          label: "الإعدادات",
                          icon: "SettingOutlined",
                        },
                      },
                    ]}
                    options={{
                      syncWithLocation: true,
                      warnWhenUnsavedChanges: true,
                    }}
                  >
                    <Routes>
                      {/* Main dashboard routes - bypassing authentication */}
                      <Route
                        element={
                          <ThemedLayoutV2
                            Header={Header}
                            Sider={Sider}
                            Title={Title}
                          >
                            <Outlet />
                          </ThemedLayoutV2>
                        }
                      >
                        <Route index element={<Dashboard />} />
                        <Route path="/users" element={<UserList />} />
                        <Route path="/chats" element={<ChatList />} />
                        <Route path="/legal-citations" element={<LegalCitationList />} />
                        <Route path="/content-moderation" element={<ContentModerationPage />} />
                        <Route path="/rag-management" element={<RAGManagementPage />} />
                        <Route path="/subscription-management" element={<SubscriptionManagementPage />} />
                        <Route path="/subscription-packages" element={<SubscriptionPackagesPage />} />
                        <Route path="/ai-settings" element={<AISettingsPage />} />
                        <Route path="/llm-services" element={<LLMServicesPage />} />
                        <Route path="/analytics" element={<AnalyticsDashboard />} />
                        <Route path="/settings" element={<SettingsPage />} />
                        
                        {/* Account routes */}
                        <Route path="/account/profile" element={<ProfilePage />} />
                        <Route path="/account/linked-accounts" element={<LinkedAccountsPage />} />
                        
                        <Route path="*" element={<ErrorComponent />} />
                      </Route>
                      
                      {/* Auth routes - kept for reference but not used during development */}
                      <Route path="/login" element={<Login />} />
                      <Route path="/forgot-password" element={<ForgotPassword />} />
                      <Route path="/reset-password" element={<ResetPassword />} />
                      <Route path="/auth/callback" element={<AuthCallback />} />
                      <Route path="/unauthorized" element={<UnauthorizedPage />} />
                      
                      {/* Payment callback route */}
                      <Route path="/payment-callback" element={<PaymentCallbackPage />} />
                    </Routes>
                    <RefineKbar />
                    <UnsavedChangesNotifier />
                    <DocumentTitleHandler />
                  </Refine>
                </AISettingsProvider>
              </AuthProvider>
            </AntdApp>
          </ConfigProvider>
        </RefineKbarProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
}

export default function WrappedApp() {
  return (
    <ThemeProvider>
      <I18nProvider>
        <App />
      </I18nProvider>
    </ThemeProvider>
  );
}
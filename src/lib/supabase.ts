import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.');
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch (error) {
  throw new Error(`Invalid VITE_SUPABASE_URL format: "${supabaseUrl}". Please ensure it's a valid URL (e.g., https://your-project-ref.supabase.co)`);
}

// Validate that the URL looks like a Supabase URL
if (!supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('localhost')) {
  console.warn('VITE_SUPABASE_URL does not appear to be a valid Supabase URL. Expected format: https://your-project-ref.supabase.co');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'shuraih-auth',
  },
});

// Types for user profiles
export type UserProfile = {
  id: string;
  email: string;
  full_name: string;
  role: 'administrator' | 'moderator' | 'subscriber_enterprise' | 'subscriber_premium' | 'subscriber_basic' | 'user';
  avatar_url?: string;
  phone?: string;
  created_at: string;
  last_login?: string;
};

// Helper functions for authentication
export const signUpWithEmail = async (
  email: string,
  password: string,
  fullName: string,
  role: UserProfile['role'] = 'user'
) => {
  try {
    // Create the user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          role,
        },
      },
    });

    if (authError) throw authError;

    // The profile will be created automatically by the database trigger
    // We don't need to manually create it here anymore

    return { data: authData, error: null };
  } catch (error) {
    console.error('Error signing up:', error);
    return { data: null, error };
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // Update last login timestamp
    if (data.user) {
      await supabase
        .from('profiles')
        .update({ last_login: new Date().toISOString() })
        .eq('id', data.user.id);
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
};

export const signInWithProvider = async (provider: 'google' | 'github') => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error signing in with ${provider}:`, error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
};

export const getCurrentUser = async () => {
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error) throw error;
    
    if (data.user) {
      // Get the user profile from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();
        
      if (profileError) throw profileError;
      
      return { user: { ...data.user, profile: profileData }, error: null };
    }
    
    return { user: null, error: null };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { user: null, error };
  }
};

export const resetPassword = async (email: string) => {
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error resetting password:', error);
    return { data: null, error };
  }
};

export const updatePassword = async (newPassword: string) => {
  try {
    const { data, error } = await supabase.auth.updateUser({
      password: newPassword,
    });
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating password:', error);
    return { data: null, error };
  }
};

export const linkProvider = async (provider: 'google' | 'github') => {
  try {
    const { data, error } = await supabase.auth.linkIdentity({
      provider,
      options: {
        redirectTo: `${window.location.origin}/account`,
      },
    });
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error linking ${provider}:`, error);
    return { data: null, error };
  }
};

export const unlinkProvider = async (provider: 'google' | 'github') => {
  try {
    // This is a placeholder as Supabase doesn't have a direct method for unlinking
    // In a real implementation, you would need to handle this with custom logic
    console.warn('Unlinking providers is not directly supported by Supabase');
    return { error: new Error('Not implemented') };
  } catch (error) {
    console.error(`Error unlinking ${provider}:`, error);
    return { error };
  }
};
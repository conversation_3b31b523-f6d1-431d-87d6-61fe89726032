@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== Base Styles ===== */
body {
  font-family: 'Cairo', 'Roboto', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===== Animations ===== */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== Logo Styles - Keep original colors ===== */
.logo-component {
  filter: none !important;
  opacity: 1 !important;
  color-scheme: initial !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* ===== RTL Support ===== */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-1 > * + * {
  margin-right: 0.25rem;
  margin-left: 0;
}

/* ===== LTR Support ===== */
[dir="ltr"] {
  text-align: left;
}

[dir="ltr"] .space-x-1 > * + * {
  margin-left: 0.25rem;
  margin-right: 0;
}

/* ===== Selection Styling ===== */
::selection {
  background-color: rgba(38, 53, 237, 0.2);
  color: inherit;
}

.dark ::selection {
  background-color: rgba(156, 163, 175, 0.3);
  color: inherit;
}

/* ===== Interactive Toolbar ===== */
.interactive-toolbar {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.toolbar-hidden {
  opacity: 0;
  transform: translateY(8px);
  pointer-events: none;
}

.toolbar-visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.toolbar-button {
  transition: all 0.2s ease-in-out;
  transform: scale(1);
}

.toolbar-button:hover {
  transform: scale(1.05);
}

.toolbar-button:active {
  transform: scale(0.95);
}

.message-container {
  will-change: transform;
  backface-visibility: hidden;
}

/* Mobile touch support */
@media (hover: none) and (pointer: coarse) {
  .group .toolbar-hidden {
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== Switch RTL Fix ===== */
.switch-container {
  direction: ltr !important;
}

[dir="rtl"] .switch-container {
  direction: ltr !important;
}

.switch-button {
  direction: ltr !important;
  text-align: left !important;
}

[dir="rtl"] .switch-button {
  direction: ltr !important;
  text-align: left !important;
}

.switch-thumb {
  transition: transform 0.2s ease-in-out;
}

/* ===== Legal Citation Styling ===== */
.legal-citation-button {
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

.citation-popup-enter {
  animation: fade-in 0.3s ease-out;
}

/* ===== Sidebar Active Item Styling ===== */
.dark .ant-menu-item-selected {
  background-color: #4B5563 !important;
  color: #FFFFFF !important;
}

body:not(.dark) .ant-menu-item-selected {
  background-color: rgba(38, 53, 237, 0.1) !important;
  color: #2635ED !important;
}

/* Fix for RTL/LTR specific issues */
[dir="rtl"] .ant-menu-title-content {
  text-align: right;
}

[dir="ltr"] .ant-menu-title-content {
  text-align: left;
}

/* Fix for dropdown menus in RTL/LTR */
[dir="rtl"] .ant-dropdown-menu-item {
  text-align: right;
}

[dir="ltr"] .ant-dropdown-menu-item {
  text-align: left;
}

/* Fix for form labels and inputs in LTR mode */
[dir="ltr"] .ant-form-item-label {
  text-align: left;
}

[dir="ltr"] .ant-form-item-label > label {
  justify-content: flex-start;
}

/* Fix for table headers in LTR mode */
[dir="ltr"] .ant-table-thead > tr > th {
  text-align: left;
}

/* Fix for buttons with icons in LTR mode */
[dir="ltr"] .ant-btn > .anticon + span,
[dir="ltr"] .ant-btn > span + .anticon {
  margin-left: 8px;
  margin-right: 0;
}

/* Fix for buttons with icons in RTL mode */
[dir="rtl"] .ant-btn > .anticon + span,
[dir="rtl"] .ant-btn > span + .anticon {
  margin-right: 8px;
  margin-left: 0;
}

/* Fix for card headers in LTR mode */
[dir="ltr"] .ant-card-head-title {
  text-align: left;
}

/* Fix for tabs in LTR mode */
[dir="ltr"] .ant-tabs-tab {
  margin-left: 0;
  margin-right: 32px;
}

[dir="ltr"] .ant-tabs-tab:last-of-type {
  margin-right: 0;
}

/* Fix for tabs in RTL mode */
[dir="rtl"] .ant-tabs-tab {
  margin-right: 0;
  margin-left: 32px;
}

[dir="rtl"] .ant-tabs-tab:last-of-type {
  margin-left: 0;
}

/* Fix for pagination in LTR mode */
[dir="ltr"] .ant-pagination-prev {
  margin-right: 8px;
}

[dir="ltr"] .ant-pagination-next {
  margin-left: 8px;
}

/* Fix for pagination in RTL mode */
[dir="rtl"] .ant-pagination-prev {
  margin-left: 8px;
}

[dir="rtl"] .ant-pagination-next {
  margin-right: 8px;
}

/* Fix for modal footer buttons in LTR mode */
[dir="ltr"] .ant-modal-footer {
  text-align: right;
}

/* Fix for modal footer buttons in RTL mode */
[dir="rtl"] .ant-modal-footer {
  text-align: left;
}

/* Fix for descriptions in LTR mode */
[dir="ltr"] .ant-descriptions-item-label {
  text-align: left;
}

/* Fix for descriptions in RTL mode */
[dir="rtl"] .ant-descriptions-item-label {
  text-align: right;
}
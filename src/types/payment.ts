export type SubscriptionPlan = 'free' | 'basic' | 'premium' | 'enterprise';
export type SubscriptionStatus = 'active' | 'canceled' | 'expired' | 'past_due';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';
export type InvoiceStatus = 'draft' | 'issued' | 'paid' | 'void';
export type PaymentMethod = 'creditcard' | 'applepay' | 'stcpay' | 'mada';

export interface Subscription {
  id: string;
  user_id: string;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  start_date: string;
  end_date: string;
  auto_renew: boolean;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string;
  user_id: string;
  subscription_id?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  payment_method: PaymentMethod;
  payment_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Invoice {
  id: string;
  payment_id: string;
  invoice_number: string;
  invoice_date: string;
  due_date: string;
  total_amount: number;
  tax_amount: number;
  status: InvoiceStatus;
  notes?: string;
  wafeq_id?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentInitiateData {
  plan: SubscriptionPlan;
  payment_method: PaymentMethod;
  callback_url?: string;
}

export interface PaymentInitiateResponse {
  payment_id: string;
  moyasar_id: string;
  amount: number;
  currency: string;
  transaction_url: string;
  callback_url: string;
}

export interface PaymentStatusResponse {
  payment_id: string;
  moyasar_id: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  created_at: string;
}

export interface PaymentHistoryResponse {
  payments: Payment[];
  total: number;
}
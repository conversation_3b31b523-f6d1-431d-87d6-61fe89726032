// هذا ملف محاكاة لخدمة تتبع استخدام التوكينز
// في التطبيق الحقيقي، سيتم استبداله بتكامل مع Supabase أو خدمة أخرى

export interface TokenUsage {
  userId: string;
  month: string;
  totalTokensUsed: number;
  tokensLimit: number;
  usagePercentage: number;
  lastNotification: {
    thresholdPercentage: number;
    sentAt: Date;
  } | null;
}

export class TokenUsageService {
  private static instance: TokenUsageService;
  private usageData: Map<string, TokenUsage> = new Map();
  
  private constructor() {
    // تهيئة بيانات تجريبية
    this.initMockData();
  }

  public static getInstance(): TokenUsageService {
    if (!TokenUsageService.instance) {
      TokenUsageService.instance = new TokenUsageService();
    }
    return TokenUsageService.instance;
  }

  private initMockData(): void {
    // إضافة بيانات تجريبية لبعض المستخدمين
    const users = [
      { id: 'user_1', role: 'subscriber_premium', usage: 92000, limit: 100000 },
      { id: 'user_2', role: 'subscriber_basic', usage: 22500, limit: 25000 },
      { id: 'user_3', role: 'user', usage: 4800, limit: 5000 },
      { id: 'user_4', role: 'subscriber_enterprise', usage: 180000, limit: 500000 },
      { id: 'user_5', role: 'subscriber_basic', usage: 8500, limit: 25000 },
      { id: 'user_6', role: 'subscriber_premium', usage: 45000, limit: 100000 },
    ];
    
    const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
    
    users.forEach(user => {
      const usagePercentage = (user.usage / user.limit) * 100;
      let lastNotification = null;
      
      // إضافة إشعار للمستخدمين الذين تجاوزوا 90%
      if (usagePercentage >= 90) {
        lastNotification = {
          thresholdPercentage: 90,
          sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // بالأمس
        };
      } else if (usagePercentage >= 75) {
        lastNotification = {
          thresholdPercentage: 75,
          sentAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // قبل 3 أيام
        };
      }
      
      this.usageData.set(user.id, {
        userId: user.id,
        month: currentMonth,
        totalTokensUsed: user.usage,
        tokensLimit: user.limit,
        usagePercentage,
        lastNotification
      });
    });
  }

  // الحصول على استخدام التوكينز للمستخدم
  public async getUserTokenUsage(userId: string, month?: string): Promise<TokenUsage | null> {
    // إذا لم يتم تحديد الشهر، استخدم الشهر الحالي
    const targetMonth = month || new Date().toISOString().substring(0, 7);
    
    // البحث عن بيانات المستخدم
    const usage = this.usageData.get(userId);
    
    // إذا لم يتم العثور على بيانات أو كان الشهر مختلفًا، أرجع null
    if (!usage || usage.month !== targetMonth) {
      return null;
    }
    
    return Promise.resolve(usage);
  }

  // تحديث استخدام التوكينز
  public async updateTokenUsage(userId: string, tokensUsed: number): Promise<TokenUsage> {
    const currentMonth = new Date().toISOString().substring(0, 7);
    
    // الحصول على بيانات الاستخدام الحالية أو إنشاء سجل جديد
    let usage = this.usageData.get(userId);
    
    if (!usage || usage.month !== currentMonth) {
      // إنشاء سجل جديد للشهر الحالي
      usage = {
        userId,
        month: currentMonth,
        totalTokensUsed: 0,
        tokensLimit: this.getTokenLimitForUser(userId),
        usagePercentage: 0,
        lastNotification: null
      };
    }
    
    // تحديث إجمالي التوكينز المستخدمة
    usage.totalTokensUsed += tokensUsed;
    
    // حساب النسبة المئوية
    usage.usagePercentage = (usage.totalTokensUsed / usage.tokensLimit) * 100;
    
    // التحقق من عتبات الإشعار
    if (usage.usagePercentage >= 90 && (!usage.lastNotification || usage.lastNotification.thresholdPercentage < 90)) {
      usage.lastNotification = {
        thresholdPercentage: 90,
        sentAt: new Date()
      };
      // في التطبيق الحقيقي، هنا سيتم إرسال إشعار للمستخدم
    } else if (usage.usagePercentage >= 75 && (!usage.lastNotification || usage.lastNotification.thresholdPercentage < 75)) {
      usage.lastNotification = {
        thresholdPercentage: 75,
        sentAt: new Date()
      };
      // في التطبيق الحقيقي، هنا سيتم إرسال إشعار للمستخدم
    } else if (usage.usagePercentage >= 50 && (!usage.lastNotification || usage.lastNotification.thresholdPercentage < 50)) {
      usage.lastNotification = {
        thresholdPercentage: 50,
        sentAt: new Date()
      };
      // في التطبيق الحقيقي، هنا سيتم إرسال إشعار للمستخدم
    }
    
    // حفظ التغييرات
    this.usageData.set(userId, usage);
    
    return Promise.resolve(usage);
  }

  // الحصول على حد التوكينز بناءً على دور المستخدم
  private getTokenLimitForUser(userId: string): number {
    // في التطبيق الحقيقي، سيتم الحصول على دور المستخدم من قاعدة البيانات
    // هنا نستخدم قيم افتراضية للمحاكاة
    
    // تعيين حدود افتراضية بناءً على معرف المستخدم للمحاكاة
    if (userId === 'user_1' || userId === 'user_6') {
      return 100000; // Premium
    } else if (userId === 'user_2' || userId === 'user_5') {
      return 25000; // Basic
    } else if (userId === 'user_4') {
      return 500000; // Enterprise
    } else {
      return 5000; // Free
    }
  }

  // الحصول على جميع المستخدمين الذين تجاوزوا نسبة معينة من الاستخدام
  public async getUsersExceedingUsage(percentage: number): Promise<TokenUsage[]> {
    const users = Array.from(this.usageData.values())
      .filter(usage => usage.usagePercentage >= percentage);
    
    return Promise.resolve(users);
  }

  // إعادة تعيين استخدام التوكينز للشهر الجديد
  public async resetMonthlyUsage(): Promise<void> {
    const currentMonth = new Date().toISOString().substring(0, 7);
    
    // في التطبيق الحقيقي، سيتم تنفيذ هذا كوظيفة مجدولة في بداية كل شهر
    // هنا نقوم بمحاكاة إعادة تعيين الاستخدام لجميع المستخدمين
    
    for (const [userId, usage] of this.usageData.entries()) {
      if (usage.month !== currentMonth) {
        // إنشاء سجل جديد للشهر الحالي
        this.usageData.set(userId, {
          userId,
          month: currentMonth,
          totalTokensUsed: 0,
          tokensLimit: usage.tokensLimit,
          usagePercentage: 0,
          lastNotification: null
        });
      }
    }
    
    return Promise.resolve();
  }
}
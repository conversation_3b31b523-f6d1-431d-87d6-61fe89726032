// هذا ملف محاكاة لخدمة إدارة الاشتراكات
// في التطبيق الحقيقي، سيتم استبداله بتكامل مع Supabase أو خدمة أخرى

export type SubscriptionTier = 'free' | 'basic' | 'premium' | 'enterprise';
export type UserRole = 'user' | 'subscriber_basic' | 'subscriber_premium' | 'subscriber_enterprise' | 'moderator' | 'administrator';

export interface Subscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: Date;
  endDate: Date | null;
  monthlyPrice: number;
  tokensUsed: number;
  tokensLimit: number;
  features: string[];
  lastPayment: Date | null;
  nextBilling: Date | null;
  autoRenew: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

export interface SubscriptionLimits {
  maxDailyQueries: number;
  maxFileUploads: number;
  maxFileSize: number; // in MB
  maxTokensPerChat: number;
  voiceEnabled: boolean;
  citationsEnabled: boolean;
  customModelsEnabled: boolean;
  moderationAccess: boolean;
  adminAccess: boolean;
}

export class SubscriptionService {
  private static instance: SubscriptionService;
  private subscriptions: Map<string, Subscription> = new Map();
  private users: Map<string, User> = new Map();
  
  // حدود الاشتراكات والأدوار
  private roleLimits: Record<UserRole, SubscriptionLimits> = {
    // المستخدم العادي
    user: {
      maxDailyQueries: 10,
      maxFileUploads: 0,
      maxFileSize: 0,
      maxTokensPerChat: 1000,
      voiceEnabled: false,
      citationsEnabled: true,
      customModelsEnabled: false,
      moderationAccess: false,
      adminAccess: false
    },
    // المشترك الأساسي
    subscriber_basic: {
      maxDailyQueries: 100,
      maxFileUploads: 5,
      maxFileSize: 10,
      maxTokensPerChat: 2000,
      voiceEnabled: true,
      citationsEnabled: true,
      customModelsEnabled: false,
      moderationAccess: false,
      adminAccess: false
    },
    // المشترك المتقدم
    subscriber_premium: {
      maxDailyQueries: 500,
      maxFileUploads: 20,
      maxFileSize: 50,
      maxTokensPerChat: 4000,
      voiceEnabled: true,
      citationsEnabled: true,
      customModelsEnabled: true,
      moderationAccess: false,
      adminAccess: false
    },
    // المشترك المؤسسي
    subscriber_enterprise: {
      maxDailyQueries: -1, // غير محدود
      maxFileUploads: 100,
      maxFileSize: 500,
      maxTokensPerChat: 8000,
      voiceEnabled: true,
      citationsEnabled: true,
      customModelsEnabled: true,
      moderationAccess: false,
      adminAccess: false
    },
    // المشرف
    moderator: {
      maxDailyQueries: -1, // غير محدود
      maxFileUploads: 50,
      maxFileSize: 100,
      maxTokensPerChat: 6000,
      voiceEnabled: true,
      citationsEnabled: true,
      customModelsEnabled: true,
      moderationAccess: true, // يمكنه مراجعة المحتوى
      adminAccess: false
    },
    // المسؤول
    administrator: {
      maxDailyQueries: -1, // غير محدود
      maxFileUploads: -1, // غير محدود
      maxFileSize: -1, // غير محدود
      maxTokensPerChat: -1, // غير محدود
      voiceEnabled: true,
      citationsEnabled: true,
      customModelsEnabled: true,
      moderationAccess: true,
      adminAccess: true // يمكنه إدارة النظام
    }
  };

  private constructor() {
    // تهيئة بيانات تجريبية
    this.initMockData();
  }

  public static getInstance(): SubscriptionService {
    if (!SubscriptionService.instance) {
      SubscriptionService.instance = new SubscriptionService();
    }
    return SubscriptionService.instance;
  }

  private initMockData(): void {
    // إضافة مستخدمين تجريبيين مع الأدوار الصحيحة
    this.users.set('user_1', { id: 'user_1', name: 'أحمد محمد', email: '<EMAIL>', role: 'administrator' });
    this.users.set('user_2', { id: 'user_2', name: 'سارة علي', email: '<EMAIL>', role: 'moderator' });
    this.users.set('user_3', { id: 'user_3', name: 'محمد خالد', email: '<EMAIL>', role: 'subscriber_premium' });
    this.users.set('user_4', { id: 'user_4', name: 'فاطمة أحمد', email: '<EMAIL>', role: 'subscriber_basic' });
    this.users.set('user_5', { id: 'user_5', name: 'عبدالله محمد', email: '<EMAIL>', role: 'user' });
    this.users.set('user_6', { id: 'user_6', name: 'خالد العتيبي', email: '<EMAIL>', role: 'subscriber_enterprise' });
    
    // إضافة اشتراكات تجريبية
    this.subscriptions.set('sub_1', {
      id: 'sub_1',
      userId: 'user_1',
      tier: 'enterprise',
      status: 'active',
      startDate: new Date('2024-01-15'),
      endDate: new Date('2024-07-15'),
      monthlyPrice: 299,
      tokensUsed: 45000,
      tokensLimit: 500000,
      features: ['جميع الميزات', 'دعم مخصص', 'API مخصص'],
      lastPayment: new Date('2024-01-15'),
      nextBilling: new Date('2024-02-15'),
      autoRenew: true
    });
    
    this.subscriptions.set('sub_2', {
      id: 'sub_2',
      userId: 'user_2',
      tier: 'premium',
      status: 'active',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-03-01'),
      monthlyPrice: 99,
      tokensUsed: 8500,
      tokensLimit: 100000,
      features: ['محادثة صوتية', 'رفع ملفات', 'دعم أولوية'],
      lastPayment: new Date('2024-02-01'),
      nextBilling: new Date('2024-03-01'),
      autoRenew: true
    });
    
    this.subscriptions.set('sub_3', {
      id: 'sub_3',
      userId: 'user_5',
      tier: 'free',
      status: 'active',
      startDate: new Date('2024-01-20'),
      endDate: null,
      monthlyPrice: 0,
      tokensUsed: 4800,
      tokensLimit: 5000,
      features: ['محادثة أساسية'],
      lastPayment: null,
      nextBilling: null,
      autoRenew: false
    });
  }

  // الحصول على اشتراك مستخدم
  public async getUserSubscription(userId: string): Promise<Subscription | null> {
    const subscription = Array.from(this.subscriptions.values())
      .find(sub => sub.userId === userId);
    
    return Promise.resolve(subscription || null);
  }

  // الحصول على دور المستخدم
  public async getUserRole(userId: string): Promise<UserRole | null> {
    const user = this.users.get(userId);
    return Promise.resolve(user?.role || null);
  }

  // التحقق من وصول المستخدم إلى ميزة
  public async checkFeatureAccess(userId: string, feature: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;
    
    const limits = this.roleLimits[user.role];
    
    switch (feature) {
      case 'voice_chat':
        return limits.voiceEnabled;
      case 'file_upload':
        return limits.maxFileUploads > 0;
      case 'citations':
        return limits.citationsEnabled;
      case 'custom_models':
        return limits.customModelsEnabled;
      case 'moderation':
        return limits.moderationAccess;
      case 'admin':
        return limits.adminAccess;
      default:
        return false;
    }
  }

  // التحقق من حدود الاستخدام
  public async checkUsageLimit(userId: string, action: string, value: number = 1): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) return false;
    
    const limits = this.roleLimits[user.role];
    
    switch (action) {
      case 'daily_queries':
        return limits.maxDailyQueries === -1 || value <= limits.maxDailyQueries;
      case 'file_size':
        return limits.maxFileSize === -1 || value <= limits.maxFileSize;
      case 'tokens_per_chat':
        return limits.maxTokensPerChat === -1 || value <= limits.maxTokensPerChat;
      default:
        return false;
    }
  }

  // تحديث استخدام التوكينز
  public async updateTokenUsage(userId: string, tokens: number): Promise<void> {
    const subscription = await this.getUserSubscription(userId);
    if (!subscription) return;
    
    subscription.tokensUsed += tokens;
    this.subscriptions.set(subscription.id, subscription);
    
    return Promise.resolve();
  }

  // ترقية الاشتراك
  public async upgradeSubscription(userId: string, newTier: SubscriptionTier): Promise<Subscription | null> {
    const subscription = await this.getUserSubscription(userId);
    if (!subscription) return null;
    
    // تحديث المستوى والحدود
    const updatedSubscription: Subscription = {
      ...subscription,
      tier: newTier,
      tokensLimit: this.getTierTokenLimit(newTier),
      monthlyPrice: this.getTierPrice(newTier),
      features: this.getTierFeatures(newTier)
    };
    
    this.subscriptions.set(subscription.id, updatedSubscription);
    
    return Promise.resolve(updatedSubscription);
  }

  // الحصول على جميع الاشتراكات
  public async getAllSubscriptions(): Promise<Subscription[]> {
    return Promise.resolve(Array.from(this.subscriptions.values()));
  }

  // الحصول على معلومات المستخدم
  public async getUser(userId: string): Promise<User | null> {
    return Promise.resolve(this.users.get(userId) || null);
  }

  // الحصول على جميع المستخدمين
  public async getAllUsers(): Promise<User[]> {
    return Promise.resolve(Array.from(this.users.values()));
  }

  // الحصول على حد التوكينز حسب المستوى
  private getTierTokenLimit(tier: SubscriptionTier): number {
    switch (tier) {
      case 'free':
        return 5000;
      case 'basic':
        return 25000;
      case 'premium':
        return 100000;
      case 'enterprise':
        return 500000;
      default:
        return 5000;
    }
  }

  // الحصول على سعر المستوى
  private getTierPrice(tier: SubscriptionTier): number {
    switch (tier) {
      case 'free':
        return 0;
      case 'basic':
        return 49;
      case 'premium':
        return 99;
      case 'enterprise':
        return 299;
      default:
        return 0;
    }
  }

  // الحصول على ميزات المستوى
  private getTierFeatures(tier: SubscriptionTier): string[] {
    switch (tier) {
      case 'free':
        return ['محادثة أساسية'];
      case 'basic':
        return ['محادثة صوتية', 'دعم بريد إلكتروني'];
      case 'premium':
        return ['محادثة صوتية', 'رفع ملفات', 'دعم أولوية'];
      case 'enterprise':
        return ['جميع الميزات', 'دعم مخصص', 'API مخصص'];
      default:
        return ['محادثة أساسية'];
    }
  }
}
// هذا ملف محاكاة لخدمة RAG
// في التطبيق الحقيقي، سيتم استبداله بتكامل مع Qdrant وLangChain

export interface Document {
  id: string;
  content: string;
  metadata: {
    title: string;
    source: string;
    url?: string;
    section?: string;
    category?: string;
    date?: string;
  };
}

export interface SearchResult {
  document: Document;
  score: number;
}

export class RAGService {
  private static instance: RAGService;
  private documents: Document[] = [];

  private constructor() {
    // تهيئة المستندات التجريبية
    this.documents = [
      {
        id: 'doc1',
        content: 'لا يجوز للمؤجر زيادة الأجرة خلال مدة العقد ما لم يتم الاتفاق على ذلك صراحةً في العقد.',
        metadata: {
          title: 'نظام إيجار العقارات',
          source: 'نظام إيجار العقارات، الصادر بالمرسوم الملكي رقم (م/61) وتاريخ 1427/09/18هـ',
          section: 'الباب الثاني - الأجرة',
          category: 'العقارات',
          date: '2006-10-11'
        }
      },
      {
        id: 'doc2',
        content: 'إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة، فإن المحكمة تنظر في الدعوى وتصدر حكمها غيابياً.',
        metadata: {
          title: 'نظام المرافعات الشرعية',
          source: 'نظام المرافعات الشرعية، الصادر بالمرسوم الملكي رقم (م/1) وتاريخ 1435/01/22هـ',
          section: 'الباب الخامس - الأحكام الغيابية',
          category: 'المرافعات',
          date: '2013-11-25'
        }
      },
      {
        id: 'doc3',
        content: 'لا يجوز لصاحب العمل فسخ العقد دون مكافأة أو إشعار العامل أو تعويضه إلا في الحالات الواردة في هذه المادة، وبشرط أن تتاح للعامل الفرصة لكي يبدي أسباب معارضته للفسخ.',
        metadata: {
          title: 'نظام العمل',
          source: 'نظام العمل، الصادر بالمرسوم الملكي رقم (م/51) وتاريخ 1426/08/23هـ',
          section: 'الباب السادس - انتهاء عقد العمل',
          category: 'العمل',
          date: '2005-09-27'
        }
      }
    ];
  }

  public static getInstance(): RAGService {
    if (!RAGService.instance) {
      RAGService.instance = new RAGService();
    }
    return RAGService.instance;
  }

  // إضافة مستند جديد
  public async addDocument(document: Document): Promise<void> {
    this.documents.push(document);
    return Promise.resolve();
  }

  // البحث في المستندات
  public async search(query: string, limit: number = 3): Promise<SearchResult[]> {
    // محاكاة البحث الدلالي
    // في التطبيق الحقيقي، سيتم استخدام Qdrant للبحث الدلالي
    
    // تبسيط: البحث عن الكلمات المفتاحية في المحتوى
    const keywords = query.toLowerCase().split(' ');
    
    const results: SearchResult[] = this.documents
      .map(doc => {
        const content = doc.content.toLowerCase();
        let score = 0;
        
        // حساب درجة التطابق البسيطة
        keywords.forEach(keyword => {
          if (content.includes(keyword)) {
            score += 1;
          }
        });
        
        return { document: doc, score };
      })
      .filter(result => result.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
    
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return results;
  }

  // الحصول على جميع المستندات
  public async getAllDocuments(): Promise<Document[]> {
    return Promise.resolve([...this.documents]);
  }

  // حذف مستند
  public async deleteDocument(id: string): Promise<void> {
    this.documents = this.documents.filter(doc => doc.id !== id);
    return Promise.resolve();
  }
}

// خدمة الاستشهادات
export class CitationService {
  private static instance: CitationService;
  private ragService: RAGService;

  private constructor() {
    this.ragService = RAGService.getInstance();
  }

  public static getInstance(): CitationService {
    if (!CitationService.instance) {
      CitationService.instance = new CitationService();
    }
    return CitationService.instance;
  }

  // إنشاء استجابة مع استشهادات
  public async generateResponseWithCitations(query: string): Promise<{
    response: string;
    citations: Array<{
      id: string;
      title: string;
      content: string;
      source: string;
    }>;
  }> {
    // 1. البحث عن المستندات ذات الصلة
    const searchResults = await this.ragService.search(query);
    
    // 2. محاكاة إنشاء استجابة (في التطبيق الحقيقي، سيتم استخدام نموذج LLM)
    let response = 'وفقاً للأنظمة السعودية، ';
    
    if (query.includes('إيجار') || query.includes('زيادة الأجرة')) {
      response += 'لا يجوز للمؤجر زيادة الأجرة خلال مدة العقد ما لم يتم الاتفاق على ذلك صراحةً في العقد، وذلك وفقاً للمادة (11) من نظام إيجار العقارات.';
    } else if (query.includes('غياب') || query.includes('المدعى عليه')) {
      response += 'إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة، فإن المحكمة تنظر في الدعوى وتصدر حكمها غيابياً، وذلك وفقاً للمادة (57) من نظام المرافعات الشرعية.';
    } else if (query.includes('فسخ العقد') || query.includes('عقد العمل')) {
      response += 'لا يجوز لصاحب العمل فسخ العقد دون مكافأة أو إشعار العامل أو تعويضه إلا في الحالات الواردة في المادة (77) من نظام العمل، وبشرط أن تتاح للعامل الفرصة لكي يبدي أسباب معارضته للفسخ.';
    } else {
      response += 'يرجى تحديد استفسارك بشكل أكثر دقة للحصول على إجابة قانونية محددة.';
    }
    
    // 3. إنشاء الاستشهادات
    const citations = searchResults.map(result => ({
      id: result.document.id,
      title: result.document.metadata.title,
      content: result.document.content,
      source: result.document.metadata.source
    }));
    
    // محاكاة تأخير المعالجة
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return { response, citations };
  }
}
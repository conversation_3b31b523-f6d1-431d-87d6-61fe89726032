// هذا ملف محاكاة لخدمة مراقبة المحتوى
// في التطبيق الحقيقي، سيتم استبداله بتكامل مع Redis Streams أو RabbitMQ

export type ReportReason = 'محتوى غير دقيق' | 'استجابة غير مناسبة' | 'مشكلة تقنية' | 'أخرى';
export type ReportStatus = 'pending' | 'approved' | 'rejected';
export type ReportPriority = 'low' | 'medium' | 'high';

export interface ContentReport {
  id: string;
  conversationId: string;
  messageId: string;
  reporterId: string;
  reporterName: string;
  reason: ReportReason;
  description: string;
  status: ReportStatus;
  priority: ReportPriority;
  createdAt: Date;
  reviewedBy: string | null;
  reviewedAt: Date | null;
  conversation: {
    title: string;
    userName: string;
    messages: Array<{
      id: string;
      sender: 'user' | 'bot';
      text: string;
      timestamp: Date;
      flagged?: boolean;
    }>;
  };
}

export interface ReviewDecision {
  reportId: string;
  decision: 'approved' | 'rejected';
  notes: string;
  reviewerId: string;
}

export class ContentModerationService {
  private static instance: ContentModerationService;
  private reports: Map<string, ContentReport> = new Map();
  private reportListeners: Array<(report: ContentReport) => void> = [];

  private constructor() {
    // تهيئة بيانات تجريبية
    this.initMockData();
  }

  public static getInstance(): ContentModerationService {
    if (!ContentModerationService.instance) {
      ContentModerationService.instance = new ContentModerationService();
    }
    return ContentModerationService.instance;
  }

  private initMockData(): void {
    // إضافة تقارير تجريبية
    const reports: ContentReport[] = [
      {
        id: '1',
        conversationId: 'conv_1',
        messageId: 'msg_1',
        reporterId: 'user_1',
        reporterName: 'أحمد محمد',
        reason: 'محتوى غير دقيق',
        description: 'الإجابة تحتوي على معلومات قانونية غير صحيحة',
        status: 'pending',
        priority: 'high',
        createdAt: new Date('2024-02-20T10:30:00'),
        reviewedBy: null,
        reviewedAt: null,
        conversation: {
          title: 'استشارة قانونية حول عقد إيجار',
          userName: 'أحمد محمد',
          messages: [
            {
              id: 'msg_1',
              sender: 'user',
              text: 'هل يحق للمالك زيادة قيمة الإيجار خلال فترة العقد؟',
              timestamp: new Date('2024-02-20T10:00:00'),
            },
            {
              id: 'msg_2',
              sender: 'bot',
              text: 'نعم، يحق للمالك زيادة الإيجار في أي وقت حسب رغبته.',
              timestamp: new Date('2024-02-20T10:00:30'),
              flagged: true,
            },
          ],
        },
      },
      {
        id: '2',
        conversationId: 'conv_2',
        messageId: 'msg_3',
        reporterId: 'user_2',
        reporterName: 'سارة علي',
        reason: 'استجابة غير مناسبة',
        description: 'الرد لا يجيب على السؤال المطروح',
        status: 'pending',
        priority: 'medium',
        createdAt: new Date('2024-02-19T15:45:00'),
        reviewedBy: null,
        reviewedAt: null,
        conversation: {
          title: 'استفسار عن إجراءات التقاضي',
          userName: 'سارة علي',
          messages: [],
        },
      },
    ];
    
    reports.forEach(report => {
      this.reports.set(report.id, report);
    });
  }

  // إضافة تقرير جديد
  public async reportContent(
    conversationId: string,
    messageId: string,
    reporterId: string,
    reporterName: string,
    reason: ReportReason,
    description: string,
    conversation: ContentReport['conversation']
  ): Promise<ContentReport> {
    // إنشاء تقرير جديد
    const report: ContentReport = {
      id: `report_${Date.now()}`,
      conversationId,
      messageId,
      reporterId,
      reporterName,
      reason,
      description,
      status: 'pending',
      priority: this.calculatePriority(reason, description),
      createdAt: new Date(),
      reviewedBy: null,
      reviewedAt: null,
      conversation,
    };
    
    // إضافة التقرير إلى القائمة
    this.reports.set(report.id, report);
    
    // إشعار المستمعين
    this.notifyListeners(report);
    
    return Promise.resolve(report);
  }

  // مراجعة تقرير
  public async reviewReport(decision: ReviewDecision): Promise<ContentReport> {
    const report = this.reports.get(decision.reportId);
    if (!report) {
      throw new Error(`Report with ID ${decision.reportId} not found`);
    }
    
    // تحديث التقرير
    const updatedReport: ContentReport = {
      ...report,
      status: decision.decision === 'approved' ? 'approved' : 'rejected',
      reviewedBy: decision.reviewerId,
      reviewedAt: new Date(),
    };
    
    this.reports.set(report.id, updatedReport);
    
    // إشعار المستمعين
    this.notifyListeners(updatedReport);
    
    return Promise.resolve(updatedReport);
  }

  // الحصول على جميع التقارير
  public async getAllReports(): Promise<ContentReport[]> {
    return Promise.resolve(Array.from(this.reports.values()));
  }

  // الحصول على تقارير قيد الانتظار
  public async getPendingReports(): Promise<ContentReport[]> {
    const pendingReports = Array.from(this.reports.values())
      .filter(report => report.status === 'pending')
      .sort((a, b) => {
        // ترتيب حسب الأولوية ثم التاريخ
        if (a.priority !== b.priority) {
          const priorityOrder: Record<ReportPriority, number> = {
            high: 0,
            medium: 1,
            low: 2,
          };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        }
        return b.createdAt.getTime() - a.createdAt.getTime();
      });
    
    return Promise.resolve(pendingReports);
  }

  // الاستماع للتقارير الجديدة
  public subscribeToReports(callback: (report: ContentReport) => void): () => void {
    this.reportListeners.push(callback);
    
    // إرجاع دالة لإلغاء الاشتراك
    return () => {
      this.reportListeners = this.reportListeners.filter(listener => listener !== callback);
    };
  }

  // إشعار المستمعين بتقرير جديد
  private notifyListeners(report: ContentReport): void {
    this.reportListeners.forEach(listener => {
      try {
        listener(report);
      } catch (error) {
        console.error('Error notifying listener:', error);
      }
    });
  }

  // حساب أولوية التقرير
  private calculatePriority(reason: ReportReason, description: string): ReportPriority {
    // في التطبيق الحقيقي، يمكن استخدام خوارزمية أكثر تعقيدًا
    if (reason === 'محتوى غير دقيق') {
      return 'high';
    } else if (reason === 'استجابة غير مناسبة') {
      return 'medium';
    } else {
      return 'low';
    }
  }
}
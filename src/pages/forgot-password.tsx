import { useState } from "react";
import { Form, Input, But<PERSON>, Card, message, Alert } from "antd";
import { MailOutlined } from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { useTheme } from "../hooks/use-theme";
import { Link } from "react-router-dom";
import { resetPassword } from "../lib/supabase";

export const ForgotPassword = () => {
  const { t } = useTranslation();
  const { actualTheme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";

  const handleSubmit = async (values: { email: string }) => {
    try {
      setLoading(true);
      setError(null);
      
      const { error: resetError } = await resetPassword(values.email);
      
      if (resetError) {
        throw resetError;
      }
      
      setSuccess(true);
    } catch (err) {
      console.error("Password reset error:", err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <div className="text-center mb-8">
          <img
            src={logoSrc}
            alt="شُريح"
            className="logo-component mx-auto mb-4"
            style={{ height: "80px" }}
          />
          <h1 className="text-2xl font-bold">استعادة كلمة المرور</h1>
          <p className="text-muted-foreground">أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور</p>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            className="mb-4"
            closable
            onClose={() => setError(null)}
          />
        )}
        
        {success ? (
          <div className="text-center">
            <Alert
              message="تم إرسال رابط إعادة تعيين كلمة المرور"
              description="يرجى التحقق من بريدك الإلكتروني واتباع التعليمات لإعادة تعيين كلمة المرور."
              type="success"
              showIcon
              className="mb-4"
            />
            <Link to="/login">
              <Button type="primary" size="large">
                العودة إلى تسجيل الدخول
              </Button>
            </Link>
          </div>
        ) : (
          <Form
            name="forgot-password"
            onFinish={handleSubmit}
            layout="vertical"
          >
            <Form.Item
              name="email"
              label="البريد الإلكتروني"
              rules={[
                { required: true, message: "الرجاء إدخال البريد الإلكتروني" },
                { type: "email", message: "الرجاء إدخال بريد إلكتروني صالح" },
              ]}
            >
              <Input
                prefix={<MailOutlined className="site-form-item-icon" />}
                placeholder="<EMAIL>"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full"
                size="large"
                loading={loading}
              >
                إرسال رابط إعادة التعيين
              </Button>
            </Form.Item>
            
            <div className="text-center mt-4">
              <Link to="/login" className="text-primary">
                العودة إلى تسجيل الدخول
              </Link>
            </div>
          </Form>
        )}
      </Card>
    </div>
  );
};
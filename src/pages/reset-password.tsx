import { useState, useEffect } from "react";
import { Form, Input, Button, Card, message, Alert } from "antd";
import { LockOutlined } from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { useTheme } from "../hooks/use-theme";
import { useNavigate } from "react-router-dom";
import { updatePassword, supabase } from "../lib/supabase";
import { z } from "zod";

const passwordSchema = z.object({
  password: z.string()
    .min(8, "كلمة المرور يجب أن تكون 8 أحرف على الأقل")
    .regex(/[A-Z]/, "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل")
    .regex(/[0-9]/, "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل")
    .regex(/[^A-Za-z0-9]/, "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
});

export const ResetPassword = () => {
  const { t } = useTranslation();
  const { actualTheme } = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasResetToken, setHasResetToken] = useState(false);

  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";

  useEffect(() => {
    // Check if we have a reset token in the URL
    const checkResetToken = async () => {
      const hash = window.location.hash;
      if (hash && hash.includes('type=recovery')) {
        setHasResetToken(true);
      } else {
        setError("لم يتم العثور على رمز إعادة تعيين كلمة المرور");
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    };

    checkResetToken();
  }, [navigate]);

  const handleSubmit = async (values: { password: string; confirmPassword: string }) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate form data
      passwordSchema.parse(values);
      
      const { data, error: updateError } = await updatePassword(values.password);
      
      if (updateError) {
        throw updateError;
      }
      
      message.success("تم تغيير كلمة المرور بنجاح");
      
      // Redirect to login page after successful password reset
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (err) {
      console.error("Password reset error:", err);
      if (err instanceof z.ZodError) {
        setError(err.errors[0].message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("حدث خطأ أثناء إعادة تعيين كلمة المرور");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <div className="text-center mb-8">
          <img
            src={logoSrc}
            alt="شُريح"
            className="logo-component mx-auto mb-4"
            style={{ height: "80px" }}
          />
          <h1 className="text-2xl font-bold">إعادة تعيين كلمة المرور</h1>
          <p className="text-muted-foreground">أدخل كلمة المرور الجديدة</p>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            className="mb-4"
            closable
            onClose={() => setError(null)}
          />
        )}
        
        {hasResetToken ? (
          <Form
            name="reset-password"
            onFinish={handleSubmit}
            layout="vertical"
          >
            <Form.Item
              name="password"
              label="كلمة المرور الجديدة"
              rules={[
                { required: true, message: "الرجاء إدخال كلمة المرور الجديدة" },
                { min: 8, message: "كلمة المرور يجب أن تكون 8 أحرف على الأقل" },
                { 
                  pattern: /[A-Z]/, 
                  message: "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل" 
                },
                { 
                  pattern: /[0-9]/, 
                  message: "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل" 
                },
                { 
                  pattern: /[^A-Za-z0-9]/, 
                  message: "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل" 
                }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="site-form-item-icon" />}
                placeholder="••••••••"
                size="large"
              />
            </Form.Item>
            
            <Form.Item
              name="confirmPassword"
              label="تأكيد كلمة المرور"
              dependencies={['password']}
              rules={[
                { required: true, message: "الرجاء تأكيد كلمة المرور" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('كلمات المرور غير متطابقة'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="site-form-item-icon" />}
                placeholder="••••••••"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full"
                size="large"
                loading={loading}
              >
                تغيير كلمة المرور
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <div className="text-center">
            <Alert
              message="رمز إعادة التعيين غير صالح"
              description="سيتم توجيهك إلى صفحة تسجيل الدخول..."
              type="error"
              showIcon
            />
          </div>
        )}
      </Card>
    </div>
  );
};
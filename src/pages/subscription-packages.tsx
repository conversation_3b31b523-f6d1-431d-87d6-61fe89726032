import { useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Divider,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  List,
  Avatar,
  Popconfirm,
  message,
  Alert,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CrownOutlined,
  DollarOutlined,
  UserOutlined,
  CheckOutlined,
  CloseOutlined,
  SettingOutlined,
  SaveOutlined,
  UndoOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatNumber } from "../lib/utils";
import { TokenUsageAlert } from "../components/subscription/TokenUsageAlert";

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

// Define data types
interface SubscriptionPackage {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number | null;
  maxChatsPerMonth: number;
  maxTokensPerMonth: number;
  features: string[];
  blockedFeatures: string[];
  isActive: boolean;
  isDefault: boolean;
  sortOrder: number;
  color: string;
  subscribersCount: number;
}

interface FeatureToggle {
  id: string;
  name: string;
  description: string;
  category: string;
  isAvailableInFree: boolean;
}

export const SubscriptionPackagesPage = () => {
  const { t, language } = useTranslation();
  const [activeTab, setActiveTab] = useState("packages");
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<SubscriptionPackage | null>(null);
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);
  const [showTokenAlert, setShowTokenAlert] = useState(true);

  // Sample data for subscription packages
  const subscriptionPackages: SubscriptionPackage[] = [
    {
      id: "free",
      name: language === "ar" ? "المجانية" : "Free",
      monthlyPrice: 0,
      yearlyPrice: null,
      maxChatsPerMonth: 100,
      maxTokensPerMonth: 5000,
      features: [language === "ar" ? "النماذج الأساسية" : "Basic Models", language === "ar" ? "دعم المجتمع" : "Community Support"],
      blockedFeatures: [language === "ar" ? "الصوت" : "Voice", language === "ar" ? "رفع الملفات" : "File Upload", language === "ar" ? "النماذج المتقدمة" : "Advanced Models"],
      isActive: true,
      isDefault: true,
      sortOrder: 1,
      color: "default",
      subscribersCount: 1250,
    },
    {
      id: "basic",
      name: language === "ar" ? "الأساسية" : "Basic",
      monthlyPrice: 199,
      yearlyPrice: 1990,
      maxChatsPerMonth: 1000,
      maxTokensPerMonth: 50000,
      features: [
        language === "ar" ? "رفع ملفات (10MB)" : "File Upload (10MB)", 
        language === "ar" ? "نماذج متوسطة" : "Medium Models", 
        language === "ar" ? "النماذج الأساسية" : "Basic Models", 
        language === "ar" ? "دعم المجتمع" : "Community Support"
      ],
      blockedFeatures: [
        language === "ar" ? "النماذج القانونية" : "Legal Models", 
        language === "ar" ? "التحليل المتقدم" : "Advanced Analysis"
      ],
      isActive: true,
      isDefault: false,
      sortOrder: 2,
      color: "blue",
      subscribersCount: 450,
    },
    {
      id: "premium",
      name: language === "ar" ? "المتقدمة" : "Premium",
      monthlyPrice: 499,
      yearlyPrice: 4990,
      maxChatsPerMonth: 5000,
      maxTokensPerMonth: 200000,
      features: [
        language === "ar" ? "الصوت" : "Voice", 
        language === "ar" ? "نماذج متقدمة" : "Advanced Models", 
        language === "ar" ? "RAG" : "RAG", 
        language === "ar" ? "رفع ملفات (10MB)" : "File Upload (10MB)", 
        language === "ar" ? "نماذج متوسطة" : "Medium Models", 
        language === "ar" ? "النماذج الأساسية" : "Basic Models", 
        language === "ar" ? "دعم المجتمع" : "Community Support"
      ],
      blockedFeatures: [
        language === "ar" ? "النماذج المخصصة" : "Custom Models", 
        language === "ar" ? "دعم 24/7" : "24/7 Support"
      ],
      isActive: true,
      isDefault: false,
      sortOrder: 3,
      color: "purple",
      subscribersCount: 180,
    },
    {
      id: "enterprise",
      name: language === "ar" ? "المؤسسية" : "Enterprise",
      monthlyPrice: 999,
      yearlyPrice: 9990,
      maxChatsPerMonth: -1, // Unlimited
      maxTokensPerMonth: -1, // Unlimited
      features: [
        language === "ar" ? "جميع المزايا" : "All Features", 
        language === "ar" ? "تخصيص النماذج" : "Custom Models", 
        language === "ar" ? "الصوت" : "Voice", 
        language === "ar" ? "نماذج متقدمة" : "Advanced Models", 
        language === "ar" ? "RAG" : "RAG", 
        language === "ar" ? "رفع ملفات (10MB)" : "File Upload (10MB)", 
        language === "ar" ? "نماذج متوسطة" : "Medium Models", 
        language === "ar" ? "النماذج الأساسية" : "Basic Models", 
        language === "ar" ? "دعم المجتمع" : "Community Support"
      ],
      blockedFeatures: [],
      isActive: true,
      isDefault: false,
      sortOrder: 4,
      color: "gold",
      subscribersCount: 25,
    },
  ];

  // Sample data for features
  const features: FeatureToggle[] = [
    {
      id: "basic_models",
      name: language === "ar" ? "النماذج الأساسية" : "Basic Models",
      description: language === "ar" ? "الوصول إلى نماذج الذكاء الاصطناعي الأساسية" : "Access to basic AI models",
      category: language === "ar" ? "النماذج" : "Models",
      isAvailableInFree: true,
    },
    {
      id: "medium_models",
      name: language === "ar" ? "النماذج المتوسطة" : "Medium Models",
      description: language === "ar" ? "الوصول إلى نماذج ذكاء اصطناعي متوسطة الأداء" : "Access to medium-performance AI models",
      category: language === "ar" ? "النماذج" : "Models",
      isAvailableInFree: false,
    },
    {
      id: "advanced_models",
      name: language === "ar" ? "النماذج المتقدمة" : "Advanced Models",
      description: language === "ar" ? "الوصول إلى نماذج ذكاء اصطناعي متقدمة" : "Access to advanced AI models",
      category: language === "ar" ? "النماذج" : "Models",
      isAvailableInFree: false,
    },
    {
      id: "legal_models",
      name: language === "ar" ? "النماذج القانونية" : "Legal Models",
      description: language === "ar" ? "نماذج متخصصة في التحليل القانوني" : "Models specialized in legal analysis",
      category: language === "ar" ? "النماذج" : "Models",
      isAvailableInFree: false,
    },
    {
      id: "custom_models",
      name: language === "ar" ? "النماذج المخصصة" : "Custom Models",
      description: language === "ar" ? "إمكانية تخصيص وتدريب نماذج خاصة" : "Ability to customize and train special models",
      category: language === "ar" ? "النماذج" : "Models",
      isAvailableInFree: false,
    },
    {
      id: "voice_chat",
      name: language === "ar" ? "المحادثة الصوتية" : "Voice Chat",
      description: language === "ar" ? "إمكانية التحدث صوتياً مع الذكاء الاصطناعي" : "Ability to speak with AI using voice",
      category: language === "ar" ? "الوسائط" : "Media",
      isAvailableInFree: false,
    },
    {
      id: "file_upload",
      name: language === "ar" ? "رفع الملفات" : "File Upload",
      description: language === "ar" ? "إمكانية رفع وتحليل الملفات" : "Ability to upload and analyze files",
      category: language === "ar" ? "الوسائط" : "Media",
      isAvailableInFree: false,
    },
    {
      id: "rag",
      name: language === "ar" ? "نظام RAG" : "RAG System",
      description: language === "ar" ? "استرجاع المعرفة مع الاستشهادات" : "Knowledge retrieval with citations",
      category: language === "ar" ? "المعرفة" : "Knowledge",
      isAvailableInFree: false,
    },
    {
      id: "community_support",
      name: language === "ar" ? "دعم المجتمع" : "Community Support",
      description: language === "ar" ? "الوصول إلى منتديات الدعم المجتمعي" : "Access to community support forums",
      category: language === "ar" ? "الدعم" : "Support",
      isAvailableInFree: true,
    },
    {
      id: "email_support",
      name: language === "ar" ? "دعم البريد الإلكتروني" : "Email Support",
      description: language === "ar" ? "دعم عبر البريد الإلكتروني خلال ساعات العمل" : "Email support during business hours",
      category: language === "ar" ? "الدعم" : "Support",
      isAvailableInFree: false,
    },
    {
      id: "priority_support",
      name: language === "ar" ? "دعم أولوية" : "Priority Support",
      description: language === "ar" ? "دعم سريع مع أولوية عالية" : "Fast support with high priority",
      category: language === "ar" ? "الدعم" : "Support",
      isAvailableInFree: false,
    },
    {
      id: "24_7_support",
      name: language === "ar" ? "دعم 24/7" : "24/7 Support",
      description: language === "ar" ? "دعم على مدار الساعة طوال أيام الأسبوع" : "Support around the clock, all week",
      category: language === "ar" ? "الدعم" : "Support",
      isAvailableInFree: false,
    },
  ];

  // Subscription statistics
  const subscriptionStats = {
    totalSubscribers: subscriptionPackages.reduce((sum, pkg) => sum + pkg.subscribersCount, 0),
    monthlyRevenue: subscriptionPackages.reduce((sum, pkg) => sum + (pkg.monthlyPrice * pkg.subscribersCount), 0),
    yearlyRevenue: subscriptionPackages.reduce((sum, pkg) => {
      if (pkg.yearlyPrice) {
        return sum + ((pkg.yearlyPrice / 12) * pkg.subscribersCount);
      }
      return sum + (pkg.monthlyPrice * pkg.subscribersCount);
    }, 0),
    conversionRate: 42.5, // Conversion rate from free to paid
  };

  // Get package color
  const getPackageColor = (color: string) => {
    switch (color) {
      case "blue":
        return "blue";
      case "purple":
        return "purple";
      case "gold":
        return "gold";
      case "green":
        return "green";
      case "red":
        return "red";
      default:
        return "default";
    }
  };

  // Render price
  const renderPrice = (price: number, yearly: boolean = false) => {
    if (price === 0) return language === "ar" ? "مجاني" : "Free";
    if (price === -1) return language === "ar" ? "حسب الطلب" : "Custom";
    return `${formatNumber(price)} ${language === "ar" ? "ريال" : "SAR"}${yearly ? (language === "ar" ? "/سنة" : "/year") : (language === "ar" ? "/شهر" : "/month")}`;
  };

  // Render limit
  const renderLimit = (limit: number, unit: string) => {
    if (limit === -1) return language === "ar" ? "غير محدود" : "Unlimited";
    return `${formatNumber(limit)} ${unit}`;
  };

  // Handle creating a new package
  const handleCreatePackage = () => {
    form.resetFields();
    setIsCreateModalVisible(true);
  };

  // Handle editing a package
  const handleEditPackage = (pkg: SubscriptionPackage) => {
    setSelectedPackage(pkg);
    form.setFieldsValue({
      ...pkg,
      features: pkg.features,
      blockedFeatures: pkg.blockedFeatures,
    });
    setIsEditModalVisible(true);
  };

  // Handle deleting a package
  const handleDeletePackage = (pkg: SubscriptionPackage) => {
    message.success(language === "ar" ? `تم حذف باقة ${pkg.name} بنجاح` : `Package ${pkg.name} deleted successfully`);
  };

  // Handle form submission
  const handleSubmitForm = (values: any) => {
    console.log("Form values:", values);
    
    if (isCreateModalVisible) {
      message.success(language === "ar" ? `تم إنشاء باقة ${values.name} بنجاح` : `Package ${values.name} created successfully`);
      setIsCreateModalVisible(false);
    } else if (isEditModalVisible) {
      message.success(language === "ar" ? `تم تحديث باقة ${values.name} بنجاح` : `Package ${values.name} updated successfully`);
      setIsEditModalVisible(false);
    }
    
    setSelectedPackage(null);
    form.resetFields();
  };

  // Handle toggling package status
  const handleTogglePackageStatus = (pkg: SubscriptionPackage) => {
    message.success(language === "ar" ? `تم ${pkg.isActive ? "تعطيل" : "تفعيل"} باقة ${pkg.name}` : `Package ${pkg.name} ${pkg.isActive ? "disabled" : "enabled"} successfully`);
  };

  // Handle setting default package
  const handleSetDefaultPackage = (pkg: SubscriptionPackage) => {
    message.success(language === "ar" ? `تم تعيين باقة ${pkg.name} كباقة افتراضية` : `Package ${pkg.name} set as default`);
  };

  // Package table columns
  const packageColumns = [
    {
      title: language === "ar" ? "الباقة" : "Package",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: SubscriptionPackage) => (
        <div className="flex items-center">
          <Tag color={getPackageColor(record.color)} className="mr-2">
            {record.isDefault && <CrownOutlined className="mr-1" />}
            {name}
          </Tag>
          <div className="text-sm text-gray-500">
            {record.isDefault && <span className="text-green-600 font-medium">{language === "ar" ? "الباقة الافتراضية" : "Default Package"}</span>}
          </div>
        </div>
      ),
    },
    {
      title: language === "ar" ? "السعر الشهري" : "Monthly Price",
      dataIndex: "monthlyPrice",
      key: "monthlyPrice",
      render: (price: number) => renderPrice(price),
    },
    {
      title: language === "ar" ? "السعر السنوي" : "Yearly Price",
      dataIndex: "yearlyPrice",
      key: "yearlyPrice",
      render: (price: number | null) => price ? renderPrice(price, true) : (language === "ar" ? "غير متاح" : "Not available"),
    },
    {
      title: language === "ar" ? "المحادثات/الشهر" : "Chats/Month",
      dataIndex: "maxChatsPerMonth",
      key: "maxChatsPerMonth",
      render: (limit: number) => renderLimit(limit, language === "ar" ? "محادثة" : "chats"),
    },
    {
      title: language === "ar" ? "التوكينز/الشهر" : "Tokens/Month",
      dataIndex: "maxTokensPerMonth",
      key: "maxTokensPerMonth",
      render: (limit: number) => renderLimit(limit, language === "ar" ? "توكن" : "tokens"),
    },
    {
      title: language === "ar" ? "المشتركين" : "Subscribers",
      dataIndex: "subscribersCount",
      key: "subscribersCount",
      render: (count: number) => formatNumber(count),
    },
    {
      title: language === "ar" ? "الحالة" : "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "red"}>
          {isActive ? (language === "ar" ? "مفعلة" : "Active") : (language === "ar" ? "معطلة" : "Disabled")}
        </Tag>
      ),
    },
    {
      title: language === "ar" ? "الإجراءات" : "Actions",
      key: "actions",
      render: (_: any, record: SubscriptionPackage) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditPackage(record)}
            title={language === "ar" ? "تعديل الباقة" : "Edit Package"}
          />
          <Popconfirm
            title={language === "ar" ? "هل أنت متأكد من حذف هذه الباقة؟" : "Are you sure you want to delete this package?"}
            onConfirm={() => handleDeletePackage(record)}
            okText={language === "ar" ? "نعم" : "Yes"}
            cancelText={language === "ar" ? "لا" : "No"}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              title={language === "ar" ? "حذف الباقة" : "Delete Package"}
              disabled={record.isDefault}
            />
          </Popconfirm>
          <Button
            type="text"
            icon={record.isActive ? <CloseOutlined /> : <CheckOutlined />}
            onClick={() => handleTogglePackageStatus(record)}
            title={record.isActive ? (language === "ar" ? "تعطيل الباقة" : "Disable Package") : (language === "ar" ? "تفعيل الباقة" : "Enable Package")}
          />
          {!record.isDefault && (
            <Button
              type="text"
              icon={<CrownOutlined />}
              onClick={() => handleSetDefaultPackage(record)}
              title={language === "ar" ? "تعيين كباقة افتراضية" : "Set as Default Package"}
            />
          )}
        </Space>
      ),
    },
  ];

  // Feature table columns
  const featureColumns = [
    {
      title: language === "ar" ? "الميزة" : "Feature",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: FeatureToggle) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
        </div>
      ),
    },
    {
      title: language === "ar" ? "الفئة" : "Category",
      dataIndex: "category",
      key: "category",
      render: (category: string) => <Tag>{category}</Tag>,
    },
    {
      title: language === "ar" ? "متاح في المجاني" : "Available in Free",
      dataIndex: "isAvailableInFree",
      key: "isAvailableInFree",
      render: (isAvailable: boolean) => (
        isAvailable ? 
          <Tag color="green" icon={<CheckOutlined />}>{language === "ar" ? "متاح" : "Available"}</Tag> : 
          <Tag color="red" icon={<CloseOutlined />}>{language === "ar" ? "محجوب" : "Blocked"}</Tag>
      ),
    },
    {
      title: language === "ar" ? "الإجراءات" : "Actions",
      key: "actions",
      render: (_: any, record: FeatureToggle) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            title={language === "ar" ? "تعديل الميزة" : "Edit Feature"}
          />
          <Button
            type="text"
            icon={record.isAvailableInFree ? <CloseOutlined /> : <CheckOutlined />}
            title={record.isAvailableInFree ? (language === "ar" ? "حجب من المجاني" : "Block from Free") : (language === "ar" ? "إتاحة في المجاني" : "Allow in Free")}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{language === "ar" ? "باقات الاشتراك" : "Subscription Packages"}</h2>
        <Space>
          {hasChanges && (
            <>
              <Button
                icon={<UndoOutlined />}
                onClick={() => setHasChanges(false)}
              >
                {language === "ar" ? "إعادة تعيين" : "Reset"}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={() => {
                  message.success(language === "ar" ? "تم حفظ التغييرات بنجاح" : "Changes saved successfully");
                  setHasChanges(false);
                }}
              >
                {language === "ar" ? "حفظ التغييرات" : "Save Changes"}
              </Button>
            </>
          )}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreatePackage}
          >
            {language === "ar" ? "إضافة باقة جديدة" : "Add New Package"}
          </Button>
        </Space>
      </div>

      {/* Token usage alert example */}
      {showTokenAlert && (
        <TokenUsageAlert 
          currentUsage={9000}
          maxAllowed={10000}
          resetDate="2024-07-31"
          onClose={() => setShowTokenAlert(false)}
        />
      )}

      {/* Quick Stats */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "إجمالي المشتركين" : "Total Subscribers"}
              value={subscriptionStats.totalSubscribers}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "الإيرادات الشهرية" : "Monthly Revenue"}
              value={subscriptionStats.monthlyRevenue}
              suffix={language === "ar" ? "ريال" : "SAR"}
              prefix={<DollarOutlined />}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "الإيرادات السنوية" : "Annual Revenue"}
              value={subscriptionStats.yearlyRevenue * 12}
              suffix={language === "ar" ? "ريال" : "SAR"}
              prefix={<DollarOutlined />}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "معدل التحويل" : "Conversion Rate"}
              value={subscriptionStats.conversionRate}
              suffix="%"
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={language === "ar" ? "الباقات" : "Packages"} key="packages">
            <Table
              dataSource={subscriptionPackages}
              columns={packageColumns}
              rowKey="id"
              pagination={false}
            />
          </TabPane>

          <TabPane tab={language === "ar" ? "الميزات" : "Features"} key="features">
            <div className="mb-4 flex justify-end">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => message.info(language === "ar" ? "إضافة ميزة جديدة" : "Add new feature")}
              >
                {language === "ar" ? "إضافة ميزة جديدة" : "Add New Feature"}
              </Button>
            </div>
            <Table
              dataSource={features}
              columns={featureColumns}
              rowKey="id"
              pagination={false}
            />
          </TabPane>

          <TabPane tab={language === "ar" ? "آلية الحجب الذكي" : "Smart Blocking Mechanism"} key="blocking">
            <div className="mt-4">
              <Title level={4}>{language === "ar" ? "قواعد الحجب الديناميكية" : "Dynamic Blocking Rules"}</Title>
              <Paragraph className="text-gray-500 mb-6">
                {language === "ar" 
                  ? "تكوين آلية الحجب الذكي للميزات حسب الباقة" 
                  : "Configure smart feature blocking mechanism by package"}
              </Paragraph>

              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="mb-4">
                  <Text strong>{language === "ar" ? "مخطط تدفق الحجب:" : "Blocking Flow Chart:"}</Text>
                </div>
                <div className="text-center">
                  <pre className="text-sm text-left">
{language === "ar" ? 
`طلب ميزة
  ↓
هل الباقة تدعمها؟
  ↓
  ├── نعم → التحقق من الحدود
  │       ↓
  │       ├── مستنفذ → حجب الميزة + إشعار
  │       └── متاح → السماح بالتنفيذ
  │
  └── لا → حجب الميزة + عرض ترقية` : 
`Feature Request
  ↓
Does Package Support It?
  ↓
  ├── Yes → Check Limits
  │       ↓
  │       ├── Exhausted → Block + Notify
  │       └── Available → Allow Execution
  │
  └── No → Block + Show Upgrade`}
                  </pre>
                </div>
              </div>

              <Title level={5}>{language === "ar" ? "تنفيذ الحجب في الواجهة" : "UI Implementation of Blocking"}</Title>
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <pre className="text-sm">
{`// src/components/FeatureGuard.jsx
const FeatureGuard = ({ feature, children }) => {
  const { subscription } = useUser();
  
  if (!checkFeatureAccess(subscription, feature)) {
    return (
      <div className="border border-dashed border-gray-300 rounded-lg p-6 text-center">
        <LockOutlined className="text-3xl text-gray-400 mb-2" />
        <h3 className="text-lg font-medium">${language === "ar" ? "هذه الميزة غير متاحة في باقتك الحالية" : "This feature is not available in your current plan"}</h3>
        <p className="text-gray-500 mb-4">${language === "ar" ? "قم بالترقية إلى باقة أعلى للوصول إلى هذه الميزة" : "Upgrade to a higher plan to access this feature"}</p>
        <Button type="primary">${language === "ar" ? "ترقية الباقة" : "Upgrade Plan"}</Button>
      </div>
    );
  }
  return children;
};

// ${language === "ar" ? "مثال الاستخدام:" : "Usage example:"}
<FeatureGuard feature="voice_chat">
  <VoiceChatComponent />
</FeatureGuard>`}
                </pre>
              </div>

              <Title level={5}>{language === "ar" ? "نظام التفعيل الديناميكي" : "Dynamic Activation System"}</Title>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-sm">
{`// src/utils/featureToggle.js
export const checkFeatureAccess = (userSubscription, feature) => {
  const PLAN_FEATURES = {
    free: ["basic_models", "community_support"],
    basic: ["file_upload_10mb", "medium_models", ...PLAN_FEATURES.free],
    pro: ["voice", "advanced_models", "rag", ...PLAN_FEATURES.basic],
    enterprise: ["custom_models", "priority_support", ...PLAN_FEATURES.pro]
  };
  return PLAN_FEATURES[userSubscription.plan]?.includes(feature);
};`}
                </pre>
              </div>
            </div>
          </TabPane>

          <TabPane tab={language === "ar" ? "التجديد والإلغاء" : "Renewal & Cancellation"} key="renewal">
            <div className="mt-4">
              <Title level={4}>{language === "ar" ? "نظام إشعارات التجديد" : "Renewal Notification System"}</Title>
              <Paragraph className="text-gray-500 mb-6">
                {language === "ar" 
                  ? "إدارة عمليات التجديد والإلغاء للاشتراكات" 
                  : "Manage subscription renewal and cancellation processes"}
              </Paragraph>

              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <pre className="text-sm">
{`-- ${language === "ar" ? "إجراء إشعار التجديد" : "Renewal Notification Procedure"}
CREATE FUNCTION notify_renewal()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.expires_at < NOW() + INTERVAL '7 days' AND NEW.expires_at > NOW() THEN
    INSERT INTO notifications(user_id, message)
    VALUES (NEW.user_id, '${language === "ar" ? "اشتراكك ينتهي خلال 7 أيام" : "Your subscription expires in 7 days"}');
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ${language === "ar" ? "تشغيل الإجراء عند تحديث الاشتراك" : "Run procedure when subscription is updated"}
CREATE TRIGGER subscription_renewal_check
AFTER INSERT OR UPDATE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION notify_renewal();`}
                </pre>
              </div>

              <Title level={5}>{language === "ar" ? "إدارة الإلغاء" : "Cancellation Management"}</Title>
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <pre className="text-sm">
{`// src/components/CancelSubscription.jsx
const CancelSubscriptionForm = () => {
  const { data: subscription } = useOne({ resource: "subscriptions" });
  
  return (
    <div className="border rounded-lg p-6">
      <h3 className="text-xl font-semibold mb-4">${language === "ar" ? "إلغاء الاشتراك" : "Cancel Subscription"}</h3>
      <p className="text-gray-500 mb-4">
        ${language === "ar" ? "سيؤدي إلغاء اشتراكك إلى إيقاف التجديد التلقائي. ستظل قادرًا على استخدام الخدمة حتى نهاية فترة الفوترة الحالية." : "Cancelling your subscription will stop automatic renewal. You'll still be able to use the service until the end of your current billing period."}
      </p>
      <Form layout="vertical">
        <Form.Item
          name="reason"
          label="${language === "ar" ? "سبب الإلغاء" : "Reason for cancellation"}"
          rules={[{ required: true, message: "${language === "ar" ? "الرجاء اختيار سبب الإلغاء" : "Please select a reason for cancellation"}" }]}
        >
          <Select
            placeholder="${language === "ar" ? "اختر سبب الإلغاء" : "Select reason for cancellation"}"
            options={[
              { value: "price", label: "${language === "ar" ? "السعر مرتفع" : "Price is too high"}" },
              { value: "features", label: "${language === "ar" ? "لا أستخدم جميع الميزات" : "Not using all features"}" },
              { value: "competitor", label: "${language === "ar" ? "وجدت خدمة أفضل" : "Found a better service"}" },
              { value: "other", label: "${language === "ar" ? "سبب آخر" : "Other reason"}" }
            ]}
          />
        </Form.Item>
        <Form.Item name="feedback" label="${language === "ar" ? "ملاحظات إضافية" : "Additional feedback"}">
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item>
          <Button 
            danger 
            onClick={() => cancelSubscription(subscription.id)}
            disabled={subscription.plan === 'enterprise'}
          >
            ${language === "ar" ? "إلغاء الاشتراك" : "Cancel Subscription"}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};`}
                </pre>
              </div>
            </div>
          </TabPane>

          <TabPane tab={language === "ar" ? "التكامل مع أنظمة الدفع" : "Payment System Integration"} key="payment">
            <div className="mt-4">
              <Title level={4}>{language === "ar" ? "تكامل بوابات الدفع" : "Payment Gateway Integration"}</Title>
              <Paragraph className="text-gray-500 mb-6">
                {language === "ar" 
                  ? "تكوين التكامل مع أنظمة الدفع المختلفة" 
                  : "Configure integration with different payment systems"}
              </Paragraph>

              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="mb-4">
                  <Text strong>{language === "ar" ? "مخطط تدفق الدفع:" : "Payment Flow Chart:"}</Text>
                </div>
                <div className="text-center">
                  <pre className="text-sm text-left">
{language === "ar" ? 
`المستخدم → نقرت "ترقية الباقة"
  ↓
الواجهة → طلب فاتورة (199 ريال)
  ↓
بوابة الدفع → صفحة الدفع
  ↓
المستخدم → إتمام الدفع
  ↓
بوابة الدفع → webhook تأكيد الدفع
  ↓
الخادم → تفعيل الباقة الجديدة
  ↓
الخادم → إرسال إشعار الترقية` : 
`User → Clicked "Upgrade Plan"
  ↓
Frontend → Request Invoice (199 SAR)
  ↓
Payment Gateway → Payment Page
  ↓
User → Complete Payment
  ↓
Payment Gateway → Webhook Payment Confirmation
  ↓
Server → Activate New Plan
  ↓
Server → Send Upgrade Notification`}
                  </pre>
                </div>
              </div>

              <Row gutter={16}>
                <Col span={12}>
                  <Card title={language === "ar" ? "بوابات الدفع المدعومة" : "Supported Payment Gateways"} className="mb-6">
                    <List
                      itemLayout="horizontal"
                      dataSource={[
                        { name: language === "ar" ? "مدى" : "Mada", status: language === "ar" ? "مفعل" : "Enabled", icon: "💳" },
                        { name: language === "ar" ? "فيزا/ماستركارد" : "Visa/Mastercard", status: language === "ar" ? "مفعل" : "Enabled", icon: "💳" },
                        { name: language === "ar" ? "آبل باي" : "Apple Pay", status: language === "ar" ? "غير مفعل" : "Disabled", icon: "🍎" },
                        { name: "STC Pay", status: language === "ar" ? "مفعل" : "Enabled", icon: "📱" },
                      ]}
                      renderItem={(item) => (
                        <List.Item
                          actions={[
                            <Switch checked={item.status === (language === "ar" ? "مفعل" : "Enabled")} />,
                            <Button type="link">{language === "ar" ? "تكوين" : "Configure"}</Button>
                          ]}
                        >
                          <List.Item.Meta
                            avatar={<Avatar>{item.icon}</Avatar>}
                            title={item.name}
                            description={`${language === "ar" ? "الحالة" : "Status"}: ${item.status}`}
                          />
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title={language === "ar" ? "إعدادات الفوترة" : "Billing Settings"} className="mb-6">
                    <Form layout="vertical">
                      <Form.Item
                        name="invoicePrefix"
                        label={language === "ar" ? "بادئة الفاتورة" : "Invoice Prefix"}
                        initialValue="INV-"
                      >
                        <Input />
                      </Form.Item>
                      <Form.Item
                        name="taxRate"
                        label={language === "ar" ? "نسبة الضريبة (%)" : "Tax Rate (%)"}
                        initialValue={15}
                      >
                        <InputNumber min={0} max={100} style={{ width: "100%" }} />
                      </Form.Item>
                      <Form.Item
                        name="gracePeriod"
                        label={language === "ar" ? "فترة السماح (أيام)" : "Grace Period (days)"}
                        initialValue={3}
                      >
                        <InputNumber min={0} max={30} style={{ width: "100%" }} />
                      </Form.Item>
                      <Form.Item
                        name="autoRenew"
                        label={language === "ar" ? "التجديد التلقائي" : "Auto Renewal"}
                        valuePropName="checked"
                        initialValue={true}
                      >
                        <Switch />
                      </Form.Item>
                    </Form>
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* Create Package Modal */}
      <Modal
        title={language === "ar" ? "إضافة باقة جديدة" : "Add New Package"}
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitForm}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={language === "ar" ? "اسم الباقة" : "Package Name"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال اسم الباقة" : "Please enter package name" }]}
              >
                <Input placeholder={language === "ar" ? "مثال: الباقة الذهبية" : "Example: Gold Package"} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="color"
                label={language === "ar" ? "لون الباقة" : "Package Color"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار لون الباقة" : "Please select package color" }]}
                initialValue="blue"
              >
                <Select
                  options={[
                    { value: "blue", label: language === "ar" ? "أزرق" : "Blue" },
                    { value: "purple", label: language === "ar" ? "بنفسجي" : "Purple" },
                    { value: "gold", label: language === "ar" ? "ذهبي" : "Gold" },
                    { value: "green", label: language === "ar" ? "أخضر" : "Green" },
                    { value: "red", label: language === "ar" ? "أحمر" : "Red" },
                    { value: "default", label: language === "ar" ? "رمادي" : "Gray" },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="monthlyPrice"
                label={language === "ar" ? "السعر الشهري (ريال)" : "Monthly Price (SAR)"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال السعر الشهري" : "Please enter monthly price" }]}
              >
                <InputNumber min={0} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="yearlyPrice"
                label={language === "ar" ? "السعر السنوي (ريال)" : "Yearly Price (SAR)"}
              >
                <InputNumber min={0} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="maxChatsPerMonth"
                label={language === "ar" ? "الحد الأقصى للمحادثات/الشهر" : "Max Chats/Month"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الحد الأقصى للمحادثات" : "Please enter max chats" }]}
              >
                <InputNumber min={-1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxTokensPerMonth"
                label={language === "ar" ? "الحد الأقصى للتوكينز/الشهر" : "Max Tokens/Month"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الحد الأقصى للتوكينز" : "Please enter max tokens" }]}
              >
                <InputNumber min={-1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="features"
            label={language === "ar" ? "الميزات المتاحة" : "Available Features"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار الميزات المتاحة" : "Please select available features" }]}
          >
            <Select
              mode="multiple"
              placeholder={language === "ar" ? "اختر الميزات المتاحة" : "Select available features"}
              style={{ width: "100%" }}
              options={features.map(feature => ({
                value: feature.id,
                label: feature.name,
              }))}
            />
          </Form.Item>

          <Form.Item
            name="blockedFeatures"
            label={language === "ar" ? "الميزات المحجوبة" : "Blocked Features"}
          >
            <Select
              mode="multiple"
              placeholder={language === "ar" ? "اختر الميزات المحجوبة" : "Select blocked features"}
              style={{ width: "100%" }}
              options={features.map(feature => ({
                value: feature.id,
                label: feature.name,
              }))}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="isActive"
                label={language === "ar" ? "مفعلة" : "Active"}
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isDefault"
                label={language === "ar" ? "افتراضية" : "Default"}
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sortOrder"
                label={language === "ar" ? "ترتيب العرض" : "Display Order"}
                initialValue={1}
              >
                <InputNumber min={1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {isCreateModalVisible ? (language === "ar" ? "إنشاء" : "Create") : (language === "ar" ? "تحديث" : "Update")}
              </Button>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setIsEditModalVisible(false);
              }}>
                {language === "ar" ? "إلغاء" : "Cancel"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Package Modal */}
      <Modal
        title={`${language === "ar" ? "تعديل باقة" : "Edit Package"} ${selectedPackage?.name}`}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitForm}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={language === "ar" ? "اسم الباقة" : "Package Name"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال اسم الباقة" : "Please enter package name" }]}
              >
                <Input placeholder={language === "ar" ? "مثال: الباقة الذهبية" : "Example: Gold Package"} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="color"
                label={language === "ar" ? "لون الباقة" : "Package Color"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار لون الباقة" : "Please select package color" }]}
              >
                <Select
                  options={[
                    { value: "blue", label: language === "ar" ? "أزرق" : "Blue" },
                    { value: "purple", label: language === "ar" ? "بنفسجي" : "Purple" },
                    { value: "gold", label: language === "ar" ? "ذهبي" : "Gold" },
                    { value: "green", label: language === "ar" ? "أخضر" : "Green" },
                    { value: "red", label: language === "ar" ? "أحمر" : "Red" },
                    { value: "default", label: language === "ar" ? "رمادي" : "Gray" },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="monthlyPrice"
                label={language === "ar" ? "السعر الشهري (ريال)" : "Monthly Price (SAR)"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال السعر الشهري" : "Please enter monthly price" }]}
              >
                <InputNumber min={0} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="yearlyPrice"
                label={language === "ar" ? "السعر السنوي (ريال)" : "Yearly Price (SAR)"}
              >
                <InputNumber min={0} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="maxChatsPerMonth"
                label={language === "ar" ? "الحد الأقصى للمحادثات/الشهر" : "Max Chats/Month"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الحد الأقصى للمحادثات" : "Please enter max chats" }]}
              >
                <InputNumber min={-1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxTokensPerMonth"
                label={language === "ar" ? "الحد الأقصى للتوكينز/الشهر" : "Max Tokens/Month"}
                rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الحد الأقصى للتوكينز" : "Please enter max tokens" }]}
              >
                <InputNumber min={-1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="features"
            label={language === "ar" ? "الميزات المتاحة" : "Available Features"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار الميزات المتاحة" : "Please select available features" }]}
          >
            <Select
              mode="multiple"
              placeholder={language === "ar" ? "اختر الميزات المتاحة" : "Select available features"}
              style={{ width: "100%" }}
              options={features.map(feature => ({
                value: feature.id,
                label: feature.name,
              }))}
            />
          </Form.Item>

          <Form.Item
            name="blockedFeatures"
            label={language === "ar" ? "الميزات المحجوبة" : "Blocked Features"}
          >
            <Select
              mode="multiple"
              placeholder={language === "ar" ? "اختر الميزات المحجوبة" : "Select blocked features"}
              style={{ width: "100%" }}
              options={features.map(feature => ({
                value: feature.id,
                label: feature.name,
              }))}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="isActive"
                label={language === "ar" ? "مفعلة" : "Active"}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isDefault"
                label={language === "ar" ? "افتراضية" : "Default"}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sortOrder"
                label={language === "ar" ? "ترتيب العرض" : "Display Order"}
              >
                <InputNumber min={1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {language === "ar" ? "تحديث" : "Update"}
              </Button>
              <Button onClick={() => setIsEditModalVisible(false)}>
                {language === "ar" ? "إلغاء" : "Cancel"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
import { useState } from "react";
import {
  Table,
  Card,
  Space,
  Button,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Radio,
  Typography,
  Drawer,
  List,
  Avatar,
  Divider,
} from "antd";
import {
  FlagOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  RobotOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime, formatNumber } from "../lib/utils";

const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Text } = Typography;

export const ContentModerationPage = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedReason, setSelectedReason] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false);
  const [isViewDrawerVisible, setIsViewDrawerVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [reviewForm] = Form.useForm();

  // بيانات تجريبية لتقارير المحتوى
  const reports = [
    {
      id: "1",
      conversationId: "conv_1",
      messageId: "msg_1",
      reporterId: "user_1",
      reporterName: "أحمد محمد",
      reason: "محتوى غير دقيق",
      description: "الإجابة تحتوي على معلومات قانونية غير صحيحة",
      status: "pending",
      priority: "high",
      createdAt: new Date("2024-02-20T10:30:00"),
      reviewedBy: null,
      reviewedAt: null,
      conversation: {
        title: "استشارة قانونية حول عقد إيجار",
        userName: "أحمد محمد",
        messages: [
          {
            id: "msg_1",
            sender: "user",
            text: "هل يحق للمالك زيادة قيمة الإيجار خلال فترة العقد؟",
            timestamp: new Date("2024-02-20T10:00:00"),
          },
          {
            id: "msg_2",
            sender: "bot",
            text: "نعم، يحق للمالك زيادة الإيجار في أي وقت حسب رغبته.",
            timestamp: new Date("2024-02-20T10:00:30"),
            flagged: true,
          },
        ],
      },
    },
    {
      id: "2",
      conversationId: "conv_2",
      messageId: "msg_3",
      reporterId: "user_2",
      reporterName: "سارة علي",
      reason: "استجابة غير مناسبة",
      description: "الرد لا يجيب على السؤال المطروح",
      status: "pending",
      priority: "medium",
      createdAt: new Date("2024-02-19T15:45:00"),
      reviewedBy: null,
      reviewedAt: null,
      conversation: {
        title: "استفسار عن إجراءات التقاضي",
        userName: "سارة علي",
        messages: [],
      },
    },
    {
      id: "3",
      conversationId: "conv_3",
      messageId: "msg_4",
      reporterId: "user_3",
      reporterName: "محمد خالد",
      reason: "مشكلة تقنية",
      description: "الرد مقطوع ولم يكتمل",
      status: "approved",
      priority: "low",
      createdAt: new Date("2024-02-18T09:15:00"),
      reviewedBy: "admin_1",
      reviewedAt: new Date("2024-02-18T14:30:00"),
      conversation: {
        title: "مشكلة في عقد عمل",
        userName: "محمد خالد",
        messages: [],
      },
    },
    {
      id: "4",
      conversationId: "conv_4",
      messageId: "msg_5",
      reporterId: "user_4",
      reporterName: "فاطمة أحمد",
      reason: "محتوى غير دقيق",
      description: "معلومات قانونية خاطئة حول حقوق الملكية الفكرية",
      status: "rejected",
      priority: "high",
      createdAt: new Date("2024-02-17T11:20:00"),
      reviewedBy: "admin_2",
      reviewedAt: new Date("2024-02-17T16:45:00"),
      conversation: {
        title: "استشارة حول حقوق الملكية الفكرية",
        userName: "فاطمة أحمد",
        messages: [],
      },
    },
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case "pending":
        return <Tag color="orange">قيد المراجعة</Tag>;
      case "approved":
        return <Tag color="green">تمت الموافقة</Tag>;
      case "rejected":
        return <Tag color="red">مرفوض</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getPriorityTag = (priority: string) => {
    switch (priority) {
      case "high":
        return <Tag color="red">عالية</Tag>;
      case "medium":
        return <Tag color="orange">متوسطة</Tag>;
      case "low":
        return <Tag color="green">منخفضة</Tag>;
      default:
        return <Tag>{priority}</Tag>;
    }
  };

  const handleReview = (report: any) => {
    setSelectedReport(report);
    setIsReviewModalVisible(true);
    reviewForm.resetFields();
  };

  const handleView = (report: any) => {
    setSelectedReport(report);
    setIsViewDrawerVisible(true);
  };

  const handleSubmitReview = (values: any) => {
    console.log("Review submitted:", {
      reportId: selectedReport.id,
      decision: values.decision,
      notes: values.notes,
    });
    setIsReviewModalVisible(false);
    setSelectedReport(null);
  };

  const columns = [
    {
      title: "المُبلِغ",
      dataIndex: "reporterName",
      key: "reporterName",
    },
    {
      title: "السبب",
      dataIndex: "reason",
      key: "reason",
    },
    {
      title: "الأولوية",
      dataIndex: "priority",
      key: "priority",
      render: (priority: string) => getPriorityTag(priority),
    },
    {
      title: "الحالة",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: "تاريخ الإبلاغ",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: "الإجراءات",
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            title="عرض التفاصيل"
          />
          {record.status === "pending" && (
            <Button
              type="primary"
              icon={<FlagOutlined />}
              onClick={() => handleReview(record)}
              size="small"
            >
              مراجعة
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // تصفية التقارير بناءً على معايير البحث
  const filteredReports = reports.filter((report) => {
    let matchesSearch = true;
    let matchesStatus = true;
    let matchesReason = true;
    let matchesDateRange = true;

    if (searchText) {
      matchesSearch =
        report.reporterName.toLowerCase().includes(searchText.toLowerCase()) ||
        report.description.toLowerCase().includes(searchText.toLowerCase()) ||
        report.conversation.title.toLowerCase().includes(searchText.toLowerCase());
    }

    if (selectedStatus) {
      matchesStatus = report.status === selectedStatus;
    }

    if (selectedReason) {
      matchesReason = report.reason === selectedReason;
    }

    if (dateRange) {
      const [startDate, endDate] = dateRange;
      matchesDateRange =
        report.createdAt >= startDate && report.createdAt <= endDate;
    }

    return matchesSearch && matchesStatus && matchesReason && matchesDateRange;
  });

  const pendingCount = reports.filter(r => r.status === "pending").length;
  const approvedCount = reports.filter(r => r.status === "approved").length;
  const rejectedCount = reports.filter(r => r.status === "rejected").length;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">مراقبة المحتوى</h2>
        <div className="flex items-center gap-4">
          <Tag color="orange">{pendingCount} قيد المراجعة</Tag>
          <Tag color="green">{approvedCount} موافق عليها</Tag>
          <Tag color="red">{rejectedCount} مرفوضة</Tag>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{pendingCount}</div>
            <div className="text-sm text-gray-500">قيد المراجعة</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{approvedCount}</div>
            <div className="text-sm text-gray-500">تمت الموافقة</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{rejectedCount}</div>
            <div className="text-sm text-gray-500">مرفوضة</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{reports.length}</div>
            <div className="text-sm text-gray-500">إجمالي التقارير</div>
          </div>
        </Card>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <Input
            placeholder="البحث في التقارير..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder="الحالة"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedStatus(value)}
            options={[
              { value: "pending", label: "قيد المراجعة" },
              { value: "approved", label: "تمت الموافقة" },
              { value: "rejected", label: "مرفوض" },
            ]}
          />
          <Select
            placeholder="السبب"
            allowClear
            style={{ width: 180 }}
            onChange={(value) => setSelectedReason(value)}
            options={[
              { value: "محتوى غير دقيق", label: "محتوى غير دقيق" },
              { value: "استجابة غير مناسبة", label: "استجابة غير مناسبة" },
              { value: "مشكلة تقنية", label: "مشكلة تقنية" },
              { value: "أخرى", label: "أخرى" },
            ]}
          />
          <RangePicker
            placeholder={["تاريخ البداية", "تاريخ النهاية"]}
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]?.toDate() as Date, dates[1]?.toDate() as Date]);
              } else {
                setDateRange(null);
              }
            }}
          />
          <Button icon={<FilterOutlined />} onClick={() => {
            setSearchText("");
            setSelectedStatus(null);
            setSelectedReason(null);
            setDateRange(null);
          }}>
            إعادة تعيين
          </Button>
        </div>
      </Card>

      {/* جدول التقارير */}
      <Table
        dataSource={filteredReports}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        rowClassName={(record) => 
          record.status === "pending" && record.priority === "high" 
            ? "bg-red-50" 
            : ""
        }
      />

      {/* نافذة المراجعة */}
      <Modal
        title="مراجعة التقرير"
        open={isReviewModalVisible}
        onCancel={() => setIsReviewModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedReport && (
          <div>
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">تفاصيل التقرير</h4>
              <p><strong>المُبلِغ:</strong> {selectedReport.reporterName}</p>
              <p><strong>السبب:</strong> {selectedReport.reason}</p>
              <p><strong>الوصف:</strong> {selectedReport.description}</p>
              <p><strong>الأولوية:</strong> {getPriorityTag(selectedReport.priority)}</p>
            </div>

            <Form
              form={reviewForm}
              layout="vertical"
              onFinish={handleSubmitReview}
            >
              <Form.Item
                name="decision"
                label="القرار"
                rules={[{ required: true, message: "الرجاء اختيار قرار" }]}
              >
                <Radio.Group>
                  <Radio value="approved">الموافقة على التقرير</Radio>
                  <Radio value="rejected">رفض التقرير</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name="notes"
                label="ملاحظات المراجع"
                rules={[{ required: true, message: "الرجاء إدخال ملاحظات" }]}
              >
                <TextArea rows={4} placeholder="أدخل ملاحظاتك حول القرار..." />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    تأكيد القرار
                  </Button>
                  <Button onClick={() => setIsReviewModalVisible(false)}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* درج عرض التفاصيل */}
      <Drawer
        title="تفاصيل التقرير"
        placement="right"
        width={700}
        onClose={() => setIsViewDrawerVisible(false)}
        open={isViewDrawerVisible}
      >
        {selectedReport && (
          <div>
            <Card title="معلومات التقرير" className="mb-4">
              <div className="space-y-2">
                <p><strong>المُبلِغ:</strong> {selectedReport.reporterName}</p>
                <p><strong>السبب:</strong> {selectedReport.reason}</p>
                <p><strong>الوصف:</strong> {selectedReport.description}</p>
                <p><strong>الأولوية:</strong> {getPriorityTag(selectedReport.priority)}</p>
                <p><strong>الحالة:</strong> {getStatusTag(selectedReport.status)}</p>
                <p><strong>تاريخ الإبلاغ:</strong> {formatDateTime(selectedReport.createdAt)}</p>
                {selectedReport.reviewedBy && (
                  <>
                    <p><strong>تمت المراجعة بواسطة:</strong> {selectedReport.reviewedBy}</p>
                    <p><strong>تاريخ المراجعة:</strong> {formatDateTime(selectedReport.reviewedAt)}</p>
                  </>
                )}
              </div>
            </Card>

            <Card title="المحادثة المُبلَّغ عنها" className="mb-4">
              <h4 className="font-medium mb-3">{selectedReport.conversation.title}</h4>
              <List
                itemLayout="horizontal"
                dataSource={selectedReport.conversation.messages}
                renderItem={(message: any) => (
                  <List.Item className={message.flagged ? "bg-red-50 p-3 rounded" : ""}>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={
                            message.sender === "user" ? (
                              <UserOutlined />
                            ) : (
                              <RobotOutlined />
                            )
                          }
                          style={{
                            backgroundColor:
                              message.sender === "user" ? "#1890ff" : "#52c41a",
                          }}
                        />
                      }
                      title={
                        <div className="flex justify-between items-center">
                          <span>
                            {message.sender === "user"
                              ? selectedReport.conversation.userName
                              : "شُريح"}
                          </span>
                          <div className="flex items-center gap-2">
                            <Text type="secondary">
                              {formatDateTime(message.timestamp)}
                            </Text>
                            {message.flagged && (
                              <Tag color="red" icon={<ExclamationCircleOutlined />}>
                                مُبلَّغ عنه
                              </Tag>
                            )}
                          </div>
                        </div>
                      }
                      description={<p className="mt-2">{message.text}</p>}
                    />
                  </List.Item>
                )}
              />
            </Card>
          </div>
        )}
      </Drawer>
    </div>
  );
};
import { useEffect, useState } from "react";
import { Card, Spin, Alert } from "antd";
import { useNavigate } from "react-router-dom";
import { supabase } from "../lib/supabase";
import { useTheme } from "../hooks/use-theme";

export const AuthCallback = () => {
  const navigate = useNavigate();
  const { actualTheme } = useTheme();
  const [error, setError] = useState<string | null>(null);

  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the auth code from the URL
        const { searchParams } = new URL(window.location.href);
        const code = searchParams.get('code');
        
        if (!code) {
          throw new Error("لم يتم العثور على رمز المصادقة");
        }
        
        // Exchange the code for a session
        const { error: sessionError } = await supabase.auth.exchangeCodeForSession(code);
        
        if (sessionError) {
          throw sessionError;
        }
        
        // Redirect to the dashboard
        navigate('/');
      } catch (err) {
        console.error("Auth callback error:", err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("حدث خطأ أثناء المصادقة");
        }
        
        // Redirect to login after a delay
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md text-center">
        <img
          src={logoSrc}
          alt="شُريح"
          className="logo-component mx-auto mb-4"
          style={{ height: "80px" }}
        />
        
        {error ? (
          <Alert
            message="فشل المصادقة"
            description={error}
            type="error"
            showIcon
          />
        ) : (
          <>
            <Spin size="large" />
            <p className="mt-4 text-lg">جاري المصادقة...</p>
          </>
        )}
      </Card>
    </div>
  );
};
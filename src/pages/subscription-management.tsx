import { useState } from "react";
import {
  Table,
  Card,
  Space,
  Button,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Descriptions,
  Statistic,
  Row,
  Col,
  Progress,
  Typography,
  Tabs,
  List,
  Avatar,
  Popconfirm,
  message,
} from "antd";
import {
  UserOutlined,
  CrownOutlined,
  DollarOutlined,
  CalendarOutlined,
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  EditOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime, formatNumber } from "../lib/utils";
import { TokenUsageAlert } from "../components/subscription/TokenUsageAlert";

const { RangePicker } = DatePicker;
const { Text } = Typography;
const { TabPane } = Tabs;

export const SubscriptionManagementPage = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [selectedTier, setSelectedTier] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false);
  const [isUpgradeModalVisible, setIsUpgradeModalVisible] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("active");
  const [showTokenAlert, setShowTokenAlert] = useState(true);

  // Sample data for subscriptions
  const subscriptions = [
    {
      id: "sub_1",
      userId: "user_1",
      userName: "أحمد محمد",
      email: "<EMAIL>",
      tier: "premium",
      status: "active",
      startDate: new Date("2024-01-15"),
      endDate: new Date("2024-07-15"),
      monthlyPrice: 99,
      tokensUsed: 45000,
      tokensLimit: 100000,
      features: ["محادثة صوتية", "رفع ملفات", "دعم أولوية"],
      lastPayment: new Date("2024-01-15"),
      nextBilling: new Date("2024-02-15"),
      autoRenew: true,
    },
    {
      id: "sub_2",
      userId: "user_2",
      userName: "سارة علي",
      email: "<EMAIL>",
      tier: "basic",
      status: "active",
      startDate: new Date("2024-02-01"),
      endDate: new Date("2024-03-01"),
      monthlyPrice: 49,
      tokensUsed: 8500,
      tokensLimit: 25000,
      features: ["محادثة صوتية", "دعم بريد إلكتروني"],
      lastPayment: new Date("2024-02-01"),
      nextBilling: new Date("2024-03-01"),
      autoRenew: true,
    },
    {
      id: "sub_3",
      userId: "user_3",
      userName: "محمد خالد",
      email: "<EMAIL>",
      tier: "free",
      status: "active",
      startDate: new Date("2024-01-20"),
      endDate: null,
      monthlyPrice: 0,
      tokensUsed: 4800,
      tokensLimit: 5000,
      features: ["محادثة أساسية"],
      lastPayment: null,
      nextBilling: null,
      autoRenew: false,
    },
    {
      id: "sub_4",
      userId: "user_4",
      userName: "فاطمة أحمد",
      email: "<EMAIL>",
      tier: "enterprise",
      status: "active",
      startDate: new Date("2023-12-01"),
      endDate: new Date("2024-12-01"),
      monthlyPrice: 299,
      tokensUsed: 180000,
      tokensLimit: 500000,
      features: ["جميع الميزات", "دعم مخصص", "API مخصص"],
      lastPayment: new Date("2024-02-01"),
      nextBilling: new Date("2024-03-01"),
      autoRenew: true,
    },
    {
      id: "sub_5",
      userId: "user_5",
      userName: "عبدالله محمد",
      email: "<EMAIL>",
      tier: "basic",
      status: "expired",
      startDate: new Date("2023-12-15"),
      endDate: new Date("2024-01-15"),
      monthlyPrice: 49,
      tokensUsed: 25000,
      tokensLimit: 25000,
      features: ["محادثة صوتية", "دعم بريد إلكتروني"],
      lastPayment: new Date("2023-12-15"),
      nextBilling: null,
      autoRenew: false,
    },
    {
      id: "sub_6",
      userId: "user_6",
      userName: "خالد العتيبي",
      email: "<EMAIL>",
      tier: "premium",
      status: "active",
      startDate: new Date("2024-01-10"),
      endDate: new Date("2024-07-10"),
      monthlyPrice: 99,
      tokensUsed: 92000,
      tokensLimit: 100000,
      features: ["محادثة صوتية", "رفع ملفات", "دعم أولوية"],
      lastPayment: new Date("2024-01-10"),
      nextBilling: new Date("2024-02-10"),
      autoRenew: true,
    },
  ];

  const getTierTag = (tier: string) => {
    switch (tier) {
      case "free":
        return <Tag color="default">مجاني</Tag>;
      case "basic":
        return <Tag color="blue">أساسي</Tag>;
      case "premium":
        return <Tag color="purple">متقدم</Tag>;
      case "enterprise":
        return <Tag color="gold">مؤسسي</Tag>;
      default:
        return <Tag>{tier}</Tag>;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case "active":
        return <Tag color="green">نشط</Tag>;
      case "expired":
        return <Tag color="red">منتهي</Tag>;
      case "cancelled":
        return <Tag color="gray">ملغي</Tag>;
      case "pending":
        return <Tag color="orange">قيد الانتظار</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const handleViewDetails = (subscription: any) => {
    setSelectedSubscription(subscription);
    setIsDetailsModalVisible(true);
  };

  const handleUpgrade = (subscription: any) => {
    setSelectedSubscription(subscription);
    setIsUpgradeModalVisible(true);
  };

  const columns = [
    {
      title: "المستخدم",
      dataIndex: "userName",
      key: "userName",
      render: (name: string, record: any) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: "المستوى",
      dataIndex: "tier",
      key: "tier",
      render: (tier: string) => getTierTag(tier),
    },
    {
      title: "الحالة",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: "السعر الشهري",
      dataIndex: "monthlyPrice",
      key: "monthlyPrice",
      render: (price: number) => (
        <span className="font-medium">
          {price === 0 ? "مجاني" : `${formatNumber(price)} ريال`}
        </span>
      ),
    },
    {
      title: "استخدام التوكينز",
      key: "tokenUsage",
      render: (_: any, record: any) => {
        const percentage = (record.tokensUsed / record.tokensLimit) * 100;
        return (
          <div className="w-32">
            <Progress
              percent={percentage}
              size="small"
              status={percentage > 90 ? "exception" : "normal"}
            />
            <div className="text-xs text-gray-500 mt-1">
              {formatNumber(record.tokensUsed)} / {formatNumber(record.tokensLimit)}
            </div>
          </div>
        );
      },
    },
    {
      title: "تاريخ الانتهاء",
      dataIndex: "endDate",
      key: "endDate",
      render: (date: Date | null) => (
        <span className={date && new Date(date) < new Date() ? "text-red-500" : ""}>
          {date ? formatDateTime(date) : "غير محدد"}
        </span>
      ),
    },
    {
      title: "الإجراءات",
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
            title="عرض التفاصيل"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleUpgrade(record)}
            title="تعديل الاشتراك"
          />
        </Space>
      ),
    },
  ];

  // Filter subscriptions
  const filteredSubscriptions = subscriptions.filter((sub) => {
    let matchesSearch = true;
    let matchesTier = true;
    let matchesStatus = true;
    let matchesDateRange = true;
    let matchesTab = true;

    if (searchText) {
      matchesSearch =
        sub.userName.toLowerCase().includes(searchText.toLowerCase()) ||
        sub.email.toLowerCase().includes(searchText.toLowerCase());
    }

    if (selectedTier) {
      matchesTier = sub.tier === selectedTier;
    }

    if (selectedStatus) {
      matchesStatus = sub.status === selectedStatus;
    }

    if (dateRange) {
      const [startDate, endDate] = dateRange;
      matchesDateRange =
        sub.startDate >= startDate && sub.startDate <= endDate;
    }

    // Filter by active tab
    switch (activeTab) {
      case "active":
        matchesTab = sub.status === "active";
        break;
      case "expired":
        matchesTab = sub.status === "expired" || sub.status === "cancelled";
        break;
      case "expiring":
        matchesTab = sub.endDate && new Date(sub.endDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
        break;
      case "high-usage":
        matchesTab = (sub.tokensUsed / sub.tokensLimit) > 0.8;
        break;
      default:
        matchesTab = true;
    }

    return matchesSearch && matchesTier && matchesStatus && matchesDateRange && matchesTab;
  });

  // Statistics
  const stats = {
    total: subscriptions.length,
    active: subscriptions.filter(s => s.status === "active").length,
    expired: subscriptions.filter(s => s.status === "expired").length,
    revenue: subscriptions
      .filter(s => s.status === "active")
      .reduce((sum, s) => sum + s.monthlyPrice, 0),
    expiringSoon: subscriptions.filter(s => 
      s.endDate && new Date(s.endDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    ).length,
    highUsage: subscriptions.filter(s => 
      (s.tokensUsed / s.tokensLimit) > 0.8
    ).length,
  };

  // Find a subscription with high token usage for the alert example
  const highUsageSubscription = subscriptions.find(s => (s.tokensUsed / s.tokensLimit) > 0.9);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">إدارة الاشتراكات</h2>
      </div>

      {/* Token usage alert for users with high usage */}
      {showTokenAlert && highUsageSubscription && (
        <TokenUsageAlert 
          currentUsage={highUsageSubscription.tokensUsed}
          maxAllowed={highUsageSubscription.tokensLimit}
          resetDate="2024-07-31"
          onClose={() => setShowTokenAlert(false)}
        />
      )}

      {/* Quick stats */}
      <Row gutter={16} className="mb-6">
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الاشتراكات"
              value={stats.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="الاشتراكات النشطة"
              value={stats.active}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CrownOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="الإيرادات الشهرية"
              value={stats.revenue}
              suffix="ريال"
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="تنتهي قريباً"
              value={stats.expiringSoon}
              valueStyle={{ color: '#cf1322' }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="استخدام عالي للتوكينز"
              value={stats.highUsage}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="معدل التجديد"
              value={85}
              suffix="%"
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Search filters */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <Input
            placeholder="البحث في الاشتراكات..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder="المستوى"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedTier(value)}
            options={[
              { value: "free", label: "مجاني" },
              { value: "basic", label: "أساسي" },
              { value: "premium", label: "متقدم" },
              { value: "enterprise", label: "مؤسسي" },
            ]}
          />
          <Select
            placeholder="الحالة"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedStatus(value)}
            options={[
              { value: "active", label: "نشط" },
              { value: "expired", label: "منتهي" },
              { value: "cancelled", label: "ملغي" },
              { value: "pending", label: "قيد الانتظار" },
            ]}
          />
          <RangePicker
            placeholder={["تاريخ البداية", "تاريخ النهاية"]}
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]?.toDate() as Date, dates[1]?.toDate() as Date]);
              } else {
                setDateRange(null);
              }
            }}
          />
          <Button icon={<FilterOutlined />} onClick={() => {
            setSearchText("");
            setSelectedTier(null);
            setSelectedStatus(null);
            setDateRange(null);
          }}>
            إعادة تعيين
          </Button>
        </div>
      </Card>

      {/* Subscription tabs */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={`النشطة (${stats.active})`} key="active">
            <Table
              dataSource={filteredSubscriptions}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab={`المنتهية (${stats.expired})`} key="expired">
            <Table
              dataSource={filteredSubscriptions}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab={`تنتهي قريباً (${stats.expiringSoon})`} key="expiring">
            <Table
              dataSource={filteredSubscriptions}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab={`استخدام عالي للتوكينز (${stats.highUsage})`} key="high-usage">
            <Table
              dataSource={filteredSubscriptions}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab={`الكل (${stats.total})`} key="all">
            <Table
              dataSource={filteredSubscriptions}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Subscription details modal */}
      <Modal
        title="تفاصيل الاشتراك"
        open={isDetailsModalVisible}
        onCancel={() => setIsDetailsModalVisible(false)}
        footer={null}
        width={700}
      >
        {selectedSubscription && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="المستخدم" span={2}>
                {selectedSubscription.userName} ({selectedSubscription.email})
              </Descriptions.Item>
              <Descriptions.Item label="المستوى">
                {getTierTag(selectedSubscription.tier)}
              </Descriptions.Item>
              <Descriptions.Item label="الحالة">
                {getStatusTag(selectedSubscription.status)}
              </Descriptions.Item>
              <Descriptions.Item label="السعر الشهري">
                {selectedSubscription.monthlyPrice === 0 
                  ? "مجاني" 
                  : `${formatNumber(selectedSubscription.monthlyPrice)} ريال`}
              </Descriptions.Item>
              <Descriptions.Item label="التجديد التلقائي">
                {selectedSubscription.autoRenew ? "مفعل" : "معطل"}
              </Descriptions.Item>
              <Descriptions.Item label="تاريخ البداية">
                {formatDateTime(selectedSubscription.startDate)}
              </Descriptions.Item>
              <Descriptions.Item label="تاريخ الانتهاء">
                {selectedSubscription.endDate 
                  ? formatDateTime(selectedSubscription.endDate) 
                  : "غير محدد"}
              </Descriptions.Item>
              <Descriptions.Item label="آخر دفعة">
                {selectedSubscription.lastPayment 
                  ? formatDateTime(selectedSubscription.lastPayment) 
                  : "لا يوجد"}
              </Descriptions.Item>
              <Descriptions.Item label="الفاتورة التالية">
                {selectedSubscription.nextBilling 
                  ? formatDateTime(selectedSubscription.nextBilling) 
                  : "لا يوجد"}
              </Descriptions.Item>
            </Descriptions>

            <div className="mt-6">
              <Title level={5}>استخدام التوكينز</Title>
              <Progress
                percent={(selectedSubscription.tokensUsed / selectedSubscription.tokensLimit) * 100}
                status={
                  (selectedSubscription.tokensUsed / selectedSubscription.tokensLimit) > 0.9 
                    ? "exception" 
                    : "normal"
                }
              />
              <Text type="secondary">
                {formatNumber(selectedSubscription.tokensUsed)} / {formatNumber(selectedSubscription.tokensLimit)} توكن
              </Text>
            </div>

            <div className="mt-6">
              <Title level={5}>الميزات المتاحة</Title>
              <div className="flex flex-wrap gap-2">
                {selectedSubscription.features.map((feature: string, index: number) => (
                  <Tag key={index} color="blue">{feature}</Tag>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Upgrade subscription modal */}
      <Modal
        title="تعديل الاشتراك"
        open={isUpgradeModalVisible}
        onCancel={() => setIsUpgradeModalVisible(false)}
        footer={null}
        width={500}
      >
        {selectedSubscription && (
          <Form layout="vertical">
            <Form.Item label="المستوى الحالي">
              <div>{getTierTag(selectedSubscription.tier)}</div>
            </Form.Item>
            
            <Form.Item label="المستوى الجديد" name="newTier">
              <Select
                placeholder="اختر المستوى الجديد"
                options={[
                  { value: "free", label: "مجاني" },
                  { value: "basic", label: "أساسي - 49 ريال/شهر" },
                  { value: "premium", label: "متقدم - 99 ريال/شهر" },
                  { value: "enterprise", label: "مؤسسي - 299 ريال/شهر" },
                ]}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" icon={<ArrowUpOutlined />}>
                  ترقية الاشتراك
                </Button>
                <Button icon={<ArrowDownOutlined />}>
                  تخفيض الاشتراك
                </Button>
                <Button onClick={() => setIsUpgradeModalVisible(false)}>
                  إلغاء
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};
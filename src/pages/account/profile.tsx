import { useState, useEffect } from "react";
import { Card, Form, Input, Button, Upload, message, Avatar, Tabs, Divider, Space, Alert } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined, UploadOutlined, LockOutlined } from "@ant-design/icons";
import { useTranslation } from "../../hooks/use-translation";
import { useAuth } from "../../contexts/auth-context";
import { supabase } from "../../lib/supabase";

const { TabPane } = Tabs;

export const ProfilePage = () => {
  const { t } = useTranslation();
  const { user, profile, refreshUser } = useAuth();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (profile) {
      profileForm.setFieldsValue({
        fullName: profile.full_name,
        email: profile.email,
        phone: profile.phone || '',
      });
      
      if (profile.avatar_url) {
        setAvatarUrl(profile.avatar_url);
      }
    }
  }, [profile, profileForm]);

  const handleProfileUpdate = async (values: any) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!user) {
        throw new Error("لم يتم العثور على المستخدم");
      }
      
      // Update user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          full_name: values.fullName,
        }
      });
      
      if (updateError) {
        throw updateError;
      }
      
      // Update profile in the profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          full_name: values.fullName,
          phone: values.phone || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);
      
      if (profileError) {
        throw profileError;
      }
      
      message.success("تم تحديث الملف الشخصي بنجاح");
      await refreshUser();
    } catch (err) {
      console.error("Profile update error:", err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("حدث خطأ أثناء تحديث الملف الشخصي");
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordUpdate = async (values: any) => {
    try {
      setPasswordLoading(true);
      setError(null);
      
      // Update password
      const { error: passwordError } = await supabase.auth.updateUser({
        password: values.newPassword,
      });
      
      if (passwordError) {
        throw passwordError;
      }
      
      message.success("تم تحديث كلمة المرور بنجاح");
      passwordForm.resetFields();
    } catch (err) {
      console.error("Password update error:", err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("حدث خطأ أثناء تحديث كلمة المرور");
      }
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleAvatarUpload = async (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    
    if (info.file.status === 'done') {
      try {
        if (!user) {
          throw new Error("لم يتم العثور على المستخدم");
        }
        
        const file = info.file.originFileObj;
        const fileExt = file.name.split('.').pop();
        const fileName = `${user.id}-${Date.now()}.${fileExt}`;
        const filePath = `avatars/${fileName}`;
        
        // Upload the file to Supabase Storage
        const { error: uploadError } = await supabase.storage
          .from('avatars')
          .upload(filePath, file);
        
        if (uploadError) {
          throw uploadError;
        }
        
        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('avatars')
          .getPublicUrl(filePath);
        
        const avatarUrl = urlData.publicUrl;
        
        // Update the profile with the new avatar URL
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            avatar_url: avatarUrl,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);
        
        if (updateError) {
          throw updateError;
        }
        
        setAvatarUrl(avatarUrl);
        message.success("تم تحديث الصورة الشخصية بنجاح");
        await refreshUser();
      } catch (err) {
        console.error("Avatar upload error:", err);
        message.error("فشل تحميل الصورة الشخصية");
      }
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">الملف الشخصي</h2>
      </div>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          className="mb-4"
          closable
          onClose={() => setError(null)}
        />
      )}

      <Card>
        <Tabs defaultActiveKey="profile">
          <TabPane
            tab={
              <span>
                <UserOutlined /> المعلومات الشخصية
              </span>
            }
            key="profile"
          >
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/3 flex flex-col items-center">
                <Avatar
                  size={120}
                  icon={<UserOutlined />}
                  src={avatarUrl}
                  className="mb-4"
                />
                <Upload
                  name="avatar"
                  showUploadList={false}
                  customRequest={({ onSuccess }) => {
                    setTimeout(() => {
                      onSuccess?.("ok");
                    }, 0);
                  }}
                  onChange={handleAvatarUpload}
                >
                  <Button icon={<UploadOutlined />}>تغيير الصورة</Button>
                </Upload>
                <Divider />
                <div className="text-center">
                  <h3 className="text-lg font-medium">{profile?.full_name}</h3>
                  <p className="text-gray-500">{profile?.email}</p>
                  <p className="text-gray-500">
                    {profile?.role === 'administrator' ? 'مسؤول' :
                     profile?.role === 'moderator' ? 'مشرف' :
                     profile?.role === 'subscriber_enterprise' ? 'مشترك مؤسسي' :
                     profile?.role === 'subscriber_premium' ? 'مشترك متقدم' :
                     profile?.role === 'subscriber_basic' ? 'مشترك أساسي' :
                     'مستخدم عادي'}
                  </p>
                </div>
              </div>
              
              <div className="md:w-2/3">
                <Form
                  form={profileForm}
                  layout="vertical"
                  onFinish={handleProfileUpdate}
                >
                  <Form.Item
                    name="fullName"
                    label="الاسم الكامل"
                    rules={[
                      { required: true, message: "الرجاء إدخال الاسم الكامل" },
                    ]}
                  >
                    <Input prefix={<UserOutlined />} />
                  </Form.Item>
                  
                  <Form.Item
                    name="email"
                    label="البريد الإلكتروني"
                  >
                    <Input prefix={<MailOutlined />} disabled />
                  </Form.Item>
                  
                  <Form.Item
                    name="phone"
                    label="رقم الهاتف"
                  >
                    <Input prefix={<PhoneOutlined />} />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                    >
                      حفظ التغييرات
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <LockOutlined /> الأمان
              </span>
            }
            key="security"
          >
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium mb-4">تغيير كلمة المرور</h3>
              
              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordUpdate}
              >
                <Form.Item
                  name="newPassword"
                  label="كلمة المرور الجديدة"
                  rules={[
                    { required: true, message: "الرجاء إدخال كلمة المرور الجديدة" },
                    { min: 8, message: "كلمة المرور يجب أن تكون 8 أحرف على الأقل" },
                    { 
                      pattern: /[A-Z]/, 
                      message: "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل" 
                    },
                    { 
                      pattern: /[0-9]/, 
                      message: "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل" 
                    },
                    { 
                      pattern: /[^A-Za-z0-9]/, 
                      message: "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل" 
                    }
                  ]}
                >
                  <Input.Password prefix={<LockOutlined />} />
                </Form.Item>
                
                <Form.Item
                  name="confirmPassword"
                  label="تأكيد كلمة المرور"
                  dependencies={['newPassword']}
                  rules={[
                    { required: true, message: "الرجاء تأكيد كلمة المرور" },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newPassword') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('كلمات المرور غير متطابقة'));
                      },
                    }),
                  ]}
                >
                  <Input.Password prefix={<LockOutlined />} />
                </Form.Item>
                
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={passwordLoading}
                  >
                    تحديث كلمة المرور
                  </Button>
                </Form.Item>
              </Form>
              
              <Divider />
              
              <h3 className="text-lg font-medium mb-4">الحسابات المرتبطة</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Avatar icon={<GoogleOutlined />} />
                    <span>جوجل</span>
                  </div>
                  <Button>ربط</Button>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Avatar icon={<GithubOutlined />} />
                    <span>جيثب</span>
                  </div>
                  <Button>ربط</Button>
                </div>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};
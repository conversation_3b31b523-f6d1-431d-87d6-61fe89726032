import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, But<PERSON>, Avatar, message, <PERSON><PERSON>, Spin } from "antd";
import { GoogleOutlined, GithubOutlined, LinkOutlined, DisconnectOutlined } from "@ant-design/icons";
import { useAuth } from "../../contexts/auth-context";
import { linkProvider, unlinkProvider, supabase } from "../../lib/supabase";

interface LinkedProvider {
  id: string;
  provider: 'google' | 'github';
  name: string;
  icon: React.ReactNode;
  isLinked: boolean;
}

export const LinkedAccountsPage = () => {
  const { user, refreshUser } = useAuth();
  const [providers, setProviders] = useState<LinkedProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [linkingProvider, setLinkingProvider] = useState<string | null>(null);

  useEffect(() => {
    const fetchLinkedAccounts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (!user) {
          throw new Error("لم يتم العثور على المستخدم");
        }
        
        // Get the user's identities
        const { data: { identities } = { identities: [] }, error: identitiesError } = await supabase.auth.getUser();
        
        if (identitiesError) {
          throw identitiesError;
        }
        
        // Create the providers list
        const providersList: LinkedProvider[] = [
          {
            id: 'google',
            provider: 'google',
            name: 'جوجل',
            icon: <GoogleOutlined />,
            isLinked: identities?.some(identity => identity.provider === 'google') || false,
          },
          {
            id: 'github',
            provider: 'github',
            name: 'جيثب',
            icon: <GithubOutlined />,
            isLinked: identities?.some(identity => identity.provider === 'github') || false,
          },
        ];
        
        setProviders(providersList);
      } catch (err) {
        console.error("Error fetching linked accounts:", err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("حدث خطأ أثناء جلب الحسابات المرتبطة");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchLinkedAccounts();
  }, [user]);

  const handleLinkProvider = async (provider: 'google' | 'github') => {
    try {
      setLinkingProvider(provider);
      setError(null);
      
      const { data, error: linkError } = await linkProvider(provider);
      
      if (linkError) {
        throw linkError;
      }
      
      // The user will be redirected to the provider's login page
      console.log("Provider linking initiated:", data);
    } catch (err) {
      console.error(`Error linking ${provider}:`, err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError(`فشل ربط حساب ${provider === 'google' ? 'جوجل' : 'جيثب'}`);
      }
      setLinkingProvider(null);
    }
  };

  const handleUnlinkProvider = async (provider: 'google' | 'github') => {
    try {
      setLinkingProvider(provider);
      setError(null);
      
      // Check if this is the only identity
      const { data: { identities } = { identities: [] } } = await supabase.auth.getUser();
      
      if (identities && identities.length <= 1) {
        throw new Error("لا يمكن إلغاء ربط الحساب الوحيد. يجب أن يكون لديك طريقة تسجيل دخول واحدة على الأقل");
      }
      
      const { error: unlinkError } = await unlinkProvider(provider);
      
      if (unlinkError) {
        throw unlinkError;
      }
      
      message.success(`تم إلغاء ربط حساب ${provider === 'google' ? 'جوجل' : 'جيثب'} بنجاح`);
      
      // Update the providers list
      setProviders(prev => 
        prev.map(p => 
          p.provider === provider ? { ...p, isLinked: false } : p
        )
      );
      
      await refreshUser();
    } catch (err) {
      console.error(`Error unlinking ${provider}:`, err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError(`فشل إلغاء ربط حساب ${provider === 'google' ? 'جوجل' : 'جيثب'}`);
      }
    } finally {
      setLinkingProvider(null);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">الحسابات المرتبطة</h2>
      </div>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          className="mb-4"
          closable
          onClose={() => setError(null)}
        />
      )}

      <Card>
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <p className="mb-4">
              يمكنك ربط حسابك بمزودي المصادقة الاجتماعية لتسهيل تسجيل الدخول في المستقبل.
            </p>
            
            <List
              itemLayout="horizontal"
              dataSource={providers}
              renderItem={item => (
                <List.Item
                  actions={[
                    item.isLinked ? (
                      <Button
                        icon={<DisconnectOutlined />}
                        danger
                        loading={linkingProvider === item.provider}
                        onClick={() => handleUnlinkProvider(item.provider)}
                      >
                        إلغاء الربط
                      </Button>
                    ) : (
                      <Button
                        type="primary"
                        icon={<LinkOutlined />}
                        loading={linkingProvider === item.provider}
                        onClick={() => handleLinkProvider(item.provider)}
                      >
                        ربط
                      </Button>
                    )
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={item.icon} />}
                    title={item.name}
                    description={item.isLinked ? "متصل" : "غير متصل"}
                  />
                </List.Item>
              )}
            />
          </>
        )}
      </Card>
    </div>
  );
};
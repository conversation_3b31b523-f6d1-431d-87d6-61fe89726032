import { useState } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Tabs,
  Table,
  Tag,
  Space,
  Typography,
} from "antd";
import {
  Line<PERSON>hart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import {
  UserOutlined,
  MessageOutlined,
  KeyOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatNumber, formatDateTime } from "../lib/utils";

const { RangePicker } = DatePicker;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

export const AnalyticsDashboard = () => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [activeTab, setActiveTab] = useState("usage");

  // بيانات تجريبية للإحصائيات
  const stats = {
    totalUsers: 1250,
    activeUsers: 780,
    totalChats: 5430,
    totalTokens: 1250000,
    responseTime: "0.8s",
    errorRate: "0.5%",
    uptime: "99.9%",
    newUsers: 45,
    newChats: 120,
    tokensToday: 35000,
  };

  // بيانات تجريبية للرسوم البيانية
  const usageData = [
    { name: "1 فبراير", users: 120, chats: 350, tokens: 95000 },
    { name: "2 فبراير", users: 132, chats: 380, tokens: 102000 },
    { name: "3 فبراير", users: 125, chats: 400, tokens: 110000 },
    { name: "4 فبراير", users: 130, chats: 420, tokens: 115000 },
    { name: "5 فبراير", users: 145, chats: 450, tokens: 125000 },
    { name: "6 فبراير", users: 150, chats: 470, tokens: 130000 },
    { name: "7 فبراير", users: 155, chats: 490, tokens: 135000 },
    { name: "8 فبراير", users: 160, chats: 510, tokens: 140000 },
    { name: "9 فبراير", users: 165, chats: 530, tokens: 145000 },
    { name: "10 فبراير", users: 170, chats: 550, tokens: 150000 },
    { name: "11 فبراير", users: 175, chats: 570, tokens: 155000 },
    { name: "12 فبراير", users: 180, chats: 590, tokens: 160000 },
    { name: "13 فبراير", users: 185, chats: 610, tokens: 165000 },
    { name: "14 فبراير", users: 190, chats: 630, tokens: 170000 },
  ];

  const performanceData = [
    { name: "1 فبراير", responseTime: 0.7, errorRate: 0.8, uptime: 99.9 },
    { name: "2 فبراير", responseTime: 0.8, errorRate: 0.7, uptime: 99.9 },
    { name: "3 فبراير", responseTime: 0.75, errorRate: 0.6, uptime: 99.9 },
    { name: "4 فبراير", responseTime: 0.9, errorRate: 0.9, uptime: 99.8 },
    { name: "5 فبراير", responseTime: 0.85, errorRate: 0.7, uptime: 99.9 },
    { name: "6 فبراير", responseTime: 0.8, errorRate: 0.6, uptime: 99.9 },
    { name: "7 فبراير", responseTime: 0.75, errorRate: 0.5, uptime: 99.9 },
    { name: "8 فبراير", responseTime: 0.7, errorRate: 0.4, uptime: 100 },
    { name: "9 فبراير", responseTime: 0.65, errorRate: 0.3, uptime: 100 },
    { name: "10 فبراير", responseTime: 0.6, errorRate: 0.4, uptime: 100 },
    { name: "11 فبراير", responseTime: 0.65, errorRate: 0.5, uptime: 99.9 },
    { name: "12 فبراير", responseTime: 0.7, errorRate: 0.6, uptime: 99.9 },
    { name: "13 فبراير", responseTime: 0.75, errorRate: 0.5, uptime: 99.9 },
    { name: "14 فبراير", responseTime: 0.8, errorRate: 0.4, uptime: 100 },
  ];

  const userBehaviorData = [
    { name: "استشارات قانونية", value: 45 },
    { name: "عقود", value: 25 },
    { name: "قضايا", value: 15 },
    { name: "أحوال شخصية", value: 10 },
    { name: "أخرى", value: 5 },
  ];

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

  const contentAnalysisData = [
    { name: "استشهادات قانونية", withCitations: 65, withoutCitations: 35 },
    { name: "دقة المعلومات", accurate: 85, inaccurate: 15 },
    { name: "تقييم المستخدمين", positive: 75, negative: 25 },
  ];

  // بيانات تجريبية للتقارير
  const reportsData = [
    {
      id: "1",
      name: "تقرير استخدام شهري",
      description: "تقرير شامل عن استخدام النظام خلال الشهر",
      lastGenerated: new Date("2024-02-15"),
      format: "PDF",
    },
    {
      id: "2",
      name: "تقرير المستخدمين النشطين",
      description: "تقرير عن المستخدمين النشطين وسلوكهم",
      lastGenerated: new Date("2024-02-10"),
      format: "Excel",
    },
    {
      id: "3",
      name: "تقرير الاستشهادات القانونية",
      description: "تحليل استخدام الاستشهادات القانونية",
      lastGenerated: new Date("2024-02-05"),
      format: "PDF",
    },
    {
      id: "4",
      name: "تقرير الأداء",
      description: "تقرير عن أداء النظام وأوقات الاستجابة",
      lastGenerated: new Date("2024-02-01"),
      format: "Excel",
    },
  ];

  const reportsColumns = [
    {
      title: "اسم التقرير",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "الوصف",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "آخر إنشاء",
      dataIndex: "lastGenerated",
      key: "lastGenerated",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: "التنسيق",
      dataIndex: "format",
      key: "format",
      render: (format: string) => (
        <Tag color={format === "PDF" ? "blue" : "green"}>{format}</Tag>
      ),
    },
    {
      title: "الإجراءات",
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => console.log("Download report:", record)}
          >
            تنزيل
          </Button>
          <Button
            size="small"
            onClick={() => console.log("Generate report:", record)}
          >
            إنشاء جديد
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("analytics.title")}</h2>
        <Space>
          <RangePicker
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]?.toDate() as Date, dates[1]?.toDate() as Date]);
              } else {
                setDateRange(null);
              }
            }}
          />
          <Select
            defaultValue="today"
            style={{ width: 120 }}
            options={[
              { value: "today", label: t("analytics.today") },
              { value: "yesterday", label: t("analytics.yesterday") },
              { value: "week", label: t("analytics.lastWeek") },
              { value: "month", label: t("analytics.lastMonth") },
              { value: "custom", label: t("analytics.custom") },
            ]}
          />
          <Button icon={<FilterOutlined />}>
            {t("analytics.applyFilter")}
          </Button>
        </Space>
      </div>

      {/* بطاقات الإحصائيات */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalUsers")}
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2 text-xs text-green-500">
              +{stats.newUsers} اليوم
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.activeUsers")}
              value={stats.activeUsers}
              prefix={<UserOutlined />}
              formatter={(value) => formatNumber(value as number)}
              valueStyle={{ color: "#3f8600" }}
            />
            <div className="mt-2 text-xs text-gray-500">
              {Math.round((stats.activeUsers / stats.totalUsers) * 100)}% من الإجمالي
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalChats")}
              value={stats.totalChats}
              prefix={<MessageOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2 text-xs text-green-500">
              +{stats.newChats} اليوم
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalTokens")}
              value={stats.totalTokens}
              prefix={<KeyOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
            <div className="mt-2 text-xs text-green-500">
              +{formatNumber(stats.tokensToday)} اليوم
            </div>
          </Card>
        </Col>
      </Row>

      {/* تبويبات التحليلات */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t("analytics.usageDashboard")} key="usage">
            <div className="mt-4">
              <Title level={4}>اتجاهات الاستخدام</Title>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={usageData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="users"
                      stroke="#8884d8"
                      name="المستخدمين النشطين"
                    />
                    <Line
                      type="monotone"
                      dataKey="chats"
                      stroke="#82ca9d"
                      name="المحادثات"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <Title level={4} className="mt-8">
                استخدام التوكينز
              </Title>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={usageData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="tokens"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="التوكينز"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabPane>

          <TabPane tab={t("analytics.performanceMetrics")} key="performance">
            <div className="mt-4">
              <Title level={4}>وقت الاستجابة</Title>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={performanceData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="responseTime"
                      stroke="#8884d8"
                      name="وقت الاستجابة (ثانية)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <Title level={4} className="mt-8">
                معدل الأخطاء
              </Title>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={performanceData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="errorRate"
                      stroke="#ff8042"
                      fill="#ff8042"
                      name="معدل الأخطاء (%)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabPane>

          <TabPane tab={t("analytics.userBehavior")} key="userBehavior">
            <div className="mt-4">
              <Title level={4}>توزيع موضوعات المحادثات</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={userBehaviorData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) =>
                            `${name}: ${(percent * 100).toFixed(0)}%`
                          }
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {userBehaviorData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={userBehaviorData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" name="عدد المحادثات">
                          {userBehaviorData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane tab={t("analytics.contentAnalysis")} key="contentAnalysis">
            <div className="mt-4">
              <Title level={4}>تحليل المحتوى</Title>
              <Row gutter={16}>
                <Col span={8}>
                  <Card title="الاستشهادات القانونية">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              {
                                name: "مع استشهادات",
                                value: contentAnalysisData[0].withCitations,
                              },
                              {
                                name: "بدون استشهادات",
                                value: contentAnalysisData[0].withoutCitations,
                              },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            outerRadius={60}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            <Cell fill="#0088FE" />
                            <Cell fill="#FF8042" />
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="دقة المعلومات">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              {
                                name: "دقيقة",
                                value: contentAnalysisData[1].accurate,
                              },
                              {
                                name: "غير دقيقة",
                                value: contentAnalysisData[1].inaccurate,
                              },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            outerRadius={60}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            <Cell fill="#00C49F" />
                            <Cell fill="#FF8042" />
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="تقييم المستخدمين">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              {
                                name: "إيجابي",
                                value: contentAnalysisData[2].positive,
                              },
                              {
                                name: "سلبي",
                                value: contentAnalysisData[2].negative,
                              },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            outerRadius={60}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            <Cell fill="#00C49F" />
                            <Cell fill="#FF8042" />
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane tab={t("analytics.customReports")} key="reports">
            <div className="mt-4">
              <Title level={4}>التقارير المتاحة</Title>
              <Table
                dataSource={reportsData}
                columns={reportsColumns}
                rowKey="id"
                pagination={false}
              />
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};
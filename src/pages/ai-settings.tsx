import { useState, useEffect } from "react";
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Select,
  Button,
  InputNumber,
  Space,
  Divider,
  message,
  Slider,
  Typography,
  Table,
  Tag,
  Progress,
  Row,
  Col,
  Statistic,
} from "antd";
import {
  RobotOutlined,
  SoundOutlined,
  TranslationOutlined,
  FileTextOutlined,
  SettingOutlined,
  MonitorOutlined,
  SaveOutlined,
  UndoOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { useList } from "@refinedev/core";

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

export const AISettingsPage = () => {
  const { t, language } = useTranslation();
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState("text-models");

  // Fetch external APIs from Supabase
  const { data: externalApisData, isLoading: isLoadingApis } = useList({
    resource: "external_apis",
    filters: [
      {
        field: "is_active",
        operator: "eq",
        value: true,
      },
    ],
  });

  // Group APIs by type
  const llmModels = externalApisData?.data?.filter(api => api.type === "llm") || [];
  const speechModels = externalApisData?.data?.filter(api => api.type === "speech") || [];
  const translationModels = externalApisData?.data?.filter(api => api.type === "translation") || [];
  const visionModels = externalApisData?.data?.filter(api => api.type === "vision") || [];
  const analysisModels = externalApisData?.data?.filter(api => api.type === "analysis") || [];

  // Sample data for AI settings
  const initialSettings = {
    textModels: {
      defaultModel: llmModels.length > 0 ? llmModels[0].id : "llama3:8b",
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.95,
      enableModelSwitching: true,
      enableQualityMonitoring: true,
    },
    voiceModels: {
      sttModel: speechModels.find(m => m.name.toLowerCase().includes("whisper"))?.id || "whisper-cpp",
      ttsModel: speechModels.find(m => m.name.toLowerCase().includes("xtts"))?.id || "xtts2",
      sampleRate: 16000,
      voiceGender: "neutral",
      speechSpeed: 1.0,
      enableVoiceChat: true,
      maxAudioDuration: 60,
      audioCompression: "opus",
    },
    translation: {
      enableTranslation: true,
      translationModel: translationModels.length > 0 ? translationModels[0].id : "qwen:7b",
      translationStyle: "contextual",
      enableSpellCheck: true,
      translationLevel: "accurate",
      dailyTranslationLimit: 1000,
    },
    legalAnalysis: {
      legalModel: llmModels.find(m => m.name.toLowerCase().includes("legal"))?.id || "legal-llama:7b",
      enableCitations: true,
      enableCompliance: true,
      autoDisclaimer: true,
      weeklyUpdate: true,
      citationFormat: "academic",
    },
    performance: {
      enableCaching: true,
      cacheTimeout: 3600,
      maxConcurrentRequests: 10,
      responseTimeout: 30,
      enableLoadBalancing: true,
      autoScaling: true,
    },
    integration: {
      enableAPI: true,
      enableWebhooks: false,
      enableABTesting: true,
      enableBackup: true,
      backupFrequency: "daily",
    },
  };

  // Update form when external APIs data is loaded
  useEffect(() => {
    if (externalApisData?.data?.length > 0) {
      // Only update if we have data and no changes have been made by the user
      if (!hasChanges) {
        const updatedSettings = { ...initialSettings };
        
        // Update default LLM model if available
        if (llmModels.length > 0) {
          updatedSettings.textModels.defaultModel = llmModels[0].id;
        }
        
        // Update STT and TTS models if available
        if (speechModels.length > 0) {
          const sttModel = speechModels.find(m => m.name.toLowerCase().includes("whisper"));
          const ttsModel = speechModels.find(m => m.name.toLowerCase().includes("xtts"));
          
          if (sttModel) updatedSettings.voiceModels.sttModel = sttModel.id;
          if (ttsModel) updatedSettings.voiceModels.ttsModel = ttsModel.id;
        }
        
        // Update translation model if available
        if (translationModels.length > 0) {
          updatedSettings.translation.translationModel = translationModels[0].id;
        }
        
        // Update legal model if available
        if (llmModels.length > 0) {
          const legalModel = llmModels.find(m => m.name.toLowerCase().includes("legal"));
          if (legalModel) updatedSettings.legalAnalysis.legalModel = legalModel.id;
        }
        
        form.setFieldsValue(updatedSettings);
      }
    }
  }, [externalApisData, form, hasChanges, llmModels, speechModels, translationModels]);

  const handleValuesChange = () => {
    setHasChanges(true);
  };

  const handleReset = () => {
    form.resetFields();
    setHasChanges(false);
  };

  const handleSubmit = (values: any) => {
    console.log("Submitted AI settings:", values);
    message.success(language === "ar" ? "تم حفظ إعدادات الذكاء الاصطناعي بنجاح" : "AI settings saved successfully");
    setHasChanges(false);
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case "active":
        return <Tag color="green" icon={<PlayCircleOutlined />}>{language === "ar" ? "نشط" : "Active"}</Tag>;
      case "inactive":
        return <Tag color="red" icon={<PauseCircleOutlined />}>{language === "ar" ? "متوقف" : "Inactive"}</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getPerformanceColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "green";
      case "good":
        return "blue";
      case "warning":
        return "orange";
      case "error":
        return "red";
      default:
        return "gray";
    }
  };

  // Convert external APIs to model options for select components
  const getLLMModelOptions = () => {
    return llmModels.map(model => ({
      label: model.name,
      value: model.id,
    }));
  };

  const getSpeechModelOptions = (type: 'stt' | 'tts') => {
    return speechModels
      .filter(model => {
        if (type === 'stt') return model.name.toLowerCase().includes('whisper') || model.name.toLowerCase().includes('speech-to-text');
        if (type === 'tts') return model.name.toLowerCase().includes('xtts') || model.name.toLowerCase().includes('text-to-speech');
        return true;
      })
      .map(model => ({
        label: model.name,
        value: model.id,
      }));
  };

  const getTranslationModelOptions = () => {
    return translationModels.map(model => ({
      label: model.name,
      value: model.id,
    }));
  };

  // Sample data for available models
  const availableModels = llmModels.map(model => ({
    id: model.id,
    name: model.name,
    type: "general",
    status: model.is_active ? "active" : "inactive",
    performance: 92,
    usage: 45,
    responseTime: "820ms",
  }));

  // Sample data for performance metrics
  const performanceMetrics = [
    { metric: language === "ar" ? "زمن الاستجابة" : "Response Time", model: "Llama3", value: "820ms", limit: "≤1500ms", status: "good" },
    { metric: language === "ar" ? "دقة الترجمة" : "Translation Accuracy", model: "Qwen", value: "92.3%", limit: "≥85%", status: "excellent" },
    { metric: language === "ar" ? "تكلفة/طلب" : "Cost/Request", model: "Legal-Llama", value: "0.002$", limit: "≤0.005$", status: "good" },
    { metric: language === "ar" ? "معدل النجاح" : "Success Rate", model: language === "ar" ? "جميع النماذج" : "All Models", value: "98.5%", limit: "≥95%", status: "excellent" },
  ];

  const modelsColumns = [
    {
      title: language === "ar" ? "النموذج" : "Model",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: any) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.type}</div>
        </div>
      ),
    },
    {
      title: language === "ar" ? "الحالة" : "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: language === "ar" ? "الأداء" : "Performance",
      dataIndex: "performance",
      key: "performance",
      render: (performance: number) => (
        <Progress percent={performance} size="small" />
      ),
    },
    {
      title: language === "ar" ? "الاستخدام" : "Usage",
      dataIndex: "usage",
      key: "usage",
      render: (usage: number) => `${usage}%`,
    },
    {
      title: language === "ar" ? "وقت الاستجابة" : "Response Time",
      dataIndex: "responseTime",
      key: "responseTime",
    },
    {
      title: language === "ar" ? "الإجراءات" : "Actions",
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button size="small" type="primary">
            {language === "ar" ? "تكوين" : "Configure"}
          </Button>
          <Button size="small">
            {record.status === "active" ? (language === "ar" ? "إيقاف" : "Stop") : (language === "ar" ? "تشغيل" : "Start")}
          </Button>
        </Space>
      ),
    },
  ];

  const metricsColumns = [
    {
      title: language === "ar" ? "المقياس" : "Metric",
      dataIndex: "metric",
      key: "metric",
    },
    {
      title: language === "ar" ? "النموذج" : "Model",
      dataIndex: "model",
      key: "model",
    },
    {
      title: language === "ar" ? "القيمة الحالية" : "Current Value",
      dataIndex: "value",
      key: "value",
    },
    {
      title: language === "ar" ? "الحد المسموح" : "Allowed Limit",
      dataIndex: "limit",
      key: "limit",
    },
    {
      title: language === "ar" ? "الحالة" : "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Tag color={getPerformanceColor(status)}>
          {status === "excellent" ? (language === "ar" ? "ممتاز" : "Excellent") : 
           status === "good" ? (language === "ar" ? "جيد" : "Good") : 
           status === "warning" ? (language === "ar" ? "تحذير" : "Warning") : (language === "ar" ? "خطأ" : "Error")}
        </Tag>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("aiSettings.title")}</h2>
        <Space>
          {hasChanges && (
            <>
              <Button
                icon={<UndoOutlined />}
                onClick={handleReset}
              >
                {language === "ar" ? "إعادة تعيين" : "Reset"}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
              >
                {language === "ar" ? "حفظ الإعدادات" : "Save Settings"}
              </Button>
            </>
          )}
        </Space>
      </div>

      {/* Quick Stats */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "النماذج النشطة" : "Active Models"}
              value={llmModels.filter(m => m.is_active).length}
              suffix={`/ ${llmModels.length}`}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "متوسط وقت الاستجابة" : "Avg Response Time"}
              value="820"
              suffix="ms"
              prefix={<MonitorOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "معدل النجاح" : "Success Rate"}
              value={98.5}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={language === "ar" ? "الطلبات اليومية" : "Daily Requests"}
              value={1234}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={initialSettings}
          onValuesChange={handleValuesChange}
          onFinish={handleSubmit}
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane
              tab={
                <span>
                  <RobotOutlined /> {t("aiSettings.textModels")}
                </span>
              }
              key="text-models"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "التحكم الشامل في النموذج" : "Comprehensive Model Control"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "إعدادات النماذج النصية ومعاملات التحكم في الاستجابات" 
                    : "Text model settings and response control parameters"}
                </Paragraph>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "defaultModel"]}
                      label={t("aiSettings.defaultModel")}
                      rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار النموذج الافتراضي" : "Please select the default model" }]}
                    >
                      <Select
                        loading={isLoadingApis}
                        options={getLLMModelOptions()}
                        placeholder={language === "ar" ? "اختر نموذجًا" : "Select a model"}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "maxTokens"]}
                      label={t("aiSettings.maxTokens")}
                    >
                      <InputNumber min={50} max={4000} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "temperature"]}
                      label={`${t("aiSettings.temperature")}: ${form.getFieldValue(["textModels", "temperature"]) || 0.7}`}
                    >
                      <Slider
                        min={0.1}
                        max={1.0}
                        step={0.1}
                        marks={{
                          0.1: language === "ar" ? "محافظ" : "Conservative",
                          0.5: language === "ar" ? "متوازن" : "Balanced",
                          1.0: language === "ar" ? "إبداعي" : "Creative"
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "topP"]}
                      label={`${t("aiSettings.topP")}: ${form.getFieldValue(["textModels", "topP"]) || 0.95}`}
                    >
                      <Slider
                        min={0.5}
                        max={1.0}
                        step={0.05}
                        marks={{
                          0.5: language === "ar" ? "دقيق" : "Precise",
                          0.75: language === "ar" ? "متوازن" : "Balanced",
                          1.0: language === "ar" ? "متنوع" : "Diverse"
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "enableModelSwitching"]}
                      label={t("aiSettings.enableModelSwitching")}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["textModels", "enableQualityMonitoring"]}
                      label={t("aiSettings.enableQualityMonitoring")}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider orientation="left">{language === "ar" ? "النماذج المتاحة" : "Available Models"}</Divider>
                {isLoadingApis ? (
                  <div className="text-center py-4">
                    <Statistic.Countdown value={Date.now() + 1000} format="ss:SSS" prefix="Loading..." />
                  </div>
                ) : (
                  <Table
                    dataSource={availableModels}
                    columns={modelsColumns}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                )}
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <SoundOutlined /> {t("aiSettings.voiceModels")}
                </span>
              }
              key="voice-models"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "معمارية الصوت المتكاملة" : "Integrated Voice Architecture"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "إعدادات نماذج التحويل من النص إلى الكلام والعكس" 
                    : "Settings for speech-to-text and text-to-speech models"}
                </Paragraph>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "sttModel"]}
                      label={language === "ar" ? "نموذج التحويل من الكلام إلى النص (STT)" : "Speech-to-Text Model (STT)"}
                    >
                      <Select
                        loading={isLoadingApis}
                        options={getSpeechModelOptions('stt')}
                        placeholder={language === "ar" ? "اختر نموذجًا" : "Select a model"}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "ttsModel"]}
                      label={language === "ar" ? "نموذج التحويل من النص إلى الكلام (TTS)" : "Text-to-Speech Model (TTS)"}
                    >
                      <Select
                        loading={isLoadingApis}
                        options={getSpeechModelOptions('tts')}
                        placeholder={language === "ar" ? "اختر نموذجًا" : "Select a model"}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "sampleRate"]}
                      label={language === "ar" ? "معدل العينات (Hz)" : "Sample Rate (Hz)"}
                    >
                      <Select
                        options={[
                          { value: 8000, label: "8kHz - " + (language === "ar" ? "جودة أساسية" : "Basic Quality") },
                          { value: 16000, label: "16kHz - " + (language === "ar" ? "جودة متوسطة" : "Medium Quality") },
                          { value: 22050, label: "22kHz - " + (language === "ar" ? "جودة عالية" : "High Quality") },
                          { value: 48000, label: "48kHz - " + (language === "ar" ? "جودة احترافية" : "Professional Quality") },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "audioCompression"]}
                      label={language === "ar" ? "ضغط الصوت" : "Audio Compression"}
                    >
                      <Select
                        options={[
                          { value: "opus", label: "Opus - " + (language === "ar" ? "ضغط ذكي" : "Smart Compression") },
                          { value: "mp3", label: "MP3 - " + (language === "ar" ? "متوافق" : "Compatible") },
                          { value: "pcm", label: "PCM - " + (language === "ar" ? "بدون ضغط" : "Uncompressed") },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "voiceGender"]}
                      label={language === "ar" ? "نبرة الصوت" : "Voice Tone"}
                    >
                      <Select
                        options={[
                          { value: "male", label: language === "ar" ? "ذكورية" : "Male" },
                          { value: "female", label: language === "ar" ? "أنثوية" : "Female" },
                          { value: "neutral", label: language === "ar" ? "محايدة" : "Neutral" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "speechSpeed"]}
                      label={`${language === "ar" ? "سرعة الكلام" : "Speech Speed"}: ${form.getFieldValue(["voiceModels", "speechSpeed"]) || 1.0}x`}
                    >
                      <Slider
                        min={0.8}
                        max={1.5}
                        step={0.1}
                        marks={{
                          0.8: language === "ar" ? "بطيء" : "Slow",
                          1.0: language === "ar" ? "طبيعي" : "Normal",
                          1.5: language === "ar" ? "سريع" : "Fast"
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "maxAudioDuration"]}
                      label={language === "ar" ? "مدة التسجيل القصوى (ثانية)" : "Maximum Recording Duration (seconds)"}
                    >
                      <InputNumber min={10} max={120} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["voiceModels", "enableVoiceChat"]}
                      label={language === "ar" ? "تمكين المحادثة الصوتية" : "Enable Voice Chat"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <TranslationOutlined /> {t("aiSettings.translation")}
                </span>
              }
              key="translation"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "طبقة الترجمة الديناميكية" : "Dynamic Translation Layer"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "إعدادات الترجمة والتحويل بين اللغات" 
                    : "Translation and language conversion settings"}
                </Paragraph>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "translationModel"]}
                      label={language === "ar" ? "نموذج الترجمة" : "Translation Model"}
                    >
                      <Select
                        loading={isLoadingApis}
                        options={getTranslationModelOptions()}
                        placeholder={language === "ar" ? "اختر نموذجًا" : "Select a model"}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "translationStyle"]}
                      label={language === "ar" ? "نمط الترجمة" : "Translation Style"}
                    >
                      <Select
                        options={[
                          { value: "literal", label: language === "ar" ? "حرفية - للنصوص القانونية" : "Literal - for legal texts" },
                          { value: "contextual", label: language === "ar" ? "سياقية - للمحادثات" : "Contextual - for conversations" },
                          { value: "creative", label: language === "ar" ? "إبداعية - للمحتوى الأدبي" : "Creative - for literary content" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "translationLevel"]}
                      label={language === "ar" ? "مستوى الترجمة" : "Translation Level"}
                    >
                      <Select
                        options={[
                          { value: "simple", label: language === "ar" ? "مبسط - سهل الفهم" : "Simple - easy to understand" },
                          { value: "accurate", label: language === "ar" ? "دقيق - احترافي" : "Accurate - professional" },
                          { value: "academic", label: language === "ar" ? "أكاديمي - متخصص" : "Academic - specialized" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "dailyTranslationLimit"]}
                      label={language === "ar" ? "الحد اليومي للترجمة" : "Daily Translation Limit"}
                    >
                      <InputNumber min={100} max={10000} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "enableTranslation"]}
                      label={language === "ar" ? "تمكين الترجمة" : "Enable Translation"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["translation", "enableSpellCheck"]}
                      label={language === "ar" ? "تمكين التصحيح اللغوي" : "Enable Spell Check"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <FileTextOutlined /> {t("aiSettings.legalAnalysis")}
                </span>
              }
              key="legal-analysis"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "النموذج المتخصص" : "Specialized Model"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "إعدادات التحليل القانوني والاستشهادات" 
                    : "Legal analysis and citation settings"}
                </Paragraph>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "legalModel"]}
                      label={language === "ar" ? "النموذج القانوني" : "Legal Model"}
                    >
                      <Select
                        loading={isLoadingApis}
                        options={llmModels
                          .filter(model => model.name.toLowerCase().includes('legal'))
                          .map(model => ({
                            label: model.name,
                            value: model.id,
                          }))}
                        placeholder={language === "ar" ? "اختر نموذجًا" : "Select a model"}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "citationFormat"]}
                      label={language === "ar" ? "تنسيق الاستشهادات" : "Citation Format"}
                    >
                      <Select
                        options={[
                          { value: "academic", label: language === "ar" ? "أكاديمي - APA" : "Academic - APA" },
                          { value: "legal", label: language === "ar" ? "قانوني - Bluebook" : "Legal - Bluebook" },
                          { value: "simple", label: language === "ar" ? "مبسط - مرجع مباشر" : "Simple - Direct Reference" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "enableCitations"]}
                      label={language === "ar" ? "تمكين الاستشهادات" : "Enable Citations"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "enableCompliance"]}
                      label={language === "ar" ? "تمكين ضوابط الامتثال" : "Enable Compliance Controls"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "autoDisclaimer"]}
                      label={language === "ar" ? "إضافة تنبيه تلقائي" : "Add Automatic Disclaimer"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["legalAnalysis", "weeklyUpdate"]}
                      label={language === "ar" ? "تحديث أسبوعي للمعرفة القانونية" : "Weekly Legal Knowledge Update"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider orientation="left">{language === "ar" ? "معاملات النموذج القانوني" : "Legal Model Parameters"}</Divider>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-sm">
{`{
  "temperature": 0.2,
  "top_p": 0.85,
  "max_tokens": 1500,
  "stop_sequences": ["###", "${language === "ar" ? "المادة" : "Article"}"],
  "context_window": 4096,
  "training_data": "${language === "ar" ? "1.2M وثيقة قانونية عربية" : "1.2M Arabic legal documents"}"
}`}
                  </pre>
                </div>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <MonitorOutlined /> {t("aiSettings.performance")}
                </span>
              }
              key="performance"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "لوحة تحليل الاستخدام" : "Usage Analysis Dashboard"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "مقاييس الأداء والإحصائيات التفصيلية" 
                    : "Performance metrics and detailed statistics"}
                </Paragraph>

                <Table
                  dataSource={performanceMetrics}
                  columns={metricsColumns}
                  rowKey="metric"
                  pagination={false}
                  className="mb-6"
                />

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "enableCaching"]}
                      label={language === "ar" ? "تمكين التخزين المؤقت" : "Enable Caching"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "enableLoadBalancing"]}
                      label={language === "ar" ? "تمكين توزيع الأحمال" : "Enable Load Balancing"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "cacheTimeout"]}
                      label={language === "ar" ? "مهلة التخزين المؤقت (ثانية)" : "Cache Timeout (seconds)"}
                    >
                      <InputNumber min={300} max={7200} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "maxConcurrentRequests"]}
                      label={language === "ar" ? "الحد الأقصى للطلبات المتزامنة" : "Max Concurrent Requests"}
                    >
                      <InputNumber min={5} max={50} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "responseTimeout"]}
                      label={language === "ar" ? "مهلة الاستجابة (ثانية)" : "Response Timeout (seconds)"}
                    >
                      <InputNumber min={10} max={120} style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["performance", "autoScaling"]}
                      label={language === "ar" ? "تمكين التوسع التلقائي" : "Enable Auto Scaling"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <SettingOutlined /> {t("aiSettings.integration")}
                </span>
              }
              key="integration"
            >
              <div className="mt-4">
                <Title level={4}>{language === "ar" ? "آليات التحديث الديناميكي" : "Dynamic Update Mechanisms"}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  {language === "ar" 
                    ? "إعدادات التكامل والنسخ الاحتياطي للإعدادات" 
                    : "Integration and backup settings for configurations"}
                </Paragraph>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["integration", "enableAPI"]}
                      label={language === "ar" ? "تمكين API للإعدادات" : "Enable API for Settings"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["integration", "enableWebhooks"]}
                      label={language === "ar" ? "تمكين Webhooks" : "Enable Webhooks"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["integration", "enableABTesting"]}
                      label={language === "ar" ? "تمكين A/B Testing" : "Enable A/B Testing"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={["integration", "enableBackup"]}
                      label={language === "ar" ? "تمكين النسخ الاحتياطي" : "Enable Backup"}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={["integration", "backupFrequency"]}
                      label={language === "ar" ? "تكرار النسخ الاحتياطي" : "Backup Frequency"}
                    >
                      <Select
                        options={[
                          { value: "hourly", label: language === "ar" ? "كل ساعة" : "Hourly" },
                          { value: "daily", label: language === "ar" ? "يومي" : "Daily" },
                          { value: "weekly", label: language === "ar" ? "أسبوعي" : "Weekly" },
                          { value: "monthly", label: language === "ar" ? "شهري" : "Monthly" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />

                <Space>
                  <Button type="primary">
                    {language === "ar" ? "إنشاء نسخة احتياطية الآن" : "Create Backup Now"}
                  </Button>
                  <Button>
                    {language === "ar" ? "استعادة من نسخة احتياطية" : "Restore from Backup"}
                  </Button>
                  <Button>
                    {language === "ar" ? "تصدير الإعدادات" : "Export Settings"}
                  </Button>
                  <Button>
                    {language === "ar" ? "استيراد الإعدادات" : "Import Settings"}
                  </Button>
                </Space>
              </div>
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </div>
  );
};
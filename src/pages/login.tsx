import { useState } from "react";
import { Form, Input, Button, Checkbox, Card, message, Tabs, Divider, Alert } from "antd";
import { UserOutlined, LockOutlined, MailOutlined, GoogleOutlined, GithubOutlined } from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { useTheme } from "../hooks/use-theme";
import { useNavigate, Link } from "react-router-dom";
import { signInWithEmail, signInWithProvider, signUpWithEmail } from "../lib/supabase";
import { z } from "zod";
import { MaintenanceMessage } from "../components/auth/maintenance-message";

const { TabPane } = Tabs;

// Validation schemas
const loginSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صالح"),
  password: z.string().min(6, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"),
  remember: z.boolean().optional(),
});

const registerSchema = z.object({
  fullName: z.string().min(3, "الاسم يجب أن يكون 3 أحرف على الأقل"),
  email: z.string().email("البريد الإلكتروني غير صالح"),
  password: z.string()
    .min(8, "كلمة المرور يجب أن تكون 8 أحرف على الأقل")
    .regex(/[A-Z]/, "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل")
    .regex(/[0-9]/, "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل")
    .regex(/[^A-Za-z0-9]/, "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل"),
  confirmPassword: z.string(),
  agreeTerms: z.literal(true, {
    errorMap: () => ({ message: "يجب الموافقة على الشروط والأحكام" }),
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

// Set this to true to show the maintenance message
const SYSTEM_MAINTENANCE = true;

export const Login = () => {
  const { t } = useTranslation();
  const { actualTheme } = useTheme();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("login");
  const [loginLoading, setLoginLoading] = useState(false);
  const [registerLoading, setRegisterLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState<string | null>(null);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [registerError, setRegisterError] = useState<string | null>(null);

  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";

  const handleLogin = async (values: LoginFormValues) => {
    try {
      setLoginLoading(true);
      setLoginError(null);
      
      // Validate form data
      loginSchema.parse(values);
      
      const { data, error } = await signInWithEmail(values.email, values.password);
      
      if (error) {
        if (error.message.includes("Invalid login")) {
          throw new Error("بيانات الاعتماد غير صحيحة");
        } else if (error.message.includes("rate limit")) {
          throw new Error("تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. الرجاء المحاولة لاحقاً");
        } else {
          throw error;
        }
      }
      
      if (data?.user) {
        message.success("تم تسجيل الدخول بنجاح");
        navigate("/");
      }
    } catch (error) {
      console.error("Login error:", error);
      if (error instanceof z.ZodError) {
        setLoginError(error.errors[0].message);
      } else if (error instanceof Error) {
        setLoginError(error.message);
      } else {
        setLoginError("حدث خطأ أثناء تسجيل الدخول");
      }
    } finally {
      setLoginLoading(false);
    }
  };

  const handleRegister = async (values: RegisterFormValues) => {
    try {
      setRegisterLoading(true);
      setRegisterError(null);
      
      // Validate form data
      registerSchema.parse(values);
      
      const { data, error } = await signUpWithEmail(
        values.email,
        values.password,
        values.fullName
      );
      
      if (error) {
        if (error.message.includes("already registered")) {
          throw new Error("البريد الإلكتروني مسجل بالفعل");
        } else {
          throw error;
        }
      }
      
      if (data) {
        message.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب.");
        setActiveTab("login");
      }
    } catch (error) {
      console.error("Register error:", error);
      if (error instanceof z.ZodError) {
        setRegisterError(error.errors[0].message);
      } else if (error instanceof Error) {
        setRegisterError(error.message);
      } else {
        setRegisterError("حدث خطأ أثناء إنشاء الحساب");
      }
    } finally {
      setRegisterLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'github') => {
    try {
      setSocialLoading(provider);
      const { data, error } = await signInWithProvider(provider);
      
      if (error) {
        throw error;
      }
      
      // The user will be redirected to the provider's login page
      console.log("Social login initiated:", data);
    } catch (error) {
      console.error(`${provider} login error:`, error);
      message.error(`فشل تسجيل الدخول باستخدام ${provider === 'google' ? 'جوجل' : 'جيثب'}`);
    } finally {
      setSocialLoading(null);
    }
  };

  // If system is under maintenance, show the maintenance message
  if (SYSTEM_MAINTENANCE) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="w-full max-w-lg">
          <div className="text-center mb-8">
            <img
              src={logoSrc}
              alt="شُريح"
              className="logo-component mx-auto mb-4"
              style={{ height: "80px" }}
            />
          </div>
          
          <MaintenanceMessage 
            estimatedDuration="ساعتين / 2 hours"
            contactEmail="<EMAIL>"
            startTime="10:00 صباحاً / 10:00 AM"
            endTime="12:00 ظهراً / 12:00 PM"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <div className="text-center mb-8">
          <img
            src={logoSrc}
            alt="شُريح"
            className="logo-component mx-auto mb-4"
            style={{ height: "80px" }}
          />
          <h1 className="text-2xl font-bold">{t("login.title")}</h1>
          <p className="text-muted-foreground">لوحة تحكم المسؤول</p>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab} centered>
          <TabPane tab="تسجيل الدخول" key="login">
            {loginError && (
              <Alert
                message={loginError}
                type="error"
                showIcon
                className="mb-4"
                closable
                onClose={() => setLoginError(null)}
              />
            )}
            
            <Form
              name="login"
              initialValues={{ remember: true }}
              onFinish={handleLogin}
              layout="vertical"
            >
              <Form.Item
                name="email"
                label={t("login.email")}
                rules={[
                  { required: true, message: "الرجاء إدخال البريد الإلكتروني" },
                  { type: "email", message: "الرجاء إدخال بريد إلكتروني صالح" },
                ]}
              >
                <Input
                  prefix={<MailOutlined className="site-form-item-icon" />}
                  placeholder="<EMAIL>"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label={t("login.password")}
                rules={[
                  { required: true, message: "الرجاء إدخال كلمة المرور" },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="••••••••"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <div className="flex justify-between items-center">
                  <Form.Item name="remember" valuePropName="checked" noStyle>
                    <Checkbox>{t("login.rememberMe")}</Checkbox>
                  </Form.Item>

                  <Link to="/forgot-password" className="text-primary">
                    {t("login.forgotPassword")}
                  </Link>
                </div>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="w-full"
                  size="large"
                  loading={loginLoading}
                >
                  {t("login.loginButton")}
                </Button>
              </Form.Item>
              
              <Divider>أو تسجيل الدخول باستخدام</Divider>
              
              <div className="flex gap-4 justify-center">
                <Button 
                  icon={<GoogleOutlined />} 
                  size="large"
                  onClick={() => handleSocialLogin('google')}
                  loading={socialLoading === 'google'}
                >
                  جوجل
                </Button>
                <Button 
                  icon={<GithubOutlined />} 
                  size="large"
                  onClick={() => handleSocialLogin('github')}
                  loading={socialLoading === 'github'}
                >
                  جيثب
                </Button>
              </div>
            </Form>
          </TabPane>
          
          <TabPane tab="إنشاء حساب" key="register">
            {registerError && (
              <Alert
                message={registerError}
                type="error"
                showIcon
                className="mb-4"
                closable
                onClose={() => setRegisterError(null)}
              />
            )}
            
            <Form
              name="register"
              onFinish={handleRegister}
              layout="vertical"
            >
              <Form.Item
                name="fullName"
                label="الاسم الكامل"
                rules={[
                  { required: true, message: "الرجاء إدخال الاسم الكامل" },
                  { min: 3, message: "الاسم يجب أن يكون 3 أحرف على الأقل" }
                ]}
              >
                <Input
                  prefix={<UserOutlined className="site-form-item-icon" />}
                  placeholder="أحمد محمد"
                  size="large"
                />
              </Form.Item>
              
              <Form.Item
                name="email"
                label="البريد الإلكتروني"
                rules={[
                  { required: true, message: "الرجاء إدخال البريد الإلكتروني" },
                  { type: "email", message: "الرجاء إدخال بريد إلكتروني صالح" },
                ]}
              >
                <Input
                  prefix={<MailOutlined className="site-form-item-icon" />}
                  placeholder="<EMAIL>"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="كلمة المرور"
                rules={[
                  { required: true, message: "الرجاء إدخال كلمة المرور" },
                  { min: 8, message: "كلمة المرور يجب أن تكون 8 أحرف على الأقل" },
                  { 
                    pattern: /[A-Z]/, 
                    message: "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل" 
                  },
                  { 
                    pattern: /[0-9]/, 
                    message: "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل" 
                  },
                  { 
                    pattern: /[^A-Za-z0-9]/, 
                    message: "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل" 
                  }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="••••••••"
                  size="large"
                />
              </Form.Item>
              
              <Form.Item
                name="confirmPassword"
                label="تأكيد كلمة المرور"
                dependencies={['password']}
                rules={[
                  { required: true, message: "الرجاء تأكيد كلمة المرور" },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('كلمات المرور غير متطابقة'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="••••••••"
                  size="large"
                />
              </Form.Item>
              
              <Form.Item
                name="agreeTerms"
                valuePropName="checked"
                rules={[
                  { 
                    validator: (_, value) =>
                      value ? Promise.resolve() : Promise.reject(new Error('يجب الموافقة على الشروط والأحكام')),
                  },
                ]}
              >
                <Checkbox>
                  أوافق على <a href="#">الشروط والأحكام</a> و <a href="#">سياسة الخصوصية</a>
                </Checkbox>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="w-full"
                  size="large"
                  loading={registerLoading}
                >
                  إنشاء حساب
                </Button>
              </Form.Item>
              
              <Divider>أو إنشاء حساب باستخدام</Divider>
              
              <div className="flex gap-4 justify-center">
                <Button 
                  icon={<GoogleOutlined />} 
                  size="large"
                  onClick={() => handleSocialLogin('google')}
                  loading={socialLoading === 'google'}
                >
                  جوجل
                </Button>
                <Button 
                  icon={<GithubOutlined />} 
                  size="large"
                  onClick={() => handleSocialLogin('github')}
                  loading={socialLoading === 'github'}
                >
                  جيثب
                </Button>
              </div>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};
import { useState } from "react";
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Select,
  Button,
  InputNumber,
  Space,
  Divider,
  message,
  Upload,
  Typography,
} from "antd";
import {
  SettingOutlined,
  MessageOutlined,
  RobotOutlined,
  SafetyOutlined,
  BellOutlined,
  ApiOutlined,
  DatabaseOutlined,
  SaveOutlined,
  UndoOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";

const { TabPane } = Tabs;
const { Title, Paragraph } = Typography;

export const SettingsPage = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);

  // بيانات تجريبية للإعدادات
  const initialSettings = {
    general: {
      siteName: "شُريح - المساعد القانوني الذكي",
      siteDescription: "منصة ذكاء اصطناعي متخصصة في الاستشارات القانونية",
      defaultLanguage: "ar",
      maintenanceMode: false,
    },
    chat: {
      maxMessageLength: 2000,
      maxFileSize: 10,
      supportedFileTypes: ["pdf", "docx", "jpg", "png"],
      enableVoice: true,
      enableCitations: true,
    },
    ai: {
      defaultModel: "shuraih-v1",
      maxTokensPerChat: 10000,
      maxChatsPerUser: 50,
      responseTimeout: 30,
    },
    security: {
      sessionTimeout: 60,
      maxLoginAttempts: 5,
      enableTwoFactor: false,
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
      },
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      adminAlerts: true,
    },
    integration: {
      apiEnabled: true,
      webhookUrl: "",
      allowedDomains: ["*"],
    },
    backup: {
      autoBackup: true,
      backupFrequency: "daily",
      backupRetention: 30,
    },
  };

  const handleValuesChange = () => {
    setHasChanges(true);
  };

  const handleReset = () => {
    form.resetFields();
    setHasChanges(false);
  };

  const handleSubmit = (values: any) => {
    console.log("Submitted values:", values);
    message.success("تم حفظ الإعدادات بنجاح");
    setHasChanges(false);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("settings.title")}</h2>
        <Space>
          {hasChanges && (
            <>
              <Button
                icon={<UndoOutlined />}
                onClick={handleReset}
              >
                {t("settings.resetSettings")}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
              >
                {t("settings.saveSettings")}
              </Button>
            </>
          )}
        </Space>
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={initialSettings}
          onValuesChange={handleValuesChange}
          onFinish={handleSubmit}
        >
          <Tabs defaultActiveKey="general">
            <TabPane
              tab={
                <span>
                  <SettingOutlined /> {t("settings.generalSettings")}
                </span>
              }
              key="general"
            >
              <div className="mt-4">
                <Title level={4}>{t("settings.generalSettings")}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  الإعدادات العامة للنظام والموقع
                </Paragraph>

                <Form.Item
                  name={["general", "siteName"]}
                  label={t("settings.siteName")}
                  rules={[{ required: true, message: "الرجاء إدخال اسم الموقع" }]}
                >
                  <Input />
                </Form.Item>

                <Form.Item
                  name={["general", "siteDescription"]}
                  label={t("settings.siteDescription")}
                >
                  <Input.TextArea rows={3} />
                </Form.Item>

                <Form.Item
                  name={["general", "defaultLanguage"]}
                  label={t("settings.defaultLanguage")}
                >
                  <Select
                    options={[
                      { value: "ar", label: "العربية" },
                      { value: "en", label: "English" },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  name={["general", "maintenanceMode"]}
                  label={t("settings.maintenanceMode")}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name="logo"
                  label="شعار الموقع"
                >
                  <Upload
                    name="logo"
                    listType="picture"
                    maxCount={1}
                    beforeUpload={() => false}
                  >
                    <Button icon={<UploadOutlined />}>تحميل الشعار</Button>
                  </Upload>
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <MessageOutlined /> {t("settings.chatSettings")}
                </span>
              }
              key="chat"
            >
              <div className="mt-4">
                <Title level={4}>{t("settings.chatSettings")}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات المحادثة والرسائل
                </Paragraph>

                <Form.Item
                  name={["chat", "maxMessageLength"]}
                  label={t("settings.maxMessageLength")}
                  rules={[{ required: true, message: "الرجاء إدخال الحد الأقصى لطول الرسالة" }]}
                >
                  <InputNumber min={100} max={10000} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["chat", "maxFileSize"]}
                  label={t("settings.maxFileSize") + " (MB)"}
                  rules={[{ required: true, message: "الرجاء إدخال الحد الأقصى لحجم الملف" }]}
                >
                  <InputNumber min={1} max={100} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["chat", "supportedFileTypes"]}
                  label={t("settings.supportedFileTypes")}
                >
                  <Select
                    mode="tags"
                    style={{ width: "100%" }}
                    placeholder="أدخل أنواع الملفات المدعومة"
                  />
                </Form.Item>

                <Form.Item
                  name={["chat", "enableVoice"]}
                  label={t("settings.enableVoice")}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["chat", "enableCitations"]}
                  label={t("settings.enableCitations")}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <RobotOutlined /> {t("settings.aiSettings")}
                </span>
              }
              key="ai"
            >
              <div className="mt-4">
                <Title level={4}>{t("settings.aiSettings")}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات الذكاء الاصطناعي والنماذج
                </Paragraph>

                <Form.Item
                  name={["ai", "defaultModel"]}
                  label={t("settings.defaultModel")}
                >
                  <Select
                    options={[
                      { value: "shuraih-v1", label: "شُريح v1.0" },
                      { value: "shuraih-pro", label: "شُريح Pro" },
                      { value: "shuraih-turbo", label: "شُريح Turbo" },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  name={["ai", "maxTokensPerChat"]}
                  label={t("settings.maxTokensPerChat")}
                >
                  <InputNumber min={1000} max={100000} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["ai", "maxChatsPerUser"]}
                  label={t("settings.maxChatsPerUser")}
                >
                  <InputNumber min={5} max={1000} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["ai", "responseTimeout"]}
                  label={t("settings.responseTimeout") + " (ثانية)"}
                >
                  <InputNumber min={5} max={120} style={{ width: "100%" }} />
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <SafetyOutlined /> {t("settings.securitySettings")}
                </span>
              }
              key="security"
            >
              <div className="mt-4">
                <Title level={4}>{t("settings.securitySettings")}</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات الأمان والحماية
                </Paragraph>

                <Form.Item
                  name={["security", "sessionTimeout"]}
                  label={t("settings.sessionTimeout") + " (دقيقة)"}
                >
                  <InputNumber min={5} max={1440} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["security", "maxLoginAttempts"]}
                  label={t("settings.maxLoginAttempts")}
                >
                  <InputNumber min={1} max={10} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["security", "enableTwoFactor"]}
                  label={t("settings.enableTwoFactor")}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Divider orientation="left">سياسة كلمة المرور</Divider>

                <Form.Item
                  name={["security", "passwordPolicy", "minLength"]}
                  label="الحد الأدنى للطول"
                >
                  <InputNumber min={6} max={20} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item
                  name={["security", "passwordPolicy", "requireUppercase"]}
                  label="تتطلب حروف كبيرة"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["security", "passwordPolicy", "requireLowercase"]}
                  label="تتطلب حروف صغيرة"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["security", "passwordPolicy", "requireNumbers"]}
                  label="تتطلب أرقام"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["security", "passwordPolicy", "requireSpecialChars"]}
                  label="تتطلب رموز خاصة"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <BellOutlined /> إعدادات الإشعارات
                </span>
              }
              key="notifications"
            >
              <div className="mt-4">
                <Title level={4}>إعدادات الإشعارات</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات الإشعارات والتنبيهات
                </Paragraph>

                <Form.Item
                  name={["notifications", "emailNotifications"]}
                  label="إشعارات البريد الإلكتروني"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["notifications", "pushNotifications"]}
                  label="إشعارات الدفع"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["notifications", "adminAlerts"]}
                  label="تنبيهات المسؤول"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <ApiOutlined /> إعدادات التكامل
                </span>
              }
              key="integration"
            >
              <div className="mt-4">
                <Title level={4}>إعدادات التكامل</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات API والتكامل مع الأنظمة الأخرى
                </Paragraph>

                <Form.Item
                  name={["integration", "apiEnabled"]}
                  label="تمكين API"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["integration", "webhookUrl"]}
                  label="رابط Webhook"
                >
                  <Input placeholder="https://example.com/webhook" />
                </Form.Item>

                <Form.Item
                  name={["integration", "allowedDomains"]}
                  label="النطاقات المسموح بها"
                >
                  <Select
                    mode="tags"
                    style={{ width: "100%" }}
                    placeholder="أدخل النطاقات المسموح بها"
                  />
                </Form.Item>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <DatabaseOutlined /> إعدادات النسخ الاحتياطي
                </span>
              }
              key="backup"
            >
              <div className="mt-4">
                <Title level={4}>إعدادات النسخ الاحتياطي</Title>
                <Paragraph className="text-gray-500 mb-6">
                  إعدادات النسخ الاحتياطي واستعادة البيانات
                </Paragraph>

                <Form.Item
                  name={["backup", "autoBackup"]}
                  label="النسخ الاحتياطي التلقائي"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name={["backup", "backupFrequency"]}
                  label="تكرار النسخ الاحتياطي"
                >
                  <Select
                    options={[
                      { value: "hourly", label: "كل ساعة" },
                      { value: "daily", label: "يومي" },
                      { value: "weekly", label: "أسبوعي" },
                      { value: "monthly", label: "شهري" },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  name={["backup", "backupRetention"]}
                  label="فترة الاحتفاظ بالنسخ الاحتياطية (أيام)"
                >
                  <InputNumber min={1} max={365} style={{ width: "100%" }} />
                </Form.Item>

                <Divider />

                <Space>
                  <Button type="primary">
                    إنشاء نسخة احتياطية الآن
                  </Button>
                  <Button>
                    استعادة من نسخة احتياطية
                  </Button>
                </Space>
              </div>
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </div>
  );
};
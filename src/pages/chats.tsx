import { useState } from "react";
import {
  Table,
  Card,
  Space,
  Button,
  Tag,
  Dropdown,
  Menu,
  Input,
  Select,
  DatePicker,
  Modal,
  Drawer,
  List,
  Avatar,
  Typography,
} from "antd";
import {
  MessageOutlined,
  EyeOutlined,
  DeleteOutlined,
  FlagOutlined,
  InboxOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime, formatNumber } from "../lib/utils";

const { RangePicker } = DatePicker;
const { Text } = Typography;

export const ChatList = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isViewDrawerVisible, setIsViewDrawerVisible] = useState(false);
  const [selectedChat, setSelectedChat] = useState<any>(null);

  // بيانات تجريبية للمحادثات
  const chats = [
    {
      id: "1",
      title: "استشارة قانونية حول عقد إيجار",
      userId: "1",
      userName: "أحمد محمد",
      messageCount: 12,
      totalTokens: 3500,
      createdAt: new Date("2024-02-10"),
      lastActivity: new Date("2024-02-10"),
      status: "active",
      hasLegalCitations: true,
      averageResponseTime: 1.2,
      messages: [
        {
          id: "m1",
          sender: "user",
          text: "السلام عليكم، أريد استشارة حول عقد إيجار سكني",
          timestamp: new Date("2024-02-10T10:00:00"),
        },
        {
          id: "m2",
          sender: "bot",
          text: "وعليكم السلام، يسعدني مساعدتك. ما هي استفسارتك المحددة حول عقد الإيجار السكني؟",
          timestamp: new Date("2024-02-10T10:00:30"),
        },
        {
          id: "m3",
          sender: "user",
          text: "هل يحق للمالك زيادة قيمة الإيجار خلال فترة العقد؟",
          timestamp: new Date("2024-02-10T10:01:00"),
        },
        {
          id: "m4",
          sender: "bot",
          text: "وفقاً للمادة (11) من نظام إيجار العقارات، لا يحق للمالك زيادة الأجرة خلال مدة العقد ما لم يتم الاتفاق على ذلك صراحةً في العقد.",
          timestamp: new Date("2024-02-10T10:01:30"),
          hasCitations: true,
        },
      ],
    },
    {
      id: "2",
      title: "استفسار عن إجراءات التقاضي",
      userId: "2",
      userName: "سارة علي",
      messageCount: 8,
      totalTokens: 2200,
      createdAt: new Date("2024-02-15"),
      lastActivity: new Date("2024-02-15"),
      status: "active",
      hasLegalCitations: true,
      averageResponseTime: 0.9,
      messages: [],
    },
    {
      id: "3",
      title: "مشكلة في عقد عمل",
      userId: "3",
      userName: "محمد خالد",
      messageCount: 15,
      totalTokens: 4100,
      createdAt: new Date("2024-01-28"),
      lastActivity: new Date("2024-01-28"),
      status: "archived",
      hasLegalCitations: false,
      averageResponseTime: 1.5,
      messages: [],
    },
    {
      id: "4",
      title: "استشارة حول حقوق الملكية الفكرية",
      userId: "4",
      userName: "فاطمة أحمد",
      messageCount: 20,
      totalTokens: 5800,
      createdAt: new Date("2024-02-05"),
      lastActivity: new Date("2024-02-05"),
      status: "flagged",
      hasLegalCitations: true,
      averageResponseTime: 1.1,
      messages: [],
    },
    {
      id: "5",
      title: "سؤال عن الوكالات الشرعية",
      userId: "5",
      userName: "عبدالله محمد",
      messageCount: 6,
      totalTokens: 1800,
      createdAt: new Date("2024-02-18"),
      lastActivity: new Date("2024-02-18"),
      status: "active",
      hasLegalCitations: true,
      averageResponseTime: 0.8,
      messages: [],
    },
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case "active":
        return <Tag color="green">{t("chats.active")}</Tag>;
      case "archived":
        return <Tag color="gray">{t("chats.archived")}</Tag>;
      case "flagged":
        return <Tag color="red">{t("chats.flagged")}</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const handleView = (chat: any) => {
    setSelectedChat(chat);
    setIsViewDrawerVisible(true);
  };

  const handleDelete = (chat: any) => {
    setSelectedChat(chat);
    setIsDeleteModalVisible(true);
  };

  const handleArchive = (chat: any) => {
    // تنفيذ أرشفة المحادثة
    console.log("Archive chat:", chat);
  };

  const handleFlag = (chat: any) => {
    // تنفيذ الإبلاغ عن المحادثة
    console.log("Flag chat:", chat);
  };

  const columns = [
    {
      title: t("chats.chatTitle"),
      dataIndex: "title",
      key: "title",
    },
    {
      title: t("chats.user"),
      dataIndex: "userName",
      key: "userName",
    },
    {
      title: t("chats.messageCount"),
      dataIndex: "messageCount",
      key: "messageCount",
      render: (count: number) => formatNumber(count),
    },
    {
      title: t("chats.createdAt"),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: t("chats.lastActivity"),
      dataIndex: "lastActivity",
      key: "lastActivity",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: t("chats.status"),
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: t("chats.actions"),
      key: "actions",
      render: (_: any, record: any) => (
        <Dropdown
          menu={{
            items: [
              {
                key: "view",
                icon: <EyeOutlined />,
                label: t("chats.viewChat"),
                onClick: () => handleView(record),
              },
              {
                key: "archive",
                icon: <InboxOutlined />,
                label: t("chats.archiveChat"),
                onClick: () => handleArchive(record),
              },
              {
                key: "delete",
                icon: <DeleteOutlined />,
                label: t("chats.deleteChat"),
                danger: true,
                onClick: () => handleDelete(record),
              },
              {
                key: "flag",
                icon: <FlagOutlined />,
                label: t("chats.flagChat"),
                onClick: () => handleFlag(record),
              },
            ],
          }}
          trigger={["click"]}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // تصفية المحادثات بناءً على معايير البحث
  const filteredChats = chats.filter((chat) => {
    let matchesSearch = true;
    let matchesStatus = true;
    let matchesDateRange = true;

    if (searchText) {
      matchesSearch =
        chat.title.toLowerCase().includes(searchText.toLowerCase()) ||
        chat.userName.toLowerCase().includes(searchText.toLowerCase());
    }

    if (selectedStatus) {
      matchesStatus = chat.status === selectedStatus;
    }

    if (dateRange) {
      const [startDate, endDate] = dateRange;
      matchesDateRange =
        chat.createdAt >= startDate && chat.createdAt <= endDate;
    }

    return matchesSearch && matchesStatus && matchesDateRange;
  });

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("chats.title")}</h2>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <Input
            placeholder={t("common.search")}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder={t("chats.status")}
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedStatus(value)}
            options={[
              { value: "active", label: t("chats.active") },
              { value: "archived", label: t("chats.archived") },
              { value: "flagged", label: t("chats.flagged") },
            ]}
          />
          <RangePicker
            placeholder={[t("chats.createdAt"), ""]}
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]?.toDate() as Date, dates[1]?.toDate() as Date]);
              } else {
                setDateRange(null);
              }
            }}
          />
          <Button icon={<FilterOutlined />} onClick={() => {
            setSearchText("");
            setSelectedStatus(null);
            setDateRange(null);
          }}>
            إعادة تعيين
          </Button>
        </div>
      </Card>

      {/* جدول المحادثات */}
      <Table
        dataSource={filteredChats}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      {/* نافذة حذف المحادثة */}
      <Modal
        title={t("chats.deleteChat")}
        open={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
            {t("common.cancel")}
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={() => {
              // تنفيذ حذف المحادثة
              console.log("Delete chat:", selectedChat);
              setIsDeleteModalVisible(false);
            }}
          >
            {t("common.delete")}
          </Button>,
        ]}
      >
        <p>
          هل أنت متأكد من رغبتك في حذف المحادثة "{selectedChat?.title}"؟ هذا الإجراء لا يمكن التراجع عنه.
        </p>
      </Modal>

      {/* درج عرض المحادثة */}
      <Drawer
        title={selectedChat?.title}
        placement="right"
        width={600}
        onClose={() => setIsViewDrawerVisible(false)}
        open={isViewDrawerVisible}
        extra={
          <Space>
            <Button onClick={() => setIsViewDrawerVisible(false)}>
              {t("common.close")}
            </Button>
          </Space>
        }
      >
        {selectedChat && (
          <>
            <div className="mb-6">
              <Card size="small" title={t("chats.chatDetails")}>
                <p>
                  <strong>{t("chats.user")}:</strong> {selectedChat.userName}
                </p>
                <p>
                  <strong>{t("chats.messageCount")}:</strong>{" "}
                  {formatNumber(selectedChat.messageCount)}
                </p>
                <p>
                  <strong>{t("chats.totalTokens")}:</strong>{" "}
                  {formatNumber(selectedChat.totalTokens)}
                </p>
                <p>
                  <strong>{t("chats.createdAt")}:</strong>{" "}
                  {formatDateTime(selectedChat.createdAt)}
                </p>
                <p>
                  <strong>{t("chats.lastActivity")}:</strong>{" "}
                  {formatDateTime(selectedChat.lastActivity)}
                </p>
                <p>
                  <strong>{t("chats.status")}:</strong>{" "}
                  {getStatusTag(selectedChat.status)}
                </p>
                <p>
                  <strong>{t("chats.averageResponseTime")}:</strong>{" "}
                  {selectedChat.averageResponseTime}s
                </p>
                <p>
                  <strong>{t("chats.hasLegalCitations")}:</strong>{" "}
                  {selectedChat.hasLegalCitations ? "نعم" : "لا"}
                </p>
              </Card>
            </div>

            <div>
              <h3 className="mb-4">{t("chats.messages")}</h3>
              <List
                itemLayout="horizontal"
                dataSource={selectedChat.messages}
                renderItem={(message: any) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={
                            message.sender === "user" ? (
                              <UserOutlined />
                            ) : (
                              <RobotOutlined />
                            )
                          }
                          style={{
                            backgroundColor:
                              message.sender === "user" ? "#1890ff" : "#52c41a",
                          }}
                        />
                      }
                      title={
                        <div className="flex justify-between">
                          <span>
                            {message.sender === "user"
                              ? selectedChat.userName
                              : "شُريح"}
                          </span>
                          <Text type="secondary">
                            {formatDateTime(message.timestamp)}
                          </Text>
                        </div>
                      }
                      description={
                        <div>
                          <p>{message.text}</p>
                          {message.hasCitations && (
                            <Tag color="blue">يحتوي على استشهادات</Tag>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          </>
        )}
      </Drawer>
    </div>
  );
};
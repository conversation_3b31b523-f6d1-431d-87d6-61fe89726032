import { useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Upload,
  Modal,
  Form,
  Select,
  Tag,
  Progress,
  Typography,
  Tabs,
  List,
  Avatar,
  Statistic,
  Row,
  Col,
} from "antd";
import {
  UploadOutlined,
  FileTextOutlined,
  SearchOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  BranchesOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime, formatNumber } from "../lib/utils";

const { TextArea } = Input;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

export const RAGManagementPage = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("documents");
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const [isProcessingModalVisible, setIsProcessingModalVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [searchText, setSearchText] = useState("");

  // بيانات تجريبية للمستندات
  const documents = [
    {
      id: "doc_1",
      name: "نظام العمل السعودي.pdf",
      type: "pdf",
      size: 2.5,
      status: "processed",
      uploadDate: new Date("2024-02-15"),
      processedDate: new Date("2024-02-15"),
      chunks: 145,
      vectors: 145,
      category: "قوانين العمل",
      language: "ar",
    },
    {
      id: "doc_2",
      name: "نظام الشركات.docx",
      type: "docx",
      size: 1.8,
      status: "processing",
      uploadDate: new Date("2024-02-20"),
      processedDate: null,
      chunks: 0,
      vectors: 0,
      category: "قوانين الشركات",
      language: "ar",
    },
    {
      id: "doc_3",
      name: "نظام المرافعات الشرعية.pdf",
      type: "pdf",
      size: 3.2,
      status: "processed",
      uploadDate: new Date("2024-02-10"),
      processedDate: new Date("2024-02-10"),
      chunks: 198,
      vectors: 198,
      category: "المرافعات",
      language: "ar",
    },
    {
      id: "doc_4",
      name: "نظام الإيجار.pdf",
      type: "pdf",
      size: 1.2,
      status: "failed",
      uploadDate: new Date("2024-02-18"),
      processedDate: null,
      chunks: 0,
      vectors: 0,
      category: "العقارات",
      language: "ar",
      error: "فشل في استخراج النص من المستند",
    },
  ];

  // بيانات تجريبية للمتجهات
  const vectorStats = {
    totalVectors: 343,
    totalDocuments: 3,
    averageChunkSize: 512,
    indexSize: 2.1, // GB
    lastUpdate: new Date("2024-02-20"),
  };

  // بيانات تجريبية لاستعلامات البحث
  const searchQueries = [
    {
      id: "q_1",
      query: "ما هي شروط فسخ عقد العمل؟",
      timestamp: new Date("2024-02-20T10:30:00"),
      results: 5,
      responseTime: 0.8,
      sources: ["نظام العمل السعودي.pdf"],
    },
    {
      id: "q_2",
      query: "كيفية تأسيس شركة ذات مسؤولية محدودة؟",
      timestamp: new Date("2024-02-20T09:15:00"),
      results: 3,
      responseTime: 1.2,
      sources: ["نظام الشركات.docx"],
    },
    {
      id: "q_3",
      query: "إجراءات رفع دعوى أمام المحكمة؟",
      timestamp: new Date("2024-02-19T16:45:00"),
      results: 7,
      responseTime: 0.6,
      sources: ["نظام المرافعات الشرعية.pdf"],
    },
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case "processed":
        return <Tag color="green" icon={<CheckCircleOutlined />}>تم المعالجة</Tag>;
      case "processing":
        return <Tag color="blue" icon={<ClockCircleOutlined />}>قيد المعالجة</Tag>;
      case "failed":
        return <Tag color="red">فشل</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const handleUpload = () => {
    setIsUploadModalVisible(true);
  };

  const handleReprocess = (document: any) => {
    console.log("Reprocessing document:", document);
    setSelectedDocument(document);
    setIsProcessingModalVisible(true);
  };

  const handleDelete = (document: any) => {
    Modal.confirm({
      title: "حذف المستند",
      content: `هل أنت متأكد من رغبتك في حذف "${document.name}"؟ سيتم حذف جميع المتجهات المرتبطة به.`,
      okText: "حذف",
      okType: "danger",
      cancelText: "إلغاء",
      onOk() {
        console.log("Delete document:", document);
      },
    });
  };

  const documentsColumns = [
    {
      title: "اسم المستند",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: any) => (
        <div className="flex items-center gap-2">
          <FileTextOutlined className="text-blue-500" />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-gray-500">
              {record.category} • {record.size} MB
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "الحالة",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: "المقاطع",
      dataIndex: "chunks",
      key: "chunks",
      render: (chunks: number) => formatNumber(chunks),
    },
    {
      title: "المتجهات",
      dataIndex: "vectors",
      key: "vectors",
      render: (vectors: number) => formatNumber(vectors),
    },
    {
      title: "تاريخ الرفع",
      dataIndex: "uploadDate",
      key: "uploadDate",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: "الإجراءات",
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            title="عرض التفاصيل"
          />
          {record.status === "failed" && (
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={() => handleReprocess(record)}
              title="إعادة المعالجة"
            />
          )}
          <Button
            type="text"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record)}
            title="حذف"
          />
        </Space>
      ),
    },
  ];

  const queriesColumns = [
    {
      title: "الاستعلام",
      dataIndex: "query",
      key: "query",
      width: "40%",
    },
    {
      title: "النتائج",
      dataIndex: "results",
      key: "results",
      render: (results: number) => formatNumber(results),
    },
    {
      title: "وقت الاستجابة",
      dataIndex: "responseTime",
      key: "responseTime",
      render: (time: number) => `${time}s`,
    },
    {
      title: "المصادر",
      dataIndex: "sources",
      key: "sources",
      render: (sources: string[]) => (
        <div>
          {sources.map((source, index) => (
            <Tag key={index} className="mb-1">{source}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: "التوقيت",
      dataIndex: "timestamp",
      key: "timestamp",
      render: (date: Date) => formatDateTime(date),
    },
  ];

  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(searchText.toLowerCase()) ||
    doc.category.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">إدارة نظام RAG</h2>
        <Button
          type="primary"
          icon={<UploadOutlined />}
          onClick={handleUpload}
        >
          رفع مستند جديد
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المتجهات"
              value={vectorStats.totalVectors}
              prefix={<BranchesOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="المستندات المعالجة"
              value={vectorStats.totalDocuments}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="حجم الفهرس"
              value={vectorStats.indexSize}
              suffix="GB"
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="متوسط حجم المقطع"
              value={vectorStats.averageChunkSize}
              suffix="حرف"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="المستندات" key="documents">
            <div className="mb-4">
              <Input
                placeholder="البحث في المستندات..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 300 }}
              />
            </div>
            
            <Table
              dataSource={filteredDocuments}
              columns={documentsColumns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="استعلامات البحث" key="queries">
            <Table
              dataSource={searchQueries}
              columns={queriesColumns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="إحصائيات المتجهات" key="vectors">
            <div className="space-y-6">
              <Card title="معلومات الفهرس">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Text strong>إجمالي المتجهات:</Text>
                    <div className="text-2xl font-bold text-blue-600">
                      {formatNumber(vectorStats.totalVectors)}
                    </div>
                  </div>
                  <div>
                    <Text strong>حجم الفهرس:</Text>
                    <div className="text-2xl font-bold text-green-600">
                      {vectorStats.indexSize} GB
                    </div>
                  </div>
                  <div>
                    <Text strong>متوسط حجم المقطع:</Text>
                    <div className="text-lg">
                      {vectorStats.averageChunkSize} حرف
                    </div>
                  </div>
                  <div>
                    <Text strong>آخر تحديث:</Text>
                    <div className="text-lg">
                      {formatDateTime(vectorStats.lastUpdate)}
                    </div>
                  </div>
                </div>
              </Card>

              <Card title="توزيع المتجهات حسب الفئة">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span>قوانين العمل</span>
                      <span>145 متجه</span>
                    </div>
                    <Progress percent={42} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span>المرافعات</span>
                      <span>198 متجه</span>
                    </div>
                    <Progress percent={58} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span>قوانين الشركات</span>
                      <span>0 متجه</span>
                    </div>
                    <Progress percent={0} />
                  </div>
                </div>
              </Card>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* نافذة رفع مستند جديد */}
      <Modal
        title="رفع مستند جديد"
        open={isUploadModalVisible}
        onCancel={() => setIsUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item
            name="files"
            label="المستندات"
            rules={[{ required: true, message: "الرجاء اختيار ملف واحد على الأقل" }]}
          >
            <Upload.Dragger
              multiple
              accept=".pdf,.docx,.txt,.md"
              beforeUpload={() => false}
              maxCount={5}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">
                انقر أو اسحب الملفات إلى هذه المنطقة للرفع
              </p>
              <p className="ant-upload-hint">
                يدعم PDF، DOCX، TXT، MD. الحد الأقصى 5 ملفات.
              </p>
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            name="category"
            label="الفئة"
            rules={[{ required: true, message: "الرجاء اختيار فئة" }]}
          >
            <Select
              placeholder="اختر فئة"
              options={[
                { value: "قوانين العمل", label: "قوانين العمل" },
                { value: "قوانين الشركات", label: "قوانين الشركات" },
                { value: "المرافعات", label: "المرافعات" },
                { value: "العقارات", label: "العقارات" },
                { value: "الأحوال الشخصية", label: "الأحوال الشخصية" },
              ]}
            />
          </Form.Item>

          <Form.Item
            name="language"
            label="اللغة"
            initialValue="ar"
          >
            <Select
              options={[
                { value: "ar", label: "العربية" },
                { value: "en", label: "الإنجليزية" },
              ]}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="وصف (اختياري)"
          >
            <TextArea rows={3} placeholder="أدخل وصفاً للمستندات..." />
          </Form.Item>

          <Form.Item
            name="chunkSize"
            label="حجم المقطع"
            initialValue={512}
          >
            <Select
              options={[
                { value: 256, label: "صغير (256 حرف)" },
                { value: 512, label: "متوسط (512 حرف)" },
                { value: 1024, label: "كبير (1024 حرف)" },
              ]}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                رفع ومعالجة
              </Button>
              <Button onClick={() => setIsUploadModalVisible(false)}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة معالجة المستند */}
      <Modal
        title="إعادة معالجة المستند"
        open={isProcessingModalVisible}
        onCancel={() => setIsProcessingModalVisible(false)}
        footer={null}
      >
        {selectedDocument && (
          <div>
            <div className="mb-4">
              <Text strong>المستند:</Text> {selectedDocument.name}
            </div>
            <div className="mb-4">
              <Text strong>الخطأ السابق:</Text> {selectedDocument.error}
            </div>

            <Form layout="vertical">
              <Form.Item
                name="chunkSize"
                label="حجم المقطع"
                initialValue={512}
              >
                <Select
                  options={[
                    { value: 256, label: "صغير (256 حرف)" },
                    { value: 512, label: "متوسط (512 حرف)" },
                    { value: 1024, label: "كبير (1024 حرف)" },
                  ]}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    إعادة المعالجة
                  </Button>
                  <Button onClick={() => setIsProcessingModalVisible(false)}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};
import { Card, Col, Row, Statistic, Table, Typography, Space, Tag } from "antd";
import {
  UserOutlined,
  MessageOutlined,
  KeyOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatNumber, formatDateTime } from "../lib/utils";

const { Title } = Typography;

export const Dashboard = () => {
  const { t } = useTranslation();

  // بيانات تجريبية للإحصائيات
  const stats = {
    totalUsers: 1250,
    activeUsers: 780,
    totalChats: 5430,
    totalTokens: 1250000,
    responseTime: "0.8s",
    errorRate: "0.5%",
    uptime: "99.9%",
  };

  // بيانات تجريبية للنشاط الأخير
  const recentActivity = [
    {
      key: "1",
      user: "أحمد محمد",
      action: "محادثة جديدة",
      time: new Date(Date.now() - 5 * 60000),
      status: "success",
    },
    {
      key: "2",
      user: "سارة علي",
      action: "تسجيل دخول",
      time: new Date(Date.now() - 15 * 60000),
      status: "success",
    },
    {
      key: "3",
      user: "محمد خالد",
      action: "تحديث ملف شخصي",
      time: new Date(Date.now() - 45 * 60000),
      status: "info",
    },
    {
      key: "4",
      user: "فاطمة أحمد",
      action: "محاولة تسجيل دخول فاشلة",
      time: new Date(Date.now() - 120 * 60000),
      status: "error",
    },
    {
      key: "5",
      user: "عبدالله محمد",
      action: "اشتراك جديد",
      time: new Date(Date.now() - 180 * 60000),
      status: "success",
    },
  ];

  const activityColumns = [
    {
      title: t("users.name"),
      dataIndex: "user",
      key: "user",
    },
    {
      title: "الإجراء",
      dataIndex: "action",
      key: "action",
    },
    {
      title: "الوقت",
      dataIndex: "time",
      key: "time",
      render: (time: Date) => formatDateTime(time),
    },
    {
      title: "الحالة",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        let color = "green";
        if (status === "error") color = "red";
        if (status === "info") color = "blue";
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
    },
  ];

  return (
    <div className="p-6">
      <Title level={2} className="mb-6">
        {t("dashboard.title")}
      </Title>

      {/* بطاقات الإحصائيات */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalUsers")}
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.activeUsers")}
              value={stats.activeUsers}
              prefix={<UserOutlined />}
              formatter={(value) => formatNumber(value as number)}
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalChats")}
              value={stats.totalChats}
              prefix={<MessageOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("dashboard.totalTokens")}
              value={stats.totalTokens}
              prefix={<KeyOutlined />}
              formatter={(value) => formatNumber(value as number)}
            />
          </Card>
        </Col>
      </Row>

      {/* بطاقات الأداء */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title={t("dashboard.responseTime")}
              value={stats.responseTime}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title={t("dashboard.errorRate")}
              value={stats.errorRate}
              valueStyle={{ color: "#cf1322" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title={t("dashboard.uptime")}
              value={stats.uptime}
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
      </Row>

      {/* النشاط الأخير */}
      <Card
        title={
          <Space>
            <span>{t("dashboard.recentActivity")}</span>
          </Space>
        }
        extra={<a href="#">{t("dashboard.viewAll")}</a>}
      >
        <Table
          dataSource={recentActivity}
          columns={activityColumns}
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );
};
import { useState } from "react";
import {
  List,
  Table,
  Card,
  Space,
  Button,
  Tag,
  Dropdown,
  Menu,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
} from "antd";
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime } from "../lib/utils";

const { RangePicker } = DatePicker;

export const UserList = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);

  // بيانات تجريبية للمستخدمين مع الأدوار الصحيحة
  const users = [
    {
      id: "1",
      name: "أحمد محمد",
      email: "<EMAIL>",
      status: "active",
      role: "administrator", // مسؤول
      joinDate: new Date("2023-01-15"),
      lastActive: new Date("2024-02-20"),
      chatCount: 45,
      totalTokens: 12500,
      subscriptionTier: "enterprise",
    },
    {
      id: "2",
      name: "سارة علي",
      email: "<EMAIL>",
      status: "active",
      role: "moderator", // مشرف
      joinDate: new Date("2023-03-10"),
      lastActive: new Date("2024-02-19"),
      chatCount: 23,
      totalTokens: 8700,
      subscriptionTier: "premium",
    },
    {
      id: "3",
      name: "محمد خالد",
      email: "<EMAIL>",
      status: "inactive",
      role: "subscriber_premium", // مشترك متقدم
      joinDate: new Date("2023-05-22"),
      lastActive: new Date("2023-12-05"),
      chatCount: 12,
      totalTokens: 4300,
      subscriptionTier: "premium",
    },
    {
      id: "4",
      name: "فاطمة أحمد",
      email: "<EMAIL>",
      status: "suspended",
      role: "subscriber_basic", // مشترك أساسي
      joinDate: new Date("2023-02-18"),
      lastActive: new Date("2024-01-30"),
      chatCount: 67,
      totalTokens: 18900,
      subscriptionTier: "basic",
    },
    {
      id: "5",
      name: "عبدالله محمد",
      email: "<EMAIL>",
      status: "active",
      role: "user", // مستخدم عادي
      joinDate: new Date("2023-07-05"),
      lastActive: new Date("2024-02-21"),
      chatCount: 34,
      totalTokens: 9800,
      subscriptionTier: "free",
    },
    {
      id: "6",
      name: "خالد العتيبي",
      email: "<EMAIL>",
      status: "active",
      role: "subscriber_enterprise", // مشترك مؤسسي
      joinDate: new Date("2023-08-12"),
      lastActive: new Date("2024-02-21"),
      chatCount: 156,
      totalTokens: 45600,
      subscriptionTier: "enterprise",
    },
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case "active":
        return <Tag color="green">{t("users.active")}</Tag>;
      case "inactive":
        return <Tag color="gray">{t("users.inactive")}</Tag>;
      case "suspended":
        return <Tag color="red">{t("users.suspended")}</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getRoleTag = (role: string) => {
    switch (role) {
      case "administrator":
        return <Tag color="red">مسؤول</Tag>;
      case "moderator":
        return <Tag color="orange">مشرف</Tag>;
      case "subscriber_enterprise":
        return <Tag color="gold">مشترك مؤسسي</Tag>;
      case "subscriber_premium":
        return <Tag color="purple">مشترك متقدم</Tag>;
      case "subscriber_basic":
        return <Tag color="blue">مشترك أساسي</Tag>;
      case "user":
        return <Tag color="default">مستخدم عادي</Tag>;
      default:
        return <Tag>{role}</Tag>;
    }
  };

  const getSubscriptionTierTag = (tier: string) => {
    switch (tier) {
      case "enterprise":
        return <Tag color="gold">مؤسسي</Tag>;
      case "premium":
        return <Tag color="purple">متقدم</Tag>;
      case "basic":
        return <Tag color="blue">أساسي</Tag>;
      case "free":
        return <Tag color="default">مجاني</Tag>;
      default:
        return <Tag>{tier}</Tag>;
    }
  };

  const handleEdit = (user: any) => {
    setSelectedUser(user);
    setIsEditModalVisible(true);
  };

  const handleDelete = (user: any) => {
    setSelectedUser(user);
    setIsDeleteModalVisible(true);
  };

  const handleSuspend = (user: any) => {
    // تنفيذ تعليق المستخدم
    console.log("Suspend user:", user);
  };

  const handleActivate = (user: any) => {
    // تنفيذ تفعيل المستخدم
    console.log("Activate user:", user);
  };

  const columns = [
    {
      title: t("users.name"),
      dataIndex: "name",
      key: "name",
      render: (name: string, record: any) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: "الدور",
      dataIndex: "role",
      key: "role",
      render: (role: string) => getRoleTag(role),
    },
    {
      title: "مستوى الاشتراك",
      dataIndex: "subscriptionTier",
      key: "subscriptionTier",
      render: (tier: string) => getSubscriptionTierTag(tier),
    },
    {
      title: t("users.status"),
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
    {
      title: t("users.joinDate"),
      dataIndex: "joinDate",
      key: "joinDate",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: t("users.lastActive"),
      dataIndex: "lastActive",
      key: "lastActive",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: "عدد المحادثات",
      dataIndex: "chatCount",
      key: "chatCount",
    },
    {
      title: t("users.actions"),
      key: "actions",
      render: (_: any, record: any) => (
        <Dropdown
          menu={{
            items: [
              {
                key: "edit",
                icon: <EditOutlined />,
                label: t("users.editUser"),
                onClick: () => handleEdit(record),
              },
              {
                key: "delete",
                icon: <DeleteOutlined />,
                label: t("users.deleteUser"),
                danger: true,
                onClick: () => handleDelete(record),
              },
              {
                type: "divider",
              },
              record.status === "suspended"
                ? {
                    key: "activate",
                    icon: <UnlockOutlined />,
                    label: t("users.activateUser"),
                    onClick: () => handleActivate(record),
                  }
                : {
                    key: "suspend",
                    icon: <LockOutlined />,
                    label: t("users.suspendUser"),
                    onClick: () => handleSuspend(record),
                  },
            ],
          }}
          trigger={["click"]}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // تصفية المستخدمين بناءً على معايير البحث
  const filteredUsers = users.filter((user) => {
    let matchesSearch = true;
    let matchesStatus = true;
    let matchesRole = true;
    let matchesDateRange = true;

    if (searchText) {
      matchesSearch =
        user.name.toLowerCase().includes(searchText.toLowerCase()) ||
        user.email.toLowerCase().includes(searchText.toLowerCase());
    }

    if (selectedStatus) {
      matchesStatus = user.status === selectedStatus;
    }

    if (selectedRole) {
      matchesRole = user.role === selectedRole;
    }

    if (dateRange) {
      const [startDate, endDate] = dateRange;
      matchesDateRange =
        user.joinDate >= startDate && user.joinDate <= endDate;
    }

    return matchesSearch && matchesStatus && matchesRole && matchesDateRange;
  });

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("users.title")}</h2>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsCreateModalVisible(true)}
        >
          {t("users.createUser")}
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {users.filter(u => u.role === "administrator").length}
            </div>
            <div className="text-sm text-gray-500">مسؤولين</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {users.filter(u => u.role === "moderator").length}
            </div>
            <div className="text-sm text-gray-500">مشرفين</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-gold-600">
              {users.filter(u => u.role === "subscriber_enterprise").length}
            </div>
            <div className="text-sm text-gray-500">مشتركين مؤسسي</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {users.filter(u => u.role === "subscriber_premium").length}
            </div>
            <div className="text-sm text-gray-500">مشتركين متقدم</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {users.filter(u => u.role === "subscriber_basic").length}
            </div>
            <div className="text-sm text-gray-500">مشتركين أساسي</div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {users.filter(u => u.role === "user").length}
            </div>
            <div className="text-sm text-gray-500">مستخدمين عاديين</div>
          </div>
        </Card>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <Input
            placeholder={t("common.search")}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder={t("users.status")}
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedStatus(value)}
            options={[
              { value: "active", label: t("users.active") },
              { value: "inactive", label: t("users.inactive") },
              { value: "suspended", label: t("users.suspended") },
            ]}
          />
          <Select
            placeholder="الدور"
            allowClear
            style={{ width: 180 }}
            onChange={(value) => setSelectedRole(value)}
            options={[
              { value: "administrator", label: "مسؤول" },
              { value: "moderator", label: "مشرف" },
              { value: "subscriber_enterprise", label: "مشترك مؤسسي" },
              { value: "subscriber_premium", label: "مشترك متقدم" },
              { value: "subscriber_basic", label: "مشترك أساسي" },
              { value: "user", label: "مستخدم عادي" },
            ]}
          />
          <RangePicker
            placeholder={[t("users.joinDate"), ""]}
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]?.toDate() as Date, dates[1]?.toDate() as Date]);
              } else {
                setDateRange(null);
              }
            }}
          />
          <Button icon={<FilterOutlined />} onClick={() => {
            setSearchText("");
            setSelectedStatus(null);
            setSelectedRole(null);
            setDateRange(null);
          }}>
            إعادة تعيين
          </Button>
        </div>
      </Card>

      {/* جدول المستخدمين */}
      <Table
        dataSource={filteredUsers}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      {/* نافذة إنشاء مستخدم جديد */}
      <Modal
        title={t("users.createUser")}
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
      >
        <Form layout="vertical">
          <Form.Item
            name="name"
            label={t("users.name")}
            rules={[{ required: true, message: "الرجاء إدخال الاسم" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label={t("users.email")}
            rules={[
              { required: true, message: "الرجاء إدخال البريد الإلكتروني" },
              { type: "email", message: "البريد الإلكتروني غير صالح" },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="الدور"
            rules={[{ required: true, message: "الرجاء اختيار الدور" }]}
          >
            <Select
              options={[
                { value: "administrator", label: "مسؤول" },
                { value: "moderator", label: "مشرف" },
                { value: "subscriber_enterprise", label: "مشترك مؤسسي" },
                { value: "subscriber_premium", label: "مشترك متقدم" },
                { value: "subscriber_basic", label: "مشترك أساسي" },
                { value: "user", label: "مستخدم عادي" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="subscriptionTier"
            label="مستوى الاشتراك"
            rules={[{ required: true, message: "الرجاء اختيار مستوى الاشتراك" }]}
          >
            <Select
              options={[
                { value: "enterprise", label: "مؤسسي" },
                { value: "premium", label: "متقدم" },
                { value: "basic", label: "أساسي" },
                { value: "free", label: "مجاني" },
              ]}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t("common.create")}
              </Button>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                {t("common.cancel")}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة تعديل المستخدم */}
      <Modal
        title={t("users.editUser")}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
      >
        {selectedUser && (
          <Form layout="vertical" initialValues={selectedUser}>
            <Form.Item
              name="name"
              label={t("users.name")}
              rules={[{ required: true, message: "الرجاء إدخال الاسم" }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="email"
              label={t("users.email")}
              rules={[
                { required: true, message: "الرجاء إدخال البريد الإلكتروني" },
                { type: "email", message: "البريد الإلكتروني غير صالح" },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="role"
              label="الدور"
              rules={[{ required: true, message: "الرجاء اختيار الدور" }]}
            >
              <Select
                options={[
                  { value: "administrator", label: "مسؤول" },
                  { value: "moderator", label: "مشرف" },
                  { value: "subscriber_enterprise", label: "مشترك مؤسسي" },
                  { value: "subscriber_premium", label: "مشترك متقدم" },
                  { value: "subscriber_basic", label: "مشترك أساسي" },
                  { value: "user", label: "مستخدم عادي" },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="subscriptionTier"
              label="مستوى الاشتراك"
              rules={[{ required: true, message: "الرجاء اختيار مستوى الاشتراك" }]}
            >
              <Select
                options={[
                  { value: "enterprise", label: "مؤسسي" },
                  { value: "premium", label: "متقدم" },
                  { value: "basic", label: "أساسي" },
                  { value: "free", label: "مجاني" },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="status"
              label={t("users.status")}
              rules={[{ required: true, message: "الرجاء اختيار الحالة" }]}
            >
              <Select
                options={[
                  { value: "active", label: t("users.active") },
                  { value: "inactive", label: t("users.inactive") },
                  { value: "suspended", label: t("users.suspended") },
                ]}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {t("common.save")}
                </Button>
                <Button onClick={() => setIsEditModalVisible(false)}>
                  {t("common.cancel")}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* نافذة حذف المستخدم */}
      <Modal
        title={t("users.deleteUser")}
        open={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
            {t("common.cancel")}
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={() => {
              // تنفيذ حذف المستخدم
              console.log("Delete user:", selectedUser);
              setIsDeleteModalVisible(false);
            }}
          >
            {t("common.delete")}
          </Button>,
        ]}
      >
        <p>
          هل أنت متأكد من رغبتك في حذف المستخدم "{selectedUser?.name}"؟ هذا الإجراء لا يمكن التراجع عنه.
        </p>
      </Modal>
    </div>
  );
};
import { Button, Result } from "antd";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/auth-context";

export const UnauthorizedPage = () => {
  const { profile } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Result
        status="403"
        title="غير مصرح"
        subTitle="عذراً، أنت غير مصرح لك بالوصول إلى هذه الصفحة."
        extra={
          <div className="space-y-4">
            <p>
              دورك الحالي: {profile?.role === 'administrator' ? 'مسؤول' :
                          profile?.role === 'moderator' ? 'مشرف' :
                          profile?.role === 'subscriber_enterprise' ? 'مشترك مؤسسي' :
                          profile?.role === 'subscriber_premium' ? 'مشترك متقدم' :
                          profile?.role === 'subscriber_basic' ? 'مشترك أساسي' :
                          'مستخدم عادي'}
            </p>
            <div>
              <Link to="/">
                <Button type="primary">العودة للرئيسية</Button>
              </Link>
            </div>
          </div>
        }
      />
    </div>
  );
};
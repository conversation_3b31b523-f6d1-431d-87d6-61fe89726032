import React, { useState, useEffect } from "react";
import {
  useTable,
  useForm,
  useSelect,
  useShow,
  useCreate,
  useUpdate,
  useDelete,
  useNavigation,
  useList,
} from "@refinedev/core";
import {
  List,
  Table,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Card,
  Space,
  Tag,
  Popconfirm,
  Modal,
  Typography,
  Tabs,
  Drawer,
  Spin,
  message,
  Alert,
} from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ApiOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  CodeOutlined,
  KeyOutlined,
  SettingOutlined,
  LinkOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import JSONInput from 'react-json-editor-ajrm';
import locale from 'react-json-editor-ajrm/locale/en';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

export const LLMServicesPage = () => {
  const { t, language } = useTranslation();
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isViewDrawerVisible, setIsViewDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [configJson, setConfigJson] = useState<any>({});
  const [activeTab, setActiveTab] = useState("llm");
  const [syncWithAISettings, setSyncWithAISettings] = useState(true);

  // Fetch AI settings to check if we need to update them
  const { data: aiSettingsData, refetch: refetchAISettings } = useList({
    resource: "ai_settings",
  });

  const { tableProps, sorters, filters, setFilters } = useTable({
    resource: "external_apis",
    filters: {
      initial: [
        {
          field: "type",
          operator: "eq",
          value: activeTab,
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: "plan_level",
          order: "asc",
        },
      ],
    },
  });

  const { formProps: createFormProps, saveButtonProps: createSaveButtonProps, onFinish: onCreateFinish } = useForm({
    resource: "external_apis",
    action: "create",
    onMutationSuccess: () => {
      setIsCreateModalVisible(false);
      message.success(language === "ar" ? "تم إنشاء واجهة API بنجاح" : "API created successfully");
      
      // Refresh AI settings if sync is enabled
      if (syncWithAISettings) {
        refetchAISettings();
      }
    },
  });

  const { formProps: editFormProps, saveButtonProps: editSaveButtonProps, onFinish: onEditFinish } = useForm({
    resource: "external_apis",
    action: "edit",
    id: selectedRecord?.id,
    onMutationSuccess: () => {
      setIsEditModalVisible(false);
      message.success(language === "ar" ? "تم تحديث واجهة API بنجاح" : "API updated successfully");
      
      // Refresh AI settings if sync is enabled
      if (syncWithAISettings) {
        refetchAISettings();
      }
    },
  });

  const { queryResult: showQueryResult } = useShow({
    resource: "external_apis",
    id: selectedRecord?.id,
  });

  const { mutate: deleteApi } = useDelete();

  const { selectProps: typeSelectProps } = useSelect({
    resource: "external_apis",
    optionLabel: "type",
    optionValue: "type",
    defaultValue: "llm",
    sorters: [
      {
        field: "type",
        order: "asc",
      },
    ],
  });

  const { selectProps: planLevelSelectProps } = useSelect({
    resource: "external_apis",
    optionLabel: "plan_level",
    optionValue: "plan_level",
    defaultValue: "free",
    sorters: [
      {
        field: "plan_level",
        order: "asc",
      },
    ],
  });

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setFilters([
      {
        field: "type",
        operator: "eq",
        value: key,
      },
    ]);
  };

  const handleCreate = () => {
    setConfigJson({});
    setIsCreateModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setSelectedRecord(record);
    setConfigJson(record.config || {});
    setIsEditModalVisible(true);
  };

  const handleView = (record: any) => {
    setSelectedRecord(record);
    setIsViewDrawerVisible(true);
  };

  const handleDelete = (record: any) => {
    deleteApi({
      resource: "external_apis",
      id: record.id,
      successNotification: {
        message: language === "ar" ? "تم حذف واجهة API بنجاح" : "API deleted successfully",
        type: "success",
      },
      onSuccess: () => {
        // Refresh AI settings if sync is enabled
        if (syncWithAISettings) {
          refetchAISettings();
        }
      }
    });
  };

  const handleToggleActive = (record: any) => {
    const { mutate } = useUpdate();
    mutate({
      resource: "external_apis",
      id: record.id,
      values: {
        is_active: !record.is_active,
      },
      successNotification: {
        message: record.is_active
          ? language === "ar" ? "تم تعطيل واجهة API بنجاح" : "API disabled successfully"
          : language === "ar" ? "تم تفعيل واجهة API بنجاح" : "API enabled successfully",
        type: "success",
      },
      onSuccess: () => {
        // Refresh AI settings if sync is enabled
        if (syncWithAISettings) {
          refetchAISettings();
        }
      }
    });
  };

  const getTypeTag = (type: string) => {
    switch (type) {
      case "llm":
        return <Tag color="blue">{language === "ar" ? "نموذج لغوي" : "LLM"}</Tag>;
      case "speech":
        return <Tag color="green">{language === "ar" ? "صوت" : "Speech"}</Tag>;
      case "vision":
        return <Tag color="purple">{language === "ar" ? "رؤية" : "Vision"}</Tag>;
      case "analysis":
        return <Tag color="orange">{language === "ar" ? "تحليل" : "Analysis"}</Tag>;
      case "translation":
        return <Tag color="cyan">{language === "ar" ? "ترجمة" : "Translation"}</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };

  const getPlanLevelTag = (planLevel: string) => {
    switch (planLevel) {
      case "free":
        return <Tag color="default">{language === "ar" ? "مجاني" : "Free"}</Tag>;
      case "basic":
        return <Tag color="blue">{language === "ar" ? "أساسي" : "Basic"}</Tag>;
      case "premium":
        return <Tag color="purple">{language === "ar" ? "متقدم" : "Premium"}</Tag>;
      case "enterprise":
        return <Tag color="gold">{language === "ar" ? "مؤسسي" : "Enterprise"}</Tag>;
      default:
        return <Tag>{planLevel}</Tag>;
    }
  };

  const columns = [
    {
      title: language === "ar" ? "الاسم" : "Name",
      dataIndex: "name",
      key: "name",
      sorter: true,
    },
    {
      title: language === "ar" ? "النوع" : "Type",
      dataIndex: "type",
      key: "type",
      render: (type: string) => getTypeTag(type),
      sorter: true,
    },
    {
      title: language === "ar" ? "رابط API" : "API Endpoint",
      dataIndex: "endpoint",
      key: "endpoint",
      ellipsis: true,
    },
    {
      title: language === "ar" ? "مستوى الاشتراك" : "Plan Level",
      dataIndex: "plan_level",
      key: "plan_level",
      render: (planLevel: string) => getPlanLevelTag(planLevel),
      sorter: true,
    },
    {
      title: language === "ar" ? "الحالة" : "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "red"}>
          {isActive
            ? language === "ar" ? "مفعّل" : "Active"
            : language === "ar" ? "معطّل" : "Inactive"}
        </Tag>
      ),
      sorter: true,
    },
    {
      title: language === "ar" ? "الإجراءات" : "Actions",
      key: "actions",
      render: (text: string, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            title={language === "ar" ? "عرض" : "View"}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title={language === "ar" ? "تعديل" : "Edit"}
          />
          <Popconfirm
            title={language === "ar" ? "هل أنت متأكد من حذف هذه الواجهة؟" : "Are you sure you want to delete this API?"}
            onConfirm={() => handleDelete(record)}
            okText={language === "ar" ? "نعم" : "Yes"}
            cancelText={language === "ar" ? "لا" : "No"}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              title={language === "ar" ? "حذف" : "Delete"}
            />
          </Popconfirm>
          <Button
            type="text"
            icon={record.is_active ? <CloseOutlined /> : <CheckOutlined />}
            onClick={() => handleToggleActive(record)}
            title={record.is_active
              ? language === "ar" ? "تعطيل" : "Disable"
              : language === "ar" ? "تفعيل" : "Enable"}
          />
        </Space>
      ),
    },
  ];

  const apiTypeOptions = [
    { label: language === "ar" ? "نموذج لغوي" : "LLM", value: "llm" },
    { label: language === "ar" ? "صوت" : "Speech", value: "speech" },
    { label: language === "ar" ? "رؤية" : "Vision", value: "vision" },
    { label: language === "ar" ? "تحليل" : "Analysis", value: "analysis" },
    { label: language === "ar" ? "ترجمة" : "Translation", value: "translation" },
  ];

  const planLevelOptions = [
    { label: language === "ar" ? "مجاني" : "Free", value: "free" },
    { label: language === "ar" ? "أساسي" : "Basic", value: "basic" },
    { label: language === "ar" ? "متقدم" : "Premium", value: "premium" },
    { label: language === "ar" ? "مؤسسي" : "Enterprise", value: "enterprise" },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">
          {language === "ar" ? "واجهات نماذج الذكاء" : "LLM & Services"}
        </h2>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          {language === "ar" ? "إضافة واجهة جديدة" : "Add New API"}
        </Button>
      </div>

      <Alert
        message={language === "ar" ? "التكامل مع إعدادات الذكاء الاصطناعي" : "AI Settings Integration"}
        description={
          language === "ar" 
            ? "التغييرات التي تقوم بها هنا ستظهر تلقائيًا في صفحة إعدادات الذكاء الاصطناعي. يمكنك تعطيل هذه الميزة أدناه."
            : "Changes you make here will automatically appear in the AI Settings page. You can disable this feature below."
        }
        type="info"
        showIcon
        action={
          <Switch 
            checked={syncWithAISettings} 
            onChange={setSyncWithAISettings} 
            checkedChildren={language === "ar" ? "مفعّل" : "On"} 
            unCheckedChildren={language === "ar" ? "معطّل" : "Off"} 
          />
        }
        className="mb-6"
      />

      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane
            tab={
              <span>
                <ApiOutlined /> {language === "ar" ? "النماذج اللغوية" : "LLM Models"}
              </span>
            }
            key="llm"
          />
          <TabPane
            tab={
              <span>
                <ApiOutlined /> {language === "ar" ? "خدمات الصوت" : "Speech Services"}
              </span>
            }
            key="speech"
          />
          <TabPane
            tab={
              <span>
                <ApiOutlined /> {language === "ar" ? "خدمات الرؤية" : "Vision Services"}
              </span>
            }
            key="vision"
          />
          <TabPane
            tab={
              <span>
                <ApiOutlined /> {language === "ar" ? "خدمات التحليل" : "Analysis Services"}
              </span>
            }
            key="analysis"
          />
          <TabPane
            tab={
              <span>
                <ApiOutlined /> {language === "ar" ? "خدمات الترجمة" : "Translation Services"}
              </span>
            }
            key="translation"
          />
        </Tabs>

        <Table
          {...tableProps}
          rowKey="id"
          columns={columns}
        />
      </Card>

      {/* Create Modal */}
      <Modal
        title={language === "ar" ? "إضافة واجهة جديدة" : "Add New API"}
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          {...createFormProps}
          layout="vertical"
          onFinish={(values) => {
            // Include the JSON config in the form values
            onCreateFinish({
              ...values,
              config: configJson,
            });
          }}
        >
          <Form.Item
            name="name"
            label={language === "ar" ? "الاسم" : "Name"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الاسم" : "Please enter a name" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="endpoint"
            label={language === "ar" ? "رابط API" : "API Endpoint"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال رابط API" : "Please enter an API endpoint" }]}
          >
            <Input placeholder="http://localhost:11434/api/generate" />
          </Form.Item>

          <Form.Item
            name="type"
            label={language === "ar" ? "النوع" : "Type"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار النوع" : "Please select a type" }]}
            initialValue={activeTab}
          >
            <Select options={apiTypeOptions} />
          </Form.Item>

          <Form.Item
            name="plan_level"
            label={language === "ar" ? "مستوى الاشتراك" : "Plan Level"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار مستوى الاشتراك" : "Please select a plan level" }]}
            initialValue="free"
          >
            <Select options={planLevelOptions} />
          </Form.Item>

          <Form.Item
            name="api_key"
            label={language === "ar" ? "مفتاح API" : "API Key"}
          >
            <Input.Password placeholder="sk-..." />
          </Form.Item>

          <Form.Item
            name="config_editor"
            label={language === "ar" ? "إعدادات JSON" : "JSON Configuration"}
          >
            <div className="border rounded-md">
              <JSONInput
                id="config-json-editor"
                placeholder={configJson}
                locale={locale}
                height="300px"
                width="100%"
                onChange={(value: any) => {
                  if (value.jsObject) {
                    setConfigJson(value.jsObject);
                  }
                }}
              />
            </div>
          </Form.Item>

          <Form.Item
            name="is_active"
            label={language === "ar" ? "مفعّل" : "Active"}
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <div className="flex justify-between items-center">
              <div>
                <Switch 
                  checked={syncWithAISettings} 
                  onChange={setSyncWithAISettings} 
                  className="mr-2" 
                />
                <span>
                  {language === "ar" ? "تحديث إعدادات الذكاء الاصطناعي تلقائيًا" : "Auto-update AI Settings"}
                </span>
              </div>
              <div className="flex gap-2">
                <Button onClick={() => setIsCreateModalVisible(false)}>
                  {language === "ar" ? "إلغاء" : "Cancel"}
                </Button>
                <Button type="primary" {...createSaveButtonProps}>
                  {language === "ar" ? "إنشاء" : "Create"}
                </Button>
              </div>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Modal */}
      <Modal
        title={language === "ar" ? "تعديل واجهة API" : "Edit API"}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          {...editFormProps}
          layout="vertical"
          onFinish={(values) => {
            // Include the JSON config in the form values
            onEditFinish({
              ...values,
              config: configJson,
            });
          }}
        >
          <Form.Item
            name="name"
            label={language === "ar" ? "الاسم" : "Name"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال الاسم" : "Please enter a name" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="endpoint"
            label={language === "ar" ? "رابط API" : "API Endpoint"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء إدخال رابط API" : "Please enter an API endpoint" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label={language === "ar" ? "النوع" : "Type"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار النوع" : "Please select a type" }]}
          >
            <Select options={apiTypeOptions} />
          </Form.Item>

          <Form.Item
            name="plan_level"
            label={language === "ar" ? "مستوى الاشتراك" : "Plan Level"}
            rules={[{ required: true, message: language === "ar" ? "الرجاء اختيار مستوى الاشتراك" : "Please select a plan level" }]}
          >
            <Select options={planLevelOptions} />
          </Form.Item>

          <Form.Item
            name="api_key"
            label={language === "ar" ? "مفتاح API" : "API Key"}
          >
            <Input.Password placeholder="sk-..." />
          </Form.Item>

          <Form.Item
            name="config_editor"
            label={language === "ar" ? "إعدادات JSON" : "JSON Configuration"}
          >
            <div className="border rounded-md">
              <JSONInput
                id="config-json-editor-edit"
                placeholder={configJson}
                locale={locale}
                height="300px"
                width="100%"
                onChange={(value: any) => {
                  if (value.jsObject) {
                    setConfigJson(value.jsObject);
                  }
                }}
              />
            </div>
          </Form.Item>

          <Form.Item
            name="is_active"
            label={language === "ar" ? "مفعّل" : "Active"}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <div className="flex justify-between items-center">
              <div>
                <Switch 
                  checked={syncWithAISettings} 
                  onChange={setSyncWithAISettings} 
                  className="mr-2" 
                />
                <span>
                  {language === "ar" ? "تحديث إعدادات الذكاء الاصطناعي تلقائيًا" : "Auto-update AI Settings"}
                </span>
              </div>
              <div className="flex gap-2">
                <Button onClick={() => setIsEditModalVisible(false)}>
                  {language === "ar" ? "إلغاء" : "Cancel"}
                </Button>
                <Button type="primary" {...editSaveButtonProps}>
                  {language === "ar" ? "حفظ" : "Save"}
                </Button>
              </div>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* View Drawer */}
      <Drawer
        title={selectedRecord?.name}
        placement="right"
        width={600}
        onClose={() => setIsViewDrawerVisible(false)}
        open={isViewDrawerVisible}
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<LinkOutlined />}
              onClick={() => {
                // Navigate to AI Settings page
                window.location.href = "/ai-settings";
              }}
            >
              {language === "ar" ? "الذهاب إلى إعدادات الذكاء الاصطناعي" : "Go to AI Settings"}
            </Button>
          </Space>
        }
      >
        {showQueryResult.isLoading ? (
          <div className="flex justify-center items-center h-full">
            <Spin size="large" />
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <Title level={5}>{language === "ar" ? "معلومات أساسية" : "Basic Information"}</Title>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text strong>{language === "ar" ? "المعرف:" : "ID:"}</Text>
                  <div>{selectedRecord?.id}</div>
                </div>
                <div>
                  <Text strong>{language === "ar" ? "النوع:" : "Type:"}</Text>
                  <div>{getTypeTag(selectedRecord?.type)}</div>
                </div>
                <div>
                  <Text strong>{language === "ar" ? "مستوى الاشتراك:" : "Plan Level:"}</Text>
                  <div>{getPlanLevelTag(selectedRecord?.plan_level)}</div>
                </div>
                <div>
                  <Text strong>{language === "ar" ? "الحالة:" : "Status:"}</Text>
                  <div>
                    <Tag color={selectedRecord?.is_active ? "green" : "red"}>
                      {selectedRecord?.is_active
                        ? language === "ar" ? "مفعّل" : "Active"
                        : language === "ar" ? "معطّل" : "Inactive"}
                    </Tag>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <Title level={5}>{language === "ar" ? "رابط API" : "API Endpoint"}</Title>
              <div className="bg-gray-50 p-3 rounded-md">
                <code>{selectedRecord?.endpoint}</code>
              </div>
            </div>

            {selectedRecord?.api_key && (
              <div className="mb-6">
                <Title level={5}>{language === "ar" ? "مفتاح API" : "API Key"}</Title>
                <div className="flex items-center">
                  <Input.Password
                    value={selectedRecord?.api_key}
                    readOnly
                    addonBefore={<KeyOutlined />}
                  />
                </div>
              </div>
            )}

            <div className="mb-6">
              <Title level={5}>{language === "ar" ? "إعدادات JSON" : "JSON Configuration"}</Title>
              <div className="bg-gray-50 p-3 rounded-md overflow-auto max-h-60">
                <pre>{JSON.stringify(selectedRecord?.config, null, 2)}</pre>
              </div>
            </div>

            <div>
              <Title level={5}>{language === "ar" ? "تاريخ الإنشاء" : "Created At"}</Title>
              <div>{new Date(selectedRecord?.created_at).toLocaleString()}</div>
            </div>

            <Divider />

            <div className="mb-6">
              <Title level={5}>{language === "ar" ? "التكامل مع إعدادات الذكاء الاصطناعي" : "AI Settings Integration"}</Title>
              <Alert
                message={
                  language === "ar" 
                    ? "هذا النموذج متاح في إعدادات الذكاء الاصطناعي" 
                    : "This model is available in AI Settings"
                }
                description={
                  language === "ar"
                    ? "يمكن للمسؤولين اختيار هذا النموذج كنموذج افتراضي في صفحة إعدادات الذكاء الاصطناعي."
                    : "Administrators can select this model as the default model in the AI Settings page."
                }
                type="success"
                showIcon
              />
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};
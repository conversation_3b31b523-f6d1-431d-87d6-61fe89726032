import { useState } from "react";
import {
  Table,
  Card,
  Space,
  Button,
  Tag,
  Dropdown,
  Menu,
  Input,
  Select,
  Modal,
  Form,
  Drawer,
  Typography,
  Tabs,
  Upload,
  message,
} from "antd";
import {
  FileTextOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  UploadOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../hooks/use-translation";
import { formatDateTime, formatNumber } from "../lib/utils";

const { TextArea } = Input;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

export const LegalCitationList = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isViewDrawerVisible, setIsViewDrawerVisible] = useState(false);
  const [isBulkImportModalVisible, setIsBulkImportModalVisible] = useState(false);
  const [selectedCitation, setSelectedCitation] = useState<any>(null);

  // بيانات تجريبية للاستشهادات القانونية
  const citations = [
    {
      id: "1",
      articleNumber: "57",
      title: "المادة 57 من نظام المرافعات الشرعية",
      content:
        "إذا غاب المدعى عليه عن الجلسة الأولى ولم يكن قد تبلغ لشخصه أو وكيله في الدعوى نفسها، وتم تبليغه لاحقاً ولم يحضر دون عذر تقبله المحكمة، فإن المحكمة تنظر في الدعوى وتصدر حكمها غيابياً.",
      source: "نظام المرافعات الشرعية، الصادر بالمرسوم الملكي رقم (م/1) وتاريخ 1435/01/22هـ",
      section: "الباب الخامس - الأحكام الغيابية",
      category: "المرافعات",
      tags: ["أحكام غيابية", "تبليغ", "محكمة"],
      usageCount: 45,
      lastUsed: new Date("2024-02-15"),
      createdAt: new Date("2023-05-10"),
      updatedAt: new Date("2023-05-10"),
    },
    {
      id: "2",
      articleNumber: "11",
      title: "المادة 11 من نظام إيجار العقارات",
      content:
        "لا يجوز للمؤجر زيادة الأجرة خلال مدة العقد ما لم يتم الاتفاق على ذلك صراحةً في العقد.",
      source: "نظام إيجار العقارات، الصادر بالمرسوم الملكي رقم (م/61) وتاريخ 1427/09/18هـ",
      section: "الباب الثاني - الأجرة",
      category: "العقارات",
      tags: ["إيجار", "أجرة", "عقد"],
      usageCount: 78,
      lastUsed: new Date("2024-02-20"),
      createdAt: new Date("2023-03-15"),
      updatedAt: new Date("2023-03-15"),
    },
    {
      id: "3",
      articleNumber: "77",
      title: "المادة 77 من نظام العمل",
      content:
        "لا يجوز لصاحب العمل فسخ العقد دون مكافأة أو إشعار العامل أو تعويضه إلا في الحالات الواردة في هذه المادة، وبشرط أن تتاح للعامل الفرصة لكي يبدي أسباب معارضته للفسخ.",
      source: "نظام العمل، الصادر بالمرسوم الملكي رقم (م/51) وتاريخ 1426/08/23هـ",
      section: "الباب السادس - انتهاء عقد العمل",
      category: "العمل",
      tags: ["فسخ العقد", "تعويض", "إنهاء الخدمة"],
      usageCount: 62,
      lastUsed: new Date("2024-02-18"),
      createdAt: new Date("2023-04-20"),
      updatedAt: new Date("2023-04-20"),
    },
    {
      id: "4",
      articleNumber: "149",
      title: "المادة 149 من نظام الشركات",
      content:
        "يجب أن يكون رأس مال الشركة ذات المسؤولية المحدودة كافياً لتحقيق غرضها، وفي جميع الأحوال لا يجوز أن يقل عن خمسمائة ألف ريال.",
      source: "نظام الشركات، الصادر بالمرسوم الملكي رقم (م/3) وتاريخ 1437/01/28هـ",
      section: "الباب السابع - الشركة ذات المسؤولية المحدودة",
      category: "الشركات",
      tags: ["شركة ذات مسؤولية محدودة", "رأس المال", "تأسيس"],
      usageCount: 35,
      lastUsed: new Date("2024-02-10"),
      createdAt: new Date("2023-06-05"),
      updatedAt: new Date("2023-06-05"),
    },
    {
      id: "5",
      articleNumber: "53",
      title: "المادة 53 من نظام الأحوال الشخصية",
      content:
        "للزوجة أن تطلب التفريق إذا أثبتت تضررها من استمرار الحياة الزوجية، ويتعذر استمرار الحياة بينهما.",
      source: "نظام الأحوال الشخصية، الصادر بالمرسوم الملكي رقم (م/73) وتاريخ 1443/06/05هـ",
      section: "الباب الرابع - الفرقة بين الزوجين",
      category: "الأحوال الشخصية",
      tags: ["طلاق", "تفريق", "ضرر"],
      usageCount: 28,
      lastUsed: new Date("2024-02-12"),
      createdAt: new Date("2023-07-15"),
      updatedAt: new Date("2023-07-15"),
    },
  ];

  const handleView = (citation: any) => {
    setSelectedCitation(citation);
    setIsViewDrawerVisible(true);
  };

  const handleEdit = (citation: any) => {
    setSelectedCitation(citation);
    setIsEditModalVisible(true);
  };

  const handleDelete = (citation: any) => {
    setSelectedCitation(citation);
    setIsDeleteModalVisible(true);
  };

  const columns = [
    {
      title: t("legalCitations.articleNumber"),
      dataIndex: "articleNumber",
      key: "articleNumber",
    },
    {
      title: t("legalCitations.title"),
      dataIndex: "title",
      key: "title",
    },
    {
      title: t("legalCitations.category"),
      dataIndex: "category",
      key: "category",
      render: (category: string) => <Tag>{category}</Tag>,
    },
    {
      title: t("legalCitations.usageCount"),
      dataIndex: "usageCount",
      key: "usageCount",
      render: (count: number) => formatNumber(count),
    },
    {
      title: t("legalCitations.lastUsed"),
      dataIndex: "lastUsed",
      key: "lastUsed",
      render: (date: Date) => formatDateTime(date),
    },
    {
      title: t("legalCitations.actions"),
      key: "actions",
      render: (_: any, record: any) => (
        <Dropdown
          menu={{
            items: [
              {
                key: "view",
                icon: <EyeOutlined />,
                label: t("legalCitations.citationDetails"),
                onClick: () => handleView(record),
              },
              {
                key: "edit",
                icon: <EditOutlined />,
                label: t("legalCitations.editCitation"),
                onClick: () => handleEdit(record),
              },
              {
                key: "delete",
                icon: <DeleteOutlined />,
                label: t("legalCitations.deleteCitation"),
                danger: true,
                onClick: () => handleDelete(record),
              },
            ],
          }}
          trigger={["click"]}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // تصفية الاستشهادات بناءً على معايير البحث
  const filteredCitations = citations.filter((citation) => {
    let matchesSearch = true;
    let matchesCategory = true;

    if (searchText) {
      matchesSearch =
        citation.title.toLowerCase().includes(searchText.toLowerCase()) ||
        citation.content.toLowerCase().includes(searchText.toLowerCase()) ||
        citation.articleNumber.includes(searchText);
    }

    if (selectedCategory) {
      matchesCategory = citation.category === selectedCategory;
    }

    return matchesSearch && matchesCategory;
  });

  // استخراج الفئات الفريدة
  const categories = Array.from(new Set(citations.map((c) => c.category)));

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t("legalCitations.title")}</h2>
        <Space>
          <Button
            icon={<UploadOutlined />}
            onClick={() => setIsBulkImportModalVisible(true)}
          >
            {t("legalCitations.bulkImport")}
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            {t("legalCitations.createCitation")}
          </Button>
        </Space>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <div className="flex flex-wrap gap-4">
          <Input
            placeholder={t("common.search")}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
          />
          <Select
            placeholder={t("legalCitations.category")}
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setSelectedCategory(value)}
            options={categories.map((category) => ({
              value: category,
              label: category,
            }))}
          />
          <Button icon={<FilterOutlined />} onClick={() => {
            setSearchText("");
            setSelectedCategory(null);
          }}>
            إعادة تعيين
          </Button>
        </div>
      </Card>

      {/* جدول الاستشهادات */}
      <Table
        dataSource={filteredCitations}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      {/* نافذة إنشاء استشهاد جديد */}
      <Modal
        title={t("legalCitations.createCitation")}
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form layout="vertical">
          <Form.Item
            name="articleNumber"
            label={t("legalCitations.articleNumber")}
            rules={[{ required: true, message: "الرجاء إدخال رقم المادة" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="title"
            label={t("legalCitations.title")}
            rules={[{ required: true, message: "الرجاء إدخال العنوان" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="content"
            label={t("legalCitations.content")}
            rules={[{ required: true, message: "الرجاء إدخال المحتوى" }]}
          >
            <TextArea rows={6} />
          </Form.Item>
          <Form.Item
            name="source"
            label={t("legalCitations.source")}
            rules={[{ required: true, message: "الرجاء إدخال المصدر" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="section"
            label={t("legalCitations.section")}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="category"
            label={t("legalCitations.category")}
            rules={[{ required: true, message: "الرجاء اختيار الفئة" }]}
          >
            <Select
              options={categories.map((category) => ({
                value: category,
                label: category,
              }))}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <div className="p-2 border-t">
                    <Input
                      placeholder="فئة جديدة"
                      suffix={
                        <Button type="text" size="small">
                          <PlusOutlined /> إضافة
                        </Button>
                      }
                    />
                  </div>
                </>
              )}
            />
          </Form.Item>
          <Form.Item
            name="tags"
            label={t("legalCitations.tags")}
          >
            <Select
              mode="tags"
              placeholder="أدخل الوسوم"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t("common.create")}
              </Button>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                {t("common.cancel")}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة تعديل الاستشهاد */}
      <Modal
        title={t("legalCitations.editCitation")}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
        width={700}
      >
        {selectedCitation && (
          <Form layout="vertical" initialValues={selectedCitation}>
            <Form.Item
              name="articleNumber"
              label={t("legalCitations.articleNumber")}
              rules={[{ required: true, message: "الرجاء إدخال رقم المادة" }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="title"
              label={t("legalCitations.title")}
              rules={[{ required: true, message: "الرجاء إدخال العنوان" }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="content"
              label={t("legalCitations.content")}
              rules={[{ required: true, message: "الرجاء إدخال المحتوى" }]}
            >
              <TextArea rows={6} />
            </Form.Item>
            <Form.Item
              name="source"
              label={t("legalCitations.source")}
              rules={[{ required: true, message: "الرجاء إدخال المصدر" }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="section"
              label={t("legalCitations.section")}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="category"
              label={t("legalCitations.category")}
              rules={[{ required: true, message: "الرجاء اختيار الفئة" }]}
            >
              <Select
                options={categories.map((category) => ({
                  value: category,
                  label: category,
                }))}
              />
            </Form.Item>
            <Form.Item
              name="tags"
              label={t("legalCitations.tags")}
            >
              <Select
                mode="tags"
                placeholder="أدخل الوسوم"
                style={{ width: '100%' }}
                defaultValue={selectedCitation.tags}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {t("common.save")}
                </Button>
                <Button onClick={() => setIsEditModalVisible(false)}>
                  {t("common.cancel")}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* نافذة حذف الاستشهاد */}
      <Modal
        title={t("legalCitations.deleteCitation")}
        open={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
            {t("common.cancel")}
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={() => {
              // تنفيذ حذف الاستشهاد
              console.log("Delete citation:", selectedCitation);
              setIsDeleteModalVisible(false);
            }}
          >
            {t("common.delete")}
          </Button>,
        ]}
      >
        <p>
          هل أنت متأكد من رغبتك في حذف الاستشهاد "{selectedCitation?.title}"؟ هذا الإجراء لا يمكن التراجع عنه.
        </p>
      </Modal>

      {/* نافذة الاستيراد الجماعي */}
      <Modal
        title={t("legalCitations.bulkImport")}
        open={isBulkImportModalVisible}
        onCancel={() => setIsBulkImportModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsBulkImportModalVisible(false)}>
            {t("common.cancel")}
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={() => {
              // تنفيذ الاستيراد الجماعي
              message.success("تم استيراد الاستشهادات بنجاح");
              setIsBulkImportModalVisible(false);
            }}
          >
            {t("legalCitations.bulkImport")}
          </Button>,
        ]}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="تحميل ملف" key="1">
            <Upload.Dragger
              accept=".csv, .xlsx, .json"
              beforeUpload={() => false}
              onChange={(info) => {
                console.log("File:", info.file);
              }}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">
                انقر أو اسحب الملف إلى هذه المنطقة للتحميل
              </p>
              <p className="ant-upload-hint">
                يدعم ملفات CSV و Excel و JSON
              </p>
            </Upload.Dragger>
          </TabPane>
          <TabPane tab="نسخ ولصق" key="2">
            <Form.Item
              name="bulkData"
              rules={[{ required: true, message: "الرجاء إدخال البيانات" }]}
            >
              <TextArea
                rows={10}
                placeholder="الصق البيانات هنا بتنسيق JSON أو CSV"
              />
            </Form.Item>
          </TabPane>
        </Tabs>
      </Modal>

      {/* درج عرض الاستشهاد */}
      <Drawer
        title={selectedCitation?.title}
        placement="right"
        width={600}
        onClose={() => setIsViewDrawerVisible(false)}
        open={isViewDrawerVisible}
        extra={
          <Space>
            <Button onClick={() => setIsViewDrawerVisible(false)}>
              {t("common.close")}
            </Button>
          </Space>
        }
      >
        {selectedCitation && (
          <>
            <div className="mb-6">
              <Title level={4}>{t("legalCitations.content")}</Title>
              <Paragraph className="text-lg leading-relaxed">
                {selectedCitation.content}
              </Paragraph>
            </div>

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.source")}</Title>
              <Paragraph>{selectedCitation.source}</Paragraph>
            </div>

            {selectedCitation.section && (
              <div className="mb-6">
                <Title level={4}>{t("legalCitations.section")}</Title>
                <Paragraph>{selectedCitation.section}</Paragraph>
              </div>
            )}

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.category")}</Title>
              <Tag>{selectedCitation.category}</Tag>
            </div>

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.tags")}</Title>
              <div>
                {selectedCitation.tags.map((tag: string) => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.usageCount")}</Title>
              <Paragraph>{formatNumber(selectedCitation.usageCount)}</Paragraph>
            </div>

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.lastUsed")}</Title>
              <Paragraph>{formatDateTime(selectedCitation.lastUsed)}</Paragraph>
            </div>

            <div className="mb-6">
              <Title level={4}>{t("legalCitations.createdAt")}</Title>
              <Paragraph>{formatDateTime(selectedCitation.createdAt)}</Paragraph>
            </div>

            <div>
              <Title level={4}>{t("legalCitations.updatedAt")}</Title>
              <Paragraph>{formatDateTime(selectedCitation.updatedAt)}</Paragraph>
            </div>
          </>
        )}
      </Drawer>
    </div>
  );
};
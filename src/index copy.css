@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== الهوية البصرية - اللون الأزرق الرئيسي ===== */
:root {
  /* Light Theme Colors - الهوية البصرية */
  --color-background: 255 255 255;           /* أبيض نقي */
  --color-foreground: 15 23 42;              /* أسود داكن للنصوص */
  --color-card: 255 255 255;                 /* أبيض للكروت */
  --color-card-foreground: 15 23 42;         /* نص الكروت */
  --color-popover: 255 255 255;              /* خلفية النوافذ المنبثقة */
  --color-popover-foreground: 15 23 42;      /* نص النوافذ المنبثقة */
  
  /* اللون الأزرق الرئيسي وتدرجاته */
  --color-primary: 38 53 237;                /* #2635ED - اللون الأزرق الرئيسي */
  --color-primary-foreground: 255 255 255;   /* أبيض على الأزرق */
  
  /* تدرجات الرمادي */
  --color-secondary: 248 250 252;            /* رمادي فاتح جداً */
  --color-secondary-foreground: 15 23 42;    /* نص على الرمادي الفاتح */
  --color-muted: 241 245 249;                /* رمادي فاتح */
  --color-muted-foreground: 100 116 139;     /* رمادي متوسط للنصوص الثانوية */
  --color-accent: 248 250 252;               /* رمادي فاتح للتمييز */
  --color-accent-foreground: 15 23 42;       /* نص على الرمادي */
  
  /* ألوان الحالات */
  --color-destructive: 239 68 68;            /* أحمر للأخطاء */
  --color-destructive-foreground: 255 255 255;
  --color-success: 34 197 94;                /* أخضر للنجاح */
  --color-warning: 245 158 11;               /* برتقالي للتحذير */
  --color-info: 59 130 246;                  /* أزرق فاتح للمعلومات */
  
  /* الحدود والإدخال */
  --color-border: 226 232 240;               /* رمادي فاتح للحدود */
  --color-input: 226 232 240;                /* رمادي فاتح للإدخال */
  --color-ring: 38 53 237;                   /* الأزرق الرئيسي للتركيز */
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

.dark {
  /* 🌙 المظهر الداكن الجديد - الهوية البصرية المحدثة */
  --color-background: 16 16 16;              /* rgb(16, 16, 16) - خلفية داكنة */
  --color-foreground: 224 224 224;           /* rgb(224, 224, 224) - نص فاتح */
  --color-card: 23 23 23;                    /* rgb(23, 23, 23) - كروت داكنة */
  --color-card-foreground: 224 224 224;      /* نص الكروت فاتح */
  --color-popover: 23 23 23;                 /* نوافذ منبثقة داكنة */
  --color-popover-foreground: 224 224 224;   /* نص النوافذ المنبثقة فاتح */
  
  /* 🎯 استبدال اللون الأزرق بالرمادي في المظهر الداكن */
  --color-primary: 38 38 38;                 /* rgb(38, 38, 38) - رمادي بدلاً من الأزرق */
  --color-primary-foreground: 224 224 224;   /* نص فاتح على الرمادي */
  
  /* تدرجات الرمادي للمظهر الداكن */
  --color-secondary: 38 38 38;               /* rgb(38, 38, 38) - رمادي متوسط */
  --color-secondary-foreground: 224 224 224; /* نص فاتح على الرمادي */
  --color-muted: 38 38 38;                   /* rgb(38, 38, 38) - رمادي للعناصر المكتومة */
  --color-muted-foreground: 156 163 175;     /* رمادي فاتح للنصوص الثانوية */
  --color-accent: 38 38 38;                  /* rgb(38, 38, 38) - رمادي للتمييز */
  --color-accent-foreground: 224 224 224;    /* نص فاتح على الرمادي */
  
  /* ألوان الحالات للمظهر الداكن */
  --color-destructive: 239 68 68;            /* أحمر للأخطاء */
  --color-destructive-foreground: 224 224 224;
  --color-success: 34 197 94;                /* أخضر للنجاح */
  --color-warning: 245 158 11;               /* برتقالي للتحذير */
  --color-info: 96 165 250;                  /* أزرق أفتح للمعلومات */
  
  /* الحدود والإدخال للمظهر الداكن */
  --color-border: 38 38 38;                  /* rgb(38, 38, 38) - حدود داكنة */
  --color-input: 38 38 38;                   /* rgb(38, 38, 38) - حقول إدخال داكنة */
  --color-ring: 38 38 38;                    /* rgb(38, 38, 38) - رمادي للتركيز بدلاً من الأزرق */
  
  /* ظلال محسنة للمظهر الداكن */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -1px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.6), 0 4px 6px -2px rgb(0 0 0 / 0.5);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.7), 0 10px 10px -5px rgb(0 0 0 / 0.6);
}

/* ===== Base Styles ===== */
* {
  border-color: rgb(var(--color-border));
}

body {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===== Utility Classes ===== */
.bg-background { background-color: rgb(var(--color-background)); }
.bg-foreground { background-color: rgb(var(--color-foreground)); }
.bg-card { background-color: rgb(var(--color-card)); }
.bg-popover { background-color: rgb(var(--color-popover)); }
.bg-primary { background-color: rgb(var(--color-primary)); }
.bg-secondary { background-color: rgb(var(--color-secondary)); }
.bg-muted { background-color: rgb(var(--color-muted)); }
.bg-accent { background-color: rgb(var(--color-accent)); }
.bg-destructive { background-color: rgb(var(--color-destructive)); }
.bg-success { background-color: rgb(var(--color-success)); }
.bg-warning { background-color: rgb(var(--color-warning)); }
.bg-info { background-color: rgb(var(--color-info)); }

.text-background { color: rgb(var(--color-background)); }
.text-foreground { color: rgb(var(--color-foreground)); }
.text-card-foreground { color: rgb(var(--color-card-foreground)); }
.text-popover-foreground { color: rgb(var(--color-popover-foreground)); }
.text-primary { color: rgb(var(--color-primary)); }
.text-primary-foreground { color: rgb(var(--color-primary-foreground)); }
.text-secondary-foreground { color: rgb(var(--color-secondary-foreground)); }
.text-muted-foreground { color: rgb(var(--color-muted-foreground)); }
.text-accent-foreground { color: rgb(var(--color-accent-foreground)); }
.text-destructive { color: rgb(var(--color-destructive)); }
.text-destructive-foreground { color: rgb(var(--color-destructive-foreground)); }
.text-success { color: rgb(var(--color-success)); }
.text-warning { color: rgb(var(--color-warning)); }
.text-info { color: rgb(var(--color-info)); }

.border-border { border-color: rgb(var(--color-border)); }
.border-input { border-color: rgb(var(--color-input)); }
.border-primary { border-color: rgb(var(--color-primary)); }
.border-destructive { border-color: rgb(var(--color-destructive)); }
.border-success { border-color: rgb(var(--color-success)); }
.border-warning { border-color: rgb(var(--color-warning)); }
.border-info { border-color: rgb(var(--color-info)); }

/* ===== Enhanced Shadows ===== */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ===== Animations ===== */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--color-muted) / 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--color-muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-muted-foreground) / 0.5);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(var(--color-muted-foreground) / 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-muted-foreground) / 0.6);
}

/* ===== Focus States ===== */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-background;
}

/* ===== Button Enhancements ===== */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-ring;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus-ring;
}

.btn-ghost {
  @apply hover:bg-accent hover:text-accent-foreground focus-ring;
}

/* ===== Card Enhancements ===== */
.card {
  @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm;
}

.card-header {
  @apply p-6 pb-0;
}

.card-content {
  @apply p-6;
}

.card-footer {
  @apply p-6 pt-0;
}

/* ===== Input Enhancements ===== */
.input {
  @apply bg-background border border-input text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none;
}

/* ===== Chat-specific Styling ===== */
.chat-bubble {
  @apply bg-card border-border;
}

.chat-bubble:hover {
  @apply border-border/60;
}

.chat-input {
  @apply bg-card border-border text-foreground placeholder:text-muted-foreground;
}

.chat-input:focus {
  @apply border-primary ring-primary/20;
}

/* ===== Legal Citation Styling ===== */
.legal-citation-button {
  color: rgb(var(--color-primary));
  text-decoration-color: rgb(var(--color-primary));
}

.legal-citation-button:hover {
  color: rgb(var(--color-primary) / 0.8);
  text-decoration-color: rgb(var(--color-primary) / 0.8);
}

.citation-popup {
  @apply bg-popover border-border text-popover-foreground;
}

.citation-popup .citation-header {
  @apply bg-muted/50 border-border;
}

/* ===== Sidebar Styling ===== */
.sidebar {
  @apply bg-card border-border;
}

.sidebar-item {
  @apply text-muted-foreground hover:text-foreground hover:bg-accent;
}

.sidebar-item.active {
  @apply text-primary bg-primary/10;
}

/* تخصيص العنصر النشط في المظهر الداكن فقط */
.dark .sidebar-item.active {
  background-color: rgb(75 85 99) !important; /* رمادي أغمق للتباين */
  color: rgb(255 255 255) !important; /* نص أبيض للوضوح */
}
body:not(.dark) .sidebar-item.active {
  background-color: rgb(var(--color-primary) / 0.1) !important;
  color: rgb(var(--color-primary)) !important;
}

/* قاعدة للسمة الفاتحة لإعادة تعيين الألوان */
body:not(.dark) .sidebar-item.active {
  background-color: rgb(var(--color-primary) / 0.1) !important;
  color: rgb(var(--color-primary)) !important;
}

/* ===== 🆕 Sidebar Expansion Animations ===== */
.sidebar {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-expanded-content {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسين انتقالات الأيقونات النشطة */
.sidebar-item {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.sidebar-item:hover {
  transform: translateY(-1px);
}

.sidebar-item.active {
  transform: scale(1.05);
}

/* تحسين مؤشر النشاط */
.sidebar-item .active-indicator {
  transition: all 0.3s ease-in-out;
}

/* تحسين الظلال للعناصر النشطة */
/*
.sidebar-item.active {
  box-shadow: 0 4px 12px rgb(var(--color-primary) / 0.15);
}
*/
/* ===== Logo Styles - الحفاظ على الألوان الأصلية ===== */
.logo-component {
  /* منع تطبيق أي فلاتر أو تأثيرات على الشعار */
  filter: none !important;
  opacity: 1 !important;
  /* الحفاظ على الألوان الأصلية للشعار */
  color-scheme: initial !important;
  /* إزالة أي خلفية */
  background: transparent !important;
  /* منع أي حدود */
  border: none !important;
  /* منع أي ظلال */
  box-shadow: none !important;
}

/* ضمان عدم تأثر الشعار بالمظهر الداكن */
.dark .logo-component {
  filter: none !important;
  opacity: 1 !important;
  /* منع تطبيق أي تأثيرات للمظهر الداكن على الشعار */
  color-scheme: initial !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* ===== 🔧 إصلاح Switch للعمل مع RTL ===== */
.switch-container {
  /* إجبار اتجاه LTR للـ Switch */
  direction: ltr !important;
}

/* إصلاح Switch في البيئة RTL */
[dir="rtl"] .switch-container {
  direction: ltr !important;
}

/* ضمان عمل Switch بشكل صحيح في جميع الحالات */
.switch-button {
  /* منع تأثر Switch بـ RTL */
  direction: ltr !important;
  text-align: left !important;
}

[dir="rtl"] .switch-button {
  direction: ltr !important;
  text-align: left !important;
}

/* إصلاح حركة الدائرة في Switch */
.switch-thumb {
  transition: transform 0.2s ease-in-out;
}

/* ===== Smooth Transitions ===== */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* ===== Accessibility Improvements ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== High Contrast Support ===== */
@media (prefers-contrast: high) {
  .dark {
    --color-border: 100 116 139;
    --color-muted-foreground: 203 213 225;
  }
}

/* ===== Print Styles ===== */
@media print {
  .dark {
    --color-background: 255 255 255;
    --color-foreground: 0 0 0;
    --color-card: 255 255 255;
    --color-border: 0 0 0;
  }
}

/* ===== RTL Support ===== */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-1 > * + * {
  margin-right: 0.25rem;
  margin-left: 0;
}

/* ===== Enhanced Focus Visible ===== */
.focus-visible:focus-visible {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

/* ===== Selection Styling ===== */
::selection {
  background-color: rgb(var(--color-primary) / 0.2);
  color: rgb(var(--color-foreground));
}

.dark ::selection {
  background-color: rgb(var(--color-primary) / 0.3);
  color: rgb(var(--color-foreground));
}

/* ===== شريط الأدوات التفاعلي ===== */
.interactive-toolbar {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.toolbar-hidden {
  opacity: 0;
  transform: translateY(8px);
  pointer-events: none;
}

.toolbar-visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.toolbar-button {
  transition: all 0.2s ease-in-out;
  transform: scale(1);
}

.toolbar-button:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-button:active {
  transform: scale(0.95);
}

.message-container {
  will-change: transform;
  backface-visibility: hidden;
}

/* دعم اللمس على الأجهزة المحمولة */
@media (hover: none) and (pointer: coarse) {
  .group .toolbar-hidden {
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }
}

/* تحسين إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  .interactive-toolbar,
  .toolbar-button {
    transition: none !important;
    animation: none !important;
  }
}

.toolbar-button:focus-visible {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* ===== تدرجات الألوان الأزرق ===== */
.bg-primary-50 { background-color: rgb(239 246 255); }
.bg-primary-100 { background-color: rgb(219 234 254); }
.bg-primary-200 { background-color: rgb(191 219 254); }
.bg-primary-300 { background-color: rgb(147 197 253); }
.bg-primary-400 { background-color: rgb(96 165 250); }
.bg-primary-500 { background-color: rgb(var(--color-primary)); }
.bg-primary-600 { background-color: rgb(37 99 235); }
.bg-primary-700 { background-color: rgb(29 78 216); }
.bg-primary-800 { background-color: rgb(30 64 175); }
.bg-primary-900 { background-color: rgb(30 58 138); }

.text-primary-50 { color: rgb(239 246 255); }
.text-primary-100 { color: rgb(219 234 254); }
.text-primary-200 { color: rgb(191 219 254); }
.text-primary-300 { color: rgb(147 197 253); }
.text-primary-400 { color: rgb(96 165 250); }
.text-primary-500 { color: rgb(var(--color-primary)); }
.text-primary-600 { color: rgb(37 99 235); }
.text-primary-700 { color: rgb(29 78 216); }
.text-primary-800 { color: rgb(30 64 175); }
.text-primary-900 { color: rgb(30 58 138); }

/* ===== تدرجات الرمادي ===== */
.bg-gray-50 { background-color: rgb(249 250 251); }
.bg-gray-100 { background-color: rgb(243 244 246); }
.bg-gray-200 { background-color: rgb(229 231 235); }
.bg-gray-300 { background-color: rgb(209 213 219); }
.bg-gray-400 { background-color: rgb(156 163 175); }
.bg-gray-500 { background-color: rgb(107 114 128); }
.bg-gray-600 { background-color: rgb(75 85 99); }
.bg-gray-700 { background-color: rgb(55 65 81); }
.bg-gray-800 { background-color: rgb(31 41 55); }
.bg-gray-900 { background-color: rgb(17 24 39); }

.text-gray-50 { color: rgb(249 250 251); }
.text-gray-100 { color: rgb(243 244 246); }
.text-gray-200 { color: rgb(229 231 235); }
.text-gray-300 { color: rgb(209 213 219); }
.text-gray-400 { color: rgb(156 163 175); }
.text-gray-500 { color: rgb(107 114 128); }
.text-gray-600 { color: rgb(75 85 99); }
.text-gray-700 { color: rgb(55 65 81); }
.text-gray-800 { color: rgb(31 41 55); }
.text-gray-900 { color: rgb(17 24 39); }

/* ===== 🌙 تحسينات خاصة بالمظهر الداكن ===== */
.dark {
  /* تحسين التباين للنصوص */
  --color-text-high-contrast: 240 240 240;
  --color-text-medium-contrast: 200 200 200;
  --color-text-low-contrast: 160 160 160;
}

/* تحسين قابلية القراءة في المظهر الداكن */
.dark .text-foreground {
  color: rgb(224 224 224);
}

.dark .text-muted-foreground {
  color: rgb(156 163 175);
}

/* تحسين الحدود في المظهر الداكن */
.dark .border-border {
  border-color: rgb(38 38 38);
}

/* تحسين الخلفيات في المظهر الداكن */
.dark .bg-card {
  background-color: rgb(23 23 23);
}

.dark .bg-muted {
  background-color: rgb(38 38 38);
}

/* تحسين الظلال في المظهر الداكن */
.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.5);
}

.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.6), 0 2px 4px -1px rgb(0 0 0 / 0.5);
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.7), 0 4px 6px -2px rgb(0 0 0 / 0.6);
}

/* تحسين التفاعلات في المظهر الداكن */
.dark .hover\:bg-accent:hover {
  background-color: rgb(38 38 38);
}

.dark .hover\:text-foreground:hover {
  color: rgb(224 224 224);
}

/* ===== 🎯 تخصيصات خاصة للمظهر الداكن - استبدال الأزرق بالرمادي ===== */

/* تخصيص الأزرار في المظهر الداكن */
.dark .bg-primary {
  background-color: rgb(38 38 38) !important;
}

.dark .text-primary {
  color: rgb(156 163 175) !important; /* رمادي أفتح للنصوص */
}

.dark .border-primary {
  border-color: rgb(38 38 38) !important;
}

/* تخصيص حالات التركيز في المظهر الداكن */
.dark .focus\:ring-primary:focus {
  --tw-ring-color: rgb(38 38 38) !important;
}

.dark .focus\:border-primary:focus {
  border-color: rgb(38 38 38) !important;
}

/* تخصيص الاستشهادات القانونية في المظهر الداكن */
.dark .legal-citation-button {
  color: rgb(156 163 175) !important;
  text-decoration-color: rgb(156 163 175) !important;
}

.dark .legal-citation-button:hover {
  color: rgb(203 213 225) !important;
}

/* تخصيص الأسئلة المقترحة في المظهر الداكن */
.dark .hover\:border-primary:hover {
  border-color: rgb(38 38 38) !important;
}

/* تخصيص النص الترحيبي في المظهر الداكن */
.dark .text-primary {
  color: rgb(156 163 175) !important;
}

/* تخصيص الشارات والعلامات في المظهر الداكن */
.dark .bg-primary\/10 {
  background-color: rgb(38 38 38 / 0.1) !important;
}

.dark .bg-primary\/5 {
  background-color: rgb(38 38 38 / 0.05) !important;
}

/* تخصيص الحدود المميزة في المظهر الداكن */
.dark .border-r-2.border-primary {
  border-right-color: rgb(38 38 38) !important;
}

/* تخصيص الخلفيات المتدرجة في المظهر الداكن */
.dark .from-primary\/5 {
  --tw-gradient-from: rgb(38 38 38 / 0.05) !important;
}

.dark .to-primary\/10 {
  --tw-gradient-to: rgb(38 38 38 / 0.1) !important;
}

.dark .border-primary\/20 {
  border-color: rgb(38 38 38 / 0.2) !important;
}
import { useContext } from "react";
import { useTranslation as useI18nTranslation } from "react-i18next";
import { I18nContext } from "../contexts/i18n";

export const useTranslation = () => {
  const context = useContext(I18nContext);
  const { t } = useI18nTranslation();
  
  if (context === undefined) {
    throw new Error("useTranslation must be used within an I18nProvider");
  }
  
  return {
    ...context,
    t,
  };
};
import { createContext, useState, useEffect, ReactNode } from "react";
import { useList } from "@refinedev/core";

// Define the context type
interface AISettingsContextType {
  llmModels: any[];
  speechModels: any[];
  translationModels: any[];
  visionModels: any[];
  analysisModels: any[];
  isLoading: boolean;
  refetchModels: () => void;
}

// Create the context with default values
export const AISettingsContext = createContext<AISettingsContextType>({
  llmModels: [],
  speechModels: [],
  translationModels: [],
  visionModels: [],
  analysisModels: [],
  isLoading: false,
  refetchModels: () => {},
});

interface AISettingsProviderProps {
  children: ReactNode;
}

export const AISettingsProvider = ({ children }: AISettingsProviderProps) => {
  // Fetch external APIs from Supabase
  const { data, isLoading, refetch } = useList({
    resource: "external_apis",
    filters: [
      {
        field: "is_active",
        operator: "eq",
        value: true,
      },
    ],
  });

  // Group APIs by type
  const llmModels = data?.data?.filter(api => api.type === "llm") || [];
  const speechModels = data?.data?.filter(api => api.type === "speech") || [];
  const translationModels = data?.data?.filter(api => api.type === "translation") || [];
  const visionModels = data?.data?.filter(api => api.type === "vision") || [];
  const analysisModels = data?.data?.filter(api => api.type === "analysis") || [];

  return (
    <AISettingsContext.Provider
      value={{
        llmModels,
        speechModels,
        translationModels,
        visionModels,
        analysisModels,
        isLoading,
        refetchModels: refetch,
      }}
    >
      {children}
    </AISettingsContext.Provider>
  );
};

// Custom hook to use the AI settings context
export const useAISettings = () => {
  const context = React.useContext(AISettingsContext);
  if (context === undefined) {
    throw new Error("useAISettings must be used within an AISettingsProvider");
  }
  return context;
};
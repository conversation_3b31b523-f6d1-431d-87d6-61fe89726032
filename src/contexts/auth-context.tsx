import { createContext, useContext, ReactNode } from 'react';
import { UserProfile } from '../lib/supabase';

// Mock user profile for development
const mockProfile: UserProfile = {
  id: 'dev-user-id',
  email: '<EMAIL>',
  full_name: 'مطور النظام',
  role: 'administrator',
  created_at: new Date().toISOString(),
  last_login: new Date().toISOString()
};

interface AuthContextType {
  session: any;
  user: any;
  profile: UserProfile;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: Error | null;
  refreshUser: () => Promise<void>;
}

// Create context with default development values
const AuthContext = createContext<AuthContextType>({
  session: {},
  user: { id: 'dev-user-id', email: '<EMAIL>' },
  profile: mockProfile,
  isLoading: false,
  isAuthenticated: true, // Always authenticated during development
  error: null,
  refreshUser: async () => {},
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  // Provide mock authentication data for development
  const value = {
    session: {},
    user: { id: 'dev-user-id', email: '<EMAIL>' },
    profile: mockProfile,
    isLoading: false,
    isAuthenticated: true,
    error: null,
    refreshUser: async () => {},
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
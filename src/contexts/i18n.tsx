import { createContext, useState, useEffect, ReactNode } from "react";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

export type Language = "ar" | "en";

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
}

export const I18nContext = createContext<I18nContextType>({
  language: "ar",
  setLanguage: () => {},
  isRTL: true,
});

interface I18nProviderProps {
  children: ReactNode;
}

// Configure translations
const resources = {
  ar: {
    translation: {
      common: {
        dashboard: "لوحة التحكم",
        users: "المستخدمين",
        chats: "المحادثات",
        legalCitations: "الاستشهادات القانونية",
        contentModeration: "مراقبة المحتوى",
        ragManagement: "إدارة RAG",
        subscriptionManagement: "إدارة الاشتراكات",
        subscriptionPackages: "باقات الاشتراك",
        aiSettings: "إعدادات الذكاء الاصطناعي",
        llmServices: "واجهات نماذج الذكاء",
        analytics: "التحليلات",
        settings: "الإعدادات",
        search: "بحث...",
        logout: "تسجيل الخروج",
        profile: "الملف الشخصي",
        language: "اللغة",
        theme: "المظهر",
        light: "فاتح",
        dark: "داكن",
        system: "النظام",
        save: "حفظ",
        cancel: "إلغاء",
        delete: "حذف",
        edit: "تعديل",
        create: "إنشاء",
        view: "عرض",
        close: "إغلاق",
        loading: "جاري التحميل...",
        noData: "لا توجد بيانات",
        error: "حدث خطأ",
        success: "تمت العملية بنجاح",
      },
      dashboard: {
        title: "لوحة التحكم",
        totalUsers: "إجمالي المستخدمين",
        activeUsers: "المستخدمين النشطين",
        totalChats: "إجمالي المحادثات",
        totalTokens: "إجمالي التوكينز",
        recentActivity: "النشاط الأخير",
        viewAll: "عرض الكل",
        performance: "الأداء",
        responseTime: "وقت الاستجابة",
        errorRate: "معدل الأخطاء",
        uptime: "وقت التشغيل",
      },
      users: {
        title: "إدارة المستخدمين",
        name: "الاسم",
        email: "البريد الإلكتروني",
        status: "الحالة",
        role: "الدور",
        joinDate: "تاريخ الانضمام",
        lastActive: "آخر نشاط",
        actions: "الإجراءات",
        active: "نشط",
        inactive: "غير نشط",
        suspended: "معلق",
        // الأدوار الصحيحة
        administrator: "مسؤول",
        moderator: "مشرف",
        subscriber_enterprise: "مشترك مؤسسي",
        subscriber_premium: "مشترك متقدم",
        subscriber_basic: "مشترك أساسي",
        user: "مستخدم عادي",
        createUser: "إنشاء مستخدم جديد",
        editUser: "تعديل المستخدم",
        deleteUser: "حذف المستخدم",
        suspendUser: "تعليق المستخدم",
        activateUser: "تفعيل المستخدم",
        userDetails: "تفاصيل المستخدم",
        chatCount: "عدد المحادثات",
        totalTokens: "إجمالي التوكينز",
      },
      chats: {
        title: "إدارة المحادثات",
        chatTitle: "عنوان المحادثة",
        user: "المستخدم",
        messageCount: "عدد الرسائل",
        createdAt: "تاريخ الإنشاء",
        lastActivity: "آخر نشاط",
        status: "الحالة",
        actions: "الإجراءات",
        active: "نشط",
        archived: "مؤرشف",
        flagged: "مبلغ عنه",
        viewChat: "عرض المحادثة",
        archiveChat: "أرشفة المحادثة",
        deleteChat: "حذف المحادثة",
        flagChat: "الإبلاغ عن المحادثة",
        chatDetails: "تفاصيل المحادثة",
        messages: "الرسائل",
        totalTokens: "إجمالي التوكينز",
        averageResponseTime: "متوسط وقت الاستجابة",
        hasLegalCitations: "يحتوي على استشهادات قانونية",
      },
      legalCitations: {
        title: "إدارة الاستشهادات القانونية",
        articleNumber: "رقم المادة",
        title: "العنوان",
        content: "المحتوى",
        source: "المصدر",
        section: "القسم",
        category: "الفئة",
        tags: "الوسوم",
        usageCount: "عدد الاستخدام",
        lastUsed: "آخر استخدام",
        createdAt: "تاريخ الإنشاء",
        updatedAt: "تاريخ التحديث",
        actions: "الإجراءات",
        createCitation: "إنشاء استشهاد جديد",
        editCitation: "تعديل الاستشهاد",
        deleteCitation: "حذف الاستشهاد",
        citationDetails: "تفاصيل الاستشهاد",
        bulkImport: "استيراد جماعي",
      },
      contentModeration: {
        title: "مراقبة المحتوى",
        pendingReports: "التقارير المعلقة",
        approvedReports: "التقارير المعتمدة",
        rejectedReports: "التقارير المرفوضة",
        reportReason: "سبب الإبلاغ",
        reportDescription: "وصف الإبلاغ",
        reviewReport: "مراجعة التقرير",
        approveReport: "الموافقة على التقرير",
        rejectReport: "رفض التقرير",
        moderatorNotes: "ملاحظات المراجع",
      },
      ragManagement: {
        title: "إدارة نظام RAG",
        documents: "المستندات",
        vectorDatabase: "قاعدة البيانات المتجهة",
        searchQueries: "استعلامات البحث",
        uploadDocument: "رفع مستند",
        processDocument: "معالجة المستند",
        documentStatus: "حالة المستند",
        vectorCount: "عدد المتجهات",
        indexSize: "حجم الفهرس",
      },
      subscriptionManagement: {
        title: "إدارة الاشتراكات",
        activeSubscriptions: "الاشتراكات النشطة",
        expiredSubscriptions: "الاشتراكات المنتهية",
        subscriptionTier: "مستوى الاشتراك",
        subscriptionStatus: "حالة الاشتراك",
        monthlyRevenue: "الإيرادات الشهرية",
        upgradeSubscription: "ترقية الاشتراك",
        downgradeSubscription: "تخفيض الاشتراك",
        cancelSubscription: "إلغاء الاشتراك",
        renewSubscription: "تجديد الاشتراك",
      },
      subscriptionPackages: {
        title: "باقات الاشتراك",
        addPackage: "إضافة باقة جديدة",
        editPackage: "تعديل الباقة",
        deletePackage: "حذف الباقة",
        packageName: "اسم الباقة",
        monthlyPrice: "السعر الشهري",
        yearlyPrice: "السعر السنوي",
        maxChats: "الحد الأقصى للمحادثات",
        maxTokens: "الحد الأقصى للتوكينز",
        features: "الميزات المتاحة",
        blockedFeatures: "الميزات المحجوبة",
        isActive: "مفعلة",
        isDefault: "افتراضية",
        sortOrder: "ترتيب العرض",
        packageColor: "لون الباقة",
        subscribersCount: "عدد المشتركين",
        featureToggle: "تبديل الميزات",
        blockingMechanism: "آلية الحجب",
        renewalSettings: "إعدادات التجديد",
        paymentIntegration: "تكامل الدفع",
      },
      aiSettings: {
        title: "إعدادات الذكاء الاصطناعي",
        textModels: "النماذج النصية",
        voiceModels: "النماذج الصوتية",
        translation: "نظام الترجمة",
        legalAnalysis: "التحليل القانوني",
        performance: "مراقبة الأداء",
        integration: "التكامل والنسخ الاحتياطي",
        defaultModel: "النموذج الافتراضي",
        temperature: "الإبداعية",
        maxTokens: "الحد الأقصى للتوكينز",
        topP: "دقة التنبؤ",
        enableModelSwitching: "تمكين التبديل التلقائي",
        enableQualityMonitoring: "تمكين مراقبة الجودة",
      },
      llmServices: {
        title: "واجهات نماذج الذكاء",
        addApi: "إضافة واجهة جديدة",
        editApi: "تعديل واجهة",
        deleteApi: "حذف واجهة",
        apiName: "اسم الواجهة",
        apiEndpoint: "رابط الواجهة",
        apiType: "نوع الواجهة",
        apiConfig: "إعدادات الواجهة",
        apiKey: "مفتاح الواجهة",
        planLevel: "مستوى الاشتراك",
        isActive: "مفعلة",
        llmModels: "النماذج اللغوية",
        speechServices: "خدمات الصوت",
        visionServices: "خدمات الرؤية",
        analysisServices: "خدمات التحليل",
        translationServices: "خدمات الترجمة",
      },
      analytics: {
        title: "التحليلات والتقارير",
        usageDashboard: "لوحة الاستخدام",
        performanceMetrics: "مقاييس الأداء",
        userBehavior: "سلوك المستخدمين",
        contentAnalysis: "تحليل المحتوى",
        revenueTracking: "تتبع الإيرادات",
        customReports: "تقارير مخصصة",
        dataExport: "تصدير البيانات",
        dateRange: "النطاق الزمني",
        today: "اليوم",
        yesterday: "أمس",
        lastWeek: "الأسبوع الماضي",
        lastMonth: "الشهر الماضي",
        custom: "مخصص",
        applyFilter: "تطبيق الفلتر",
        resetFilter: "إعادة تعيين الفلتر",
      },
      settings: {
        title: "إعدادات النظام",
        generalSettings: "الإعدادات العامة",
        chatSettings: "إعدادات المحادثة",
        aiSettings: "إعدادات الذكاء الاصطناعي",
        securitySettings: "إعدادات الأمان",
        notificationSettings: "إعدادات الإشعارات",
        integrationSettings: "إعدادات التكامل",
        backupSettings: "إعدادات النسخ الاحتياطي",
        siteName: "اسم الموقع",
        siteDescription: "وصف الموقع",
        defaultLanguage: "اللغة الافتراضية",
        maintenanceMode: "وضع الصيانة",
        maxMessageLength: "الحد الأقصى لطول الرسالة",
        maxFileSize: "الحد الأقصى لحجم الملف",
        supportedFileTypes: "أنواع الملفات المدعومة",
        enableVoice: "تمكين الصوت",
        enableCitations: "تمكين الاستشهادات",
        defaultModel: "النموذج الافتراضي",
        maxTokensPerChat: "الحد الأقصى للتوكينز لكل محادثة",
        maxChatsPerUser: "الحد الأقصى للمحادثات لكل مستخدم",
        responseTimeout: "مهلة الاستجابة",
        sessionTimeout: "مهلة الجلسة",
        maxLoginAttempts: "الحد الأقصى لمحاولات تسجيل الدخول",
        enableTwoFactor: "تمكين المصادقة الثنائية",
        passwordPolicy: "سياسة كلمة المرور",
        saveSettings: "حفظ الإعدادات",
        resetSettings: "إعادة تعيين الإعدادات",
      },
      login: {
        title: "تسجيل الدخول",
        email: "البريد الإلكتروني",
        password: "كلمة المرور",
        rememberMe: "تذكرني",
        forgotPassword: "نسيت كلمة المرور؟",
        loginButton: "تسجيل الدخول",
        invalidCredentials: "بيانات الاعتماد غير صالحة",
      },
      chat: {
        typeMessage: "اكتب رسالتك هنا...",
        send: "إرسال",
        sending: "جاري الإرسال...",
        voice: "صوت",
        file: "ملف",
        stop: "توقف",
        stopRecording: "إيقاف التسجيل",
        error: "حدث خطأ أثناء الإرسال",
      },
    },
  },
  en: {
    translation: {
      common: {
        dashboard: "Dashboard",
        users: "Users",
        chats: "Chats",
        legalCitations: "Legal Citations",
        contentModeration: "Content Moderation",
        ragManagement: "RAG Management",
        subscriptionManagement: "Subscription Management",
        subscriptionPackages: "Subscription Packages",
        aiSettings: "AI Settings",
        llmServices: "LLM & Services",
        analytics: "Analytics",
        settings: "Settings",
        search: "Search...",
        logout: "Logout",
        profile: "Profile",
        language: "Language",
        theme: "Theme",
        light: "Light",
        dark: "Dark",
        system: "System",
        save: "Save",
        cancel: "Cancel",
        delete: "Delete",
        edit: "Edit",
        create: "Create",
        view: "View",
        close: "Close",
        loading: "Loading...",
        noData: "No data",
        error: "Error",
        success: "Success",
      },
      dashboard: {
        title: "Dashboard",
        totalUsers: "Total Users",
        activeUsers: "Active Users",
        totalChats: "Total Chats",
        totalTokens: "Total Tokens",
        recentActivity: "Recent Activity",
        viewAll: "View All",
        performance: "Performance",
        responseTime: "Response Time",
        errorRate: "Error Rate",
        uptime: "Uptime",
      },
      users: {
        title: "User Management",
        name: "Name",
        email: "Email",
        status: "Status",
        role: "Role",
        joinDate: "Join Date",
        lastActive: "Last Active",
        actions: "Actions",
        active: "Active",
        inactive: "Inactive",
        suspended: "Suspended",
        // Correct roles
        administrator: "Administrator",
        moderator: "Moderator",
        subscriber_enterprise: "Enterprise Subscriber",
        subscriber_premium: "Premium Subscriber",
        subscriber_basic: "Basic Subscriber",
        user: "Regular User",
        createUser: "Create New User",
        editUser: "Edit User",
        deleteUser: "Delete User",
        suspendUser: "Suspend User",
        activateUser: "Activate User",
        userDetails: "User Details",
        chatCount: "Chat Count",
        totalTokens: "Total Tokens",
      },
      chats: {
        title: "Chat Management",
        chatTitle: "Chat Title",
        user: "User",
        messageCount: "Message Count",
        createdAt: "Created At",
        lastActivity: "Last Activity",
        status: "Status",
        actions: "Actions",
        active: "Active",
        archived: "Archived",
        flagged: "Flagged",
        viewChat: "View Chat",
        archiveChat: "Archive Chat",
        deleteChat: "Delete Chat",
        flagChat: "Flag Chat",
        chatDetails: "Chat Details",
        messages: "Messages",
        totalTokens: "Total Tokens",
        averageResponseTime: "Average Response Time",
        hasLegalCitations: "Has Legal Citations",
      },
      legalCitations: {
        title: "Legal Citations Management",
        articleNumber: "Article Number",
        title: "Title",
        content: "Content",
        source: "Source",
        section: "Section",
        category: "Category",
        tags: "Tags",
        usageCount: "Usage Count",
        lastUsed: "Last Used",
        createdAt: "Created At",
        updatedAt: "Updated At",
        actions: "Actions",
        createCitation: "Create New Citation",
        editCitation: "Edit Citation",
        deleteCitation: "Delete Citation",
        citationDetails: "Citation Details",
        bulkImport: "Bulk Import",
      },
      contentModeration: {
        title: "Content Moderation",
        pendingReports: "Pending Reports",
        approvedReports: "Approved Reports",
        rejectedReports: "Rejected Reports",
        reportReason: "Report Reason",
        reportDescription: "Report Description",
        reviewReport: "Review Report",
        approveReport: "Approve Report",
        rejectReport: "Reject Report",
        moderatorNotes: "Moderator Notes",
      },
      ragManagement: {
        title: "RAG System Management",
        documents: "Documents",
        vectorDatabase: "Vector Database",
        searchQueries: "Search Queries",
        uploadDocument: "Upload Document",
        processDocument: "Process Document",
        documentStatus: "Document Status",
        vectorCount: "Vector Count",
        indexSize: "Index Size",
      },
      subscriptionManagement: {
        title: "Subscription Management",
        activeSubscriptions: "Active Subscriptions",
        expiredSubscriptions: "Expired Subscriptions",
        subscriptionTier: "Subscription Tier",
        subscriptionStatus: "Subscription Status",
        monthlyRevenue: "Monthly Revenue",
        upgradeSubscription: "Upgrade Subscription",
        downgradeSubscription: "Downgrade Subscription",
        cancelSubscription: "Cancel Subscription",
        renewSubscription: "Renew Subscription",
      },
      subscriptionPackages: {
        title: "Subscription Packages",
        addPackage: "Add New Package",
        editPackage: "Edit Package",
        deletePackage: "Delete Package",
        packageName: "Package Name",
        monthlyPrice: "Monthly Price",
        yearlyPrice: "Yearly Price",
        maxChats: "Max Chats",
        maxTokens: "Max Tokens",
        features: "Available Features",
        blockedFeatures: "Blocked Features",
        isActive: "Active",
        isDefault: "Default",
        sortOrder: "Sort Order",
        packageColor: "Package Color",
        subscribersCount: "Subscribers Count",
        featureToggle: "Feature Toggle",
        blockingMechanism: "Blocking Mechanism",
        renewalSettings: "Renewal Settings",
        paymentIntegration: "Payment Integration",
      },
      aiSettings: {
        title: "AI Settings",
        textModels: "Text Models",
        voiceModels: "Voice Models",
        translation: "Translation System",
        legalAnalysis: "Legal Analysis",
        performance: "Performance Monitoring",
        integration: "Integration & Backup",
        defaultModel: "Default Model",
        temperature: "Temperature",
        maxTokens: "Max Tokens",
        topP: "Top-P",
        enableModelSwitching: "Enable Auto Model Switching",
        enableQualityMonitoring: "Enable Quality Monitoring",
      },
      llmServices: {
        title: "LLM & Services",
        addApi: "Add New API",
        editApi: "Edit API",
        deleteApi: "Delete API",
        apiName: "API Name",
        apiEndpoint: "API Endpoint",
        apiType: "API Type",
        apiConfig: "API Configuration",
        apiKey: "API Key",
        planLevel: "Plan Level",
        isActive: "Active",
        llmModels: "LLM Models",
        speechServices: "Speech Services",
        visionServices: "Vision Services",
        analysisServices: "Analysis Services",
        translationServices: "Translation Services",
      },
      analytics: {
        title: "Analytics & Reports",
        usageDashboard: "Usage Dashboard",
        performanceMetrics: "Performance Metrics",
        userBehavior: "User Behavior",
        contentAnalysis: "Content Analysis",
        revenueTracking: "Revenue Tracking",
        customReports: "Custom Reports",
        dataExport: "Data Export",
        dateRange: "Date Range",
        today: "Today",
        yesterday: "Yesterday",
        lastWeek: "Last Week",
        lastMonth: "Last Month",
        custom: "Custom",
        applyFilter: "Apply Filter",
        resetFilter: "Reset Filter",
      },
      settings: {
        title: "System Settings",
        generalSettings: "General Settings",
        chatSettings: "Chat Settings",
        aiSettings: "AI Settings",
        securitySettings: "Security Settings",
        notificationSettings: "Notification Settings",
        integrationSettings: "Integration Settings",
        backupSettings: "Backup Settings",
        siteName: "Site Name",
        siteDescription: "Site Description",
        defaultLanguage: "Default Language",
        maintenanceMode: "Maintenance Mode",
        maxMessageLength: "Max Message Length",
        maxFileSize: "Max File Size",
        supportedFileTypes: "Supported File Types",
        enableVoice: "Enable Voice",
        enableCitations: "Enable Citations",
        defaultModel: "Default Model",
        maxTokensPerChat: "Max Tokens Per Chat",
        maxChatsPerUser: "Max Chats Per User",
        responseTimeout: "Response Timeout",
        sessionTimeout: "Session Timeout",
        maxLoginAttempts: "Max Login Attempts",
        enableTwoFactor: "Enable Two-Factor",
        passwordPolicy: "Password Policy",
        saveSettings: "Save Settings",
        resetSettings: "Reset Settings",
      },
      login: {
        title: "Login",
        email: "Email",
        password: "Password",
        rememberMe: "Remember Me",
        forgotPassword: "Forgot Password?",
        loginButton: "Login",
        invalidCredentials: "Invalid credentials",
      },
      chat: {
        typeMessage: "Type your message here...",
        send: "Send",
        sending: "Sending...",
        voice: "Voice",
        file: "File",
        stop: "Stop",
        stopRecording: "Stop Recording",
        error: "Error occurred while sending",
      },
    },
  },
};

// Initialize i18next
i18n.use(initReactI18next).init({
  resources,
  lng: "ar",
  interpolation: {
    escapeValue: false,
  },
});

export const I18nProvider = ({ children }: I18nProviderProps) => {
  const [language, setLanguage] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem("language") as Language;
    return savedLanguage || "ar";
  });

  const isRTL = language === "ar";

  // Apply text direction to HTML element
  useEffect(() => {
    document.documentElement.dir = isRTL ? "rtl" : "ltr";
    document.documentElement.lang = language;
  }, [isRTL, language]);

  // Change i18next language
  useEffect(() => {
    i18n.changeLanguage(language);
    localStorage.setItem("language", language);
  }, [language]);

  return (
    <I18nContext.Provider
      value={{
        language,
        setLanguage,
        isRTL,
      }}
    >
      {children}
    </I18nContext.Provider>
  );
};
import React, { useEffect, useState } from 'react';
import { <PERSON>ert, Progress, Button, Space, Typography } from 'antd';
import { WarningOutlined, ArrowUpOutlined, BarChartOutlined } from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/auth-context';

const { Text } = Typography;

interface TokenUsageAlertProps {
  currentUsage: number;
  maxAllowed: number;
  resetDate: string;
  onClose?: () => void;
}

export const TokenUsageAlert: React.FC<TokenUsageAlertProps> = ({
  currentUsage,
  maxAllowed,
  resetDate,
  onClose
}) => {
  const { language } = useTranslation();
  const navigate = useNavigate();
  const { profile } = useAuth();
  const [shouldShow, setShouldShow] = useState(false);
  
  // Calculate percentage
  const usagePercentage = Math.round((currentUsage / maxAllowed) * 100);
  
  // Format numbers with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US').format(num);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Check if we should show the alert based on user role
  useEffect(() => {
    // Only show for regular users, not for administrators
    if (profile) {
      const isAdmin = profile.role === 'administrator';
      const isHighUsage = usagePercentage >= 90;
      
      // Show alert if:
      // 1. User is not an admin
      // 2. Usage is high (>=90%)
      setShouldShow(!isAdmin && isHighUsage);
    }
  }, [profile, usagePercentage]);

  // If we shouldn't show the alert, return null
  if (!shouldShow) {
    return null;
  }
  
  return (
    <Alert
      type="warning"
      showIcon
      icon={<WarningOutlined />}
      message={language === 'ar' ? "تحذير استخدام" : "Usage Warning"}
      description={
        <div className="space-y-3">
          <p>
            {language === 'ar' 
              ? `لقد وصلت إلى ${usagePercentage}% من حد التوكينز الشهري المخصص لك. عند الوصول إلى 100%، سيتم تقييد استخدامك للنظام.`
              : `You have reached ${usagePercentage}% of your monthly token limit. When you reach 100%, your system usage will be restricted.`
            }
          </p>
          
          <div className="mt-2">
            <Progress 
              percent={usagePercentage} 
              status={usagePercentage > 95 ? "exception" : "active"}
              strokeColor={usagePercentage > 95 ? "#ff4d4f" : "#faad14"}
            />
            <div className="flex justify-between text-sm mt-1">
              <Text>{formatNumber(currentUsage)} {language === 'ar' ? "توكن" : "tokens"}</Text>
              <Text>{formatNumber(maxAllowed)} {language === 'ar' ? "توكن" : "tokens"}</Text>
            </div>
          </div>
          
          <div className="text-sm text-gray-500 mt-2">
            {language === 'ar' 
              ? `سيتم إعادة تعيين الحد في ${formatDate(resetDate)}`
              : `Limit will reset on ${formatDate(resetDate)}`
            }
          </div>
          
          <Space className="mt-3">
            <Button 
              type="primary" 
              icon={<ArrowUpOutlined />}
              onClick={() => navigate('/subscription-packages')}
            >
              {language === 'ar' ? "ترقية الاشتراك" : "Upgrade Subscription"}
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => navigate('/analytics')}
            >
              {language === 'ar' ? "عرض الاستخدام" : "View Usage"}
            </Button>
          </Space>
        </div>
      }
      closable
      onClose={onClose}
      className="mb-4"
    />
  );
};
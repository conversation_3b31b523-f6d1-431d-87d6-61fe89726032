import React, { useEffect, useState } from 'react';
import { Modal, Button, Alert, Spin, Typography, Space, Divider } from 'antd';
import { CreditCardOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';
import { api } from '../../lib/api';

const { Title, Text, Paragraph } = Typography;

interface PaymentModalProps {
  visible: boolean;
  onClose: () => void;
  packageId: string;
  packageName: string;
  amount: number;
  onPaymentSuccess?: () => void;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  visible,
  onClose,
  packageId,
  packageName,
  amount,
  onPaymentSuccess
}) => {
  const { language } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'success' | 'failed'>('pending');
  const [transactionUrl, setTransactionUrl] = useState<string | null>(null);

  // Initialize payment when modal opens
  useEffect(() => {
    if (visible && !paymentId) {
      initializePayment();
    }
  }, [visible]);

  // Check payment status periodically
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (paymentId && paymentStatus === 'pending') {
      interval = setInterval(() => {
        checkPaymentStatus(paymentId);
      }, 3000); // Check every 3 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [paymentId, paymentStatus]);

  const initializePayment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post('/api/v1/payments/initiate', {
        plan: packageId,
        payment_method: 'creditcard',
        callback_url: `${window.location.origin}/payment-callback`
      });
      
      setPaymentId(response.data.payment_id);
      setTransactionUrl(response.data.transaction_url);
      
      // Initialize Moyasar form if the script is loaded
      if (window.Moyasar) {
        window.Moyasar.init({
          element: '.moyasar-form',
          amount: amount * 100, // Convert to halalas
          currency: 'SAR',
          description: `اشتراك في باقة ${packageName}`,
          publishable_api_key: import.meta.env.VITE_MOYASAR_PUBLISHABLE_KEY,
          callback_url: `${window.location.origin}/payment-callback`,
          methods: ['creditcard'],
          on_completed: (payment: any) => {
            // This will be called when payment is completed
            setPaymentStatus('success');
            if (onPaymentSuccess) onPaymentSuccess();
          }
        });
      } else {
        setError('لم يتم تحميل مكتبة Moyasar بشكل صحيح');
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'حدث خطأ أثناء إنشاء عملية الدفع');
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (id: string) => {
    try {
      const response = await api.get(`/api/v1/payments/status/${id}`);
      
      if (response.data.status === 'paid') {
        setPaymentStatus('success');
        if (onPaymentSuccess) onPaymentSuccess();
      } else if (response.data.status === 'failed') {
        setPaymentStatus('failed');
      }
    } catch (err) {
      console.error('Error checking payment status:', err);
    }
  };

  const handleClose = () => {
    // Reset state
    setPaymentId(null);
    setPaymentStatus('pending');
    setTransactionUrl(null);
    setError(null);
    onClose();
  };

  const renderPaymentForm = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <Spin size="large" />
          <Text className="mt-4">{language === 'ar' ? 'جاري تهيئة عملية الدفع...' : 'Initializing payment...'}</Text>
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message={language === 'ar' ? 'خطأ في عملية الدفع' : 'Payment Error'}
          description={error}
          type="error"
          showIcon
          action={
            <Button onClick={initializePayment}>
              {language === 'ar' ? 'إعادة المحاولة' : 'Try Again'}
            </Button>
          }
        />
      );
    }

    if (paymentStatus === 'success') {
      return (
        <div className="text-center py-8">
          <CheckCircleOutlined className="text-6xl text-green-500 mb-4" />
          <Title level={3}>{language === 'ar' ? 'تمت عملية الدفع بنجاح!' : 'Payment Successful!'}</Title>
          <Paragraph>
            {language === 'ar' 
              ? 'تم تفعيل اشتراكك بنجاح. يمكنك الآن الاستمتاع بجميع مزايا الباقة.' 
              : 'Your subscription has been activated successfully. You can now enjoy all the features of the package.'}
          </Paragraph>
          <Button type="primary" onClick={handleClose}>
            {language === 'ar' ? 'العودة' : 'Return'}
          </Button>
        </div>
      );
    }

    if (paymentStatus === 'failed') {
      return (
        <div className="text-center py-8">
          <CloseCircleOutlined className="text-6xl text-red-500 mb-4" />
          <Title level={3}>{language === 'ar' ? 'فشلت عملية الدفع' : 'Payment Failed'}</Title>
          <Paragraph>
            {language === 'ar' 
              ? 'لم تتم عملية الدفع بنجاح. يرجى التحقق من بيانات البطاقة والمحاولة مرة أخرى.' 
              : 'The payment process was not successful. Please check your card details and try again.'}
          </Paragraph>
          <Space>
            <Button type="primary" onClick={initializePayment}>
              {language === 'ar' ? 'إعادة المحاولة' : 'Try Again'}
            </Button>
            <Button onClick={handleClose}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
          </Space>
        </div>
      );
    }

    return (
      <div>
        <div className="mb-4">
          <div className="flex justify-between mb-2">
            <Text>{language === 'ar' ? 'الباقة:' : 'Package:'}</Text>
            <Text strong>{packageName}</Text>
          </div>
          <div className="flex justify-between mb-2">
            <Text>{language === 'ar' ? 'المبلغ:' : 'Amount:'}</Text>
            <Text strong>{amount} {language === 'ar' ? 'ريال' : 'SAR'}</Text>
          </div>
          <div className="flex justify-between mb-2">
            <Text>{language === 'ar' ? 'ضريبة القيمة المضافة (15%):' : 'VAT (15%):'}</Text>
            <Text strong>{(amount * 0.15).toFixed(2)} {language === 'ar' ? 'ريال' : 'SAR'}</Text>
          </div>
          <Divider />
          <div className="flex justify-between mb-2">
            <Text strong>{language === 'ar' ? 'الإجمالي:' : 'Total:'}</Text>
            <Text strong>{(amount * 1.15).toFixed(2)} {language === 'ar' ? 'ريال' : 'SAR'}</Text>
          </div>
        </div>
        
        <Alert
          message={language === 'ar' ? 'معلومات آمنة' : 'Secure Information'}
          description={language === 'ar' 
            ? 'جميع بيانات البطاقة مشفرة ومحمية. نحن لا نخزن بيانات بطاقتك.' 
            : 'All card data is encrypted and protected. We do not store your card details.'}
          type="info"
          showIcon
          className="mb-4"
        />
        
        <div className="moyasar-form"></div>
        
        {transactionUrl && (
          <div className="mt-4 text-center">
            <Text className="text-gray-500">
              {language === 'ar' 
                ? 'إذا لم يظهر نموذج الدفع، يمكنك' 
                : 'If the payment form does not appear, you can'}
            </Text>
            <Button type="link" href={transactionUrl} target="_blank">
              {language === 'ar' ? 'الانتقال إلى صفحة الدفع' : 'Go to payment page'}
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className="flex items-center">
          <CreditCardOutlined className="mr-2" />
          {language === 'ar' ? 'إتمام عملية الدفع' : 'Complete Payment'}
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      centered
    >
      {renderPaymentForm()}
    </Modal>
  );
};

// Add TypeScript declaration for Moyasar
declare global {
  interface Window {
    Moyasar: {
      init: (config: {
        element: string;
        amount: number;
        currency: string;
        description: string;
        publishable_api_key: string;
        callback_url: string;
        methods: string[];
        on_completed?: (payment: any) => void;
      }) => void;
    };
  }
}
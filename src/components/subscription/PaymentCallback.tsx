import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Result, <PERSON><PERSON>, Spin } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';
import { paymentService } from '../../services/payment-service';

export const PaymentCallback: React.FC = () => {
  const { language } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkPaymentStatus = async () => {
      try {
        // Get payment ID from URL query parameters
        const params = new URLSearchParams(location.search);
        const id = params.get('id');
        
        if (!id) {
          setStatus('error');
          setError(language === 'ar' ? 'معرف الدفع غير موجود' : 'Payment ID is missing');
          return;
        }
        
        // Check payment status
        const response = await paymentService.checkPaymentStatus(id);
        
        if (response.status === 'paid') {
          setStatus('success');
        } else {
          setStatus('error');
          setError(language === 'ar' ? 'فشلت عملية الدفع' : 'Payment failed');
        }
      } catch (err) {
        console.error('Error checking payment status:', err);
        setStatus('error');
        setError(language === 'ar' ? 'حدث خطأ أثناء التحقق من حالة الدفع' : 'Error checking payment status');
      }
    };

    checkPaymentStatus();
  }, [location.search, language]);

  const handleGoToSubscription = () => {
    navigate('/subscription');
  };

  const handleTryAgain = () => {
    navigate('/subscription-packages');
  };

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Spin size="large" />
        <p className="mt-4">
          {language === 'ar' ? 'جاري التحقق من حالة الدفع...' : 'Verifying payment status...'}
        </p>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <Result
        icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
        title={language === 'ar' ? 'تمت عملية الدفع بنجاح!' : 'Payment Successful!'}
        subTitle={language === 'ar' 
          ? 'تم تفعيل اشتراكك بنجاح. يمكنك الآن الاستمتاع بجميع مزايا الباقة.' 
          : 'Your subscription has been activated successfully. You can now enjoy all the features of the package.'}
        extra={[
          <Button type="primary" key="subscription" onClick={handleGoToSubscription}>
            {language === 'ar' ? 'عرض الاشتراك' : 'View Subscription'}
          </Button>,
        ]}
      />
    );
  }

  return (
    <Result
      status="error"
      title={language === 'ar' ? 'فشلت عملية الدفع' : 'Payment Failed'}
      subTitle={error || (language === 'ar' 
        ? 'لم تتم عملية الدفع بنجاح. يرجى المحاولة مرة أخرى.' 
        : 'The payment process was not successful. Please try again.')}
      extra={[
        <Button type="primary" key="tryAgain" onClick={handleTryAgain}>
          {language === 'ar' ? 'المحاولة مرة أخرى' : 'Try Again'}
        </Button>,
        <Button key="home" onClick={() => navigate('/')}>
          {language === 'ar' ? 'العودة للرئيسية' : 'Back to Home'}
        </Button>,
      ]}
    />
  );
};
import React, { useState } from 'react';
import { Card, Button, Tag, List, Typography, Space, Badge } from 'antd';
import { CheckOutlined, CloseOutlined, CrownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';
import { useAuth } from '../../contexts/auth-context';
import { formatNumber } from '../../lib/utils';
import { PaymentModal } from './PaymentModal';

const { Title, Text } = Typography;

interface SubscriptionPackage {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number | null;
  maxChatsPerMonth: number;
  maxTokensPerMonth: number;
  features: string[];
  blockedFeatures: string[];
  isActive: boolean;
  isDefault: boolean;
  color: string;
}

interface SubscriptionCardProps {
  package: SubscriptionPackage;
  currentPackage?: boolean;
  onSubscriptionChange?: () => void;
}

export const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  package: pkg,
  currentPackage = false,
  onSubscriptionChange
}) => {
  const { language } = useTranslation();
  const { profile } = useAuth();
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  const getPackageColor = (color: string) => {
    switch (color) {
      case 'blue':
        return 'blue';
      case 'purple':
        return 'purple';
      case 'gold':
        return 'gold';
      case 'green':
        return 'green';
      case 'red':
        return 'red';
      default:
        return 'default';
    }
  };

  const renderPrice = (price: number, yearly: boolean = false) => {
    if (price === 0) return language === 'ar' ? 'مجاني' : 'Free';
    if (price === -1) return language === 'ar' ? 'حسب الطلب' : 'Custom';
    return `${formatNumber(price)} ${language === 'ar' ? 'ريال' : 'SAR'}${yearly ? (language === 'ar' ? '/سنة' : '/year') : (language === 'ar' ? '/شهر' : '/month')}`;
  };

  const renderLimit = (limit: number, unit: string) => {
    if (limit === -1) return language === 'ar' ? 'غير محدود' : 'Unlimited';
    return `${formatNumber(limit)} ${unit}`;
  };

  const handleSubscribe = () => {
    // Check if user is logged in
    if (!profile) {
      // Redirect to login page
      window.location.href = '/login';
      return;
    }

    // Open payment modal
    setPaymentModalVisible(true);
  };

  const handlePaymentSuccess = () => {
    // Close payment modal
    setPaymentModalVisible(false);
    
    // Notify parent component about subscription change
    if (onSubscriptionChange) {
      onSubscriptionChange();
    }
  };

  return (
    <Badge.Ribbon 
      text={currentPackage ? (language === 'ar' ? 'الباقة الحالية' : 'Current Plan') : null} 
      color="green"
      style={{ display: currentPackage ? 'block' : 'none' }}
    >
      <Card
        className={`h-full transition-all duration-300 ${currentPackage ? 'border-green-500 shadow-md' : 'hover:shadow-md'}`}
        title={
          <div className="flex items-center justify-between">
            <Tag color={getPackageColor(pkg.color)} className="text-lg px-3 py-1">
              {pkg.isDefault && <CrownOutlined className="mr-1" />}
              {pkg.name}
            </Tag>
            {pkg.yearlyPrice && (
              <Tag color="green">
                {language === 'ar' ? 'وفر 2 شهر' : 'Save 2 months'}
              </Tag>
            )}
          </div>
        }
        actions={[
          <div key="subscribe" className="px-4 pb-4">
            <Button
              type={currentPackage ? 'default' : 'primary'}
              size="large"
              block
              onClick={handleSubscribe}
              disabled={currentPackage}
              icon={currentPackage ? undefined : <ArrowUpOutlined />}
            >
              {currentPackage 
                ? (language === 'ar' ? 'الباقة الحالية' : 'Current Plan')
                : (language === 'ar' ? 'اشترك الآن' : 'Subscribe Now')}
            </Button>
          </div>
        ]}
      >
        <div className="mb-6 text-center">
          <Title level={2} className="mb-0">
            {renderPrice(pkg.monthlyPrice)}
          </Title>
          {pkg.yearlyPrice && (
            <Text type="secondary" className="block mt-1">
              {language === 'ar' ? 'سنوياً: ' : 'Yearly: '}{renderPrice(pkg.yearlyPrice, true)}
            </Text>
          )}
        </div>

        <div className="mb-6">
          <Space direction="vertical" className="w-full">
            <div>
              <Text strong>{language === 'ar' ? 'المحادثات/الشهر:' : 'Chats/Month:'}</Text>
              <div className="text-lg">{renderLimit(pkg.maxChatsPerMonth, language === 'ar' ? 'محادثة' : 'chats')}</div>
            </div>
            <div>
              <Text strong>{language === 'ar' ? 'التوكينز/الشهر:' : 'Tokens/Month:'}</Text>
              <div className="text-lg">{renderLimit(pkg.maxTokensPerMonth, language === 'ar' ? 'توكن' : 'tokens')}</div>
            </div>
          </Space>
        </div>

        <div>
          <Title level={5}>{language === 'ar' ? 'الميزات المتاحة:' : 'Available Features:'}</Title>
          <List
            dataSource={pkg.features}
            renderItem={item => (
              <List.Item className="py-1 px-0">
                <div className="flex items-start">
                  <CheckOutlined className="text-green-500 mt-1 mr-2" />
                  <div>{item}</div>
                </div>
              </List.Item>
            )}
          />
        </div>

        {pkg.blockedFeatures.length > 0 && (
          <div className="mt-4">
            <Title level={5}>{language === 'ar' ? 'الميزات غير المتاحة:' : 'Unavailable Features:'}</Title>
            <List
              dataSource={pkg.blockedFeatures}
              renderItem={item => (
                <List.Item className="py-1 px-0">
                  <div className="flex items-start">
                    <CloseOutlined className="text-red-500 mt-1 mr-2" />
                    <div className="text-gray-500">{item}</div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}

        {/* Payment Modal */}
        <PaymentModal
          visible={paymentModalVisible}
          onClose={() => setPaymentModalVisible(false)}
          packageId={pkg.id}
          packageName={pkg.name}
          amount={pkg.monthlyPrice}
          onPaymentSuccess={handlePaymentSuccess}
        />
      </Card>
    </Badge.Ribbon>
  );
};
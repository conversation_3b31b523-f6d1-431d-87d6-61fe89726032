import { useState } from "react";
import { Layout, Menu, theme } from "antd";
import {
  DashboardOutlined,
  UserOutlined,
  MessageOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  FlagOutlined,
  DatabaseOutlined,
  CrownOutlined,
  RobotOutlined,
  AppstoreOutlined,
  ApiOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../../hooks/use-translation";
import { useNavigate, useLocation } from "react-router-dom";
import { useTheme } from "../../hooks/use-theme";

const { Sider: AntdSider } = Layout;
const { useToken } = theme;

export const Sider = () => {
  const { token } = useToken();
  const { t } = useTranslation();
  const { actualTheme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === "/") return ["dashboard"];
    if (path.startsWith("/users")) return ["users"];
    if (path.startsWith("/chats")) return ["chats"];
    if (path.startsWith("/legal-citations")) return ["legal-citations"];
    if (path.startsWith("/content-moderation")) return ["content-moderation"];
    if (path.startsWith("/rag-management")) return ["rag-management"];
    if (path.startsWith("/subscription-management")) return ["subscription-management"];
    if (path.startsWith("/subscription-packages")) return ["subscription-packages"];
    if (path.startsWith("/ai-settings")) return ["ai-settings"];
    if (path.startsWith("/llm-services")) return ["llm-services"];
    if (path.startsWith("/analytics")) return ["analytics"];
    if (path.startsWith("/settings")) return ["settings"];
    return [];
  };

  const menuItems = [
    {
      key: "dashboard",
      icon: <DashboardOutlined />,
      label: t("common.dashboard"),
      onClick: () => navigate("/"),
    },
    {
      key: "users",
      icon: <UserOutlined />,
      label: t("common.users"),
      onClick: () => navigate("/users"),
    },
    {
      key: "chats",
      icon: <MessageOutlined />,
      label: t("common.chats"),
      onClick: () => navigate("/chats"),
    },
    {
      key: "legal-citations",
      icon: <FileTextOutlined />,
      label: t("common.legalCitations"),
      onClick: () => navigate("/legal-citations"),
    },
    {
      key: "content-moderation",
      icon: <FlagOutlined />,
      label: t("common.contentModeration"),
      onClick: () => navigate("/content-moderation"),
    },
    {
      key: "rag-management",
      icon: <DatabaseOutlined />,
      label: t("common.ragManagement"),
      onClick: () => navigate("/rag-management"),
    },
    {
      key: "subscription-management",
      icon: <CrownOutlined />,
      label: t("common.subscriptionManagement"),
      onClick: () => navigate("/subscription-management"),
    },
    {
      key: "subscription-packages",
      icon: <AppstoreOutlined />,
      label: "باقات الاشتراك",
      onClick: () => navigate("/subscription-packages"),
    },
    {
      key: "ai-settings",
      icon: <RobotOutlined />,
      label: t("common.aiSettings"),
      onClick: () => navigate("/ai-settings"),
    },
    {
      key: "llm-services",
      icon: <ApiOutlined />,
      label: "واجهات نماذج الذكاء",
      onClick: () => navigate("/llm-services"),
    },
    {
      key: "analytics",
      icon: <BarChartOutlined />,
      label: t("common.analytics"),
      onClick: () => navigate("/analytics"),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: t("common.settings"),
      onClick: () => navigate("/settings"),
    },
  ];

  // Determine the appropriate logo based on theme
  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";

  return (
    <AntdSider
      collapsible
      collapsed={collapsed}
      onCollapse={(value) => setCollapsed(value)}
      style={{
        background: token.colorBgContainer,
        borderRight: `1px solid ${token.colorBorderSecondary}`,
      }}
      width={260}
      theme="light"
    >
      <div
        style={{
          height: "64px",
          display: "flex",
          alignItems: "center",
          justifyContent: collapsed ? "center" : "flex-start",
          padding: collapsed ? "0" : "0 16px",
        }}
      >
        {!collapsed && (
          <img
            src={logoSrc}
            alt="شُريح"
            className="logo-component"
            style={{ height: "40px" }}
          />
        )}
      </div>
      <Menu
        mode="inline"
        selectedKeys={getSelectedKeys()}
        style={{ border: "none" }}
        items={menuItems}
      />
    </AntdSider>
  );
};
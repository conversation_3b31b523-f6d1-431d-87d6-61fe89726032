import { useTheme } from "../../hooks/use-theme";

export const Title = () => {
  const { actualTheme } = useTheme();
  
  const logoSrc = actualTheme === "dark" 
    ? "/Shuraih.Logo_DarkMode.svg" 
    : "/ShuraihAIUI.svg";
  
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "64px",
      }}
    >
      <img
        src={logoSrc}
        alt="شُريح"
        className="logo-component"
        style={{ height: "40px" }}
      />
    </div>
  );
};
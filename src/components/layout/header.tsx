import {
  Layout,
  Avatar,
  Dropdown,
  Space,
  Button,
  Input,
  Badge,
  theme,
  List,
  Typography,
  Popover,
  Divider,
  Empty,
  Modal,
  Tag,
} from "antd";
import {
  UserOutlined,
  SearchOutlined,
  BellOutlined,
  SettingOutlined,
  LogoutOutlined,
  GlobalOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  ProfileOutlined,
  LinkOutlined,
  CheckOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "../../hooks/use-translation";
import { useTheme } from "../../hooks/use-theme";
import { useAuth } from "../../contexts/auth-context";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";

const { Header: AntdHeader } = Layout;
const { useToken } = theme;
const { Text, Paragraph, Title } = Typography;

// Sample notifications data
const sampleNotifications = [
  {
    id: "1",
    title: "تحديث النظام",
    message: "تم تحديث النظام إلى الإصدار الجديد 2.0",
    time: new Date(Date.now() - 30 * 60000),
    read: false,
    type: "info",
    details: "تم تحديث النظام إلى الإصدار الجديد 2.0 مع إضافة العديد من الميزات الجديدة مثل دعم اللغة العربية المحسن، وتحسينات في واجهة المستخدم، وإصلاحات للأخطاء المعروفة. يرجى تحديث التطبيق الخاص بك للاستفادة من هذه التحسينات.",
    actions: [
      { label: "عرض التغييرات", url: "/settings" },
      { label: "تجاهل", action: "dismiss" }
    ]
  },
  {
    id: "2",
    title: "اشتراك جديد",
    message: "تم تفعيل اشتراكك في الباقة المتقدمة",
    time: new Date(Date.now() - 2 * 60 * 60000),
    read: false,
    type: "success",
    details: "تم تفعيل اشتراكك في الباقة المتقدمة بنجاح. يمكنك الآن الاستفادة من جميع الميزات المتقدمة مثل المحادثة الصوتية، ورفع الملفات، والوصول إلى النماذج المتقدمة. ستبدأ فترة الاشتراك من اليوم وتستمر لمدة شهر واحد.",
    actions: [
      { label: "عرض تفاصيل الاشتراك", url: "/subscription-management" }
    ]
  },
  {
    id: "3",
    title: "تنبيه أمان",
    message: "تم تسجيل الدخول إلى حسابك من جهاز جديد",
    time: new Date(Date.now() - 5 * 60 * 60000),
    read: true,
    type: "warning",
    details: "تم تسجيل الدخول إلى حسابك من جهاز جديد في تمام الساعة 10:30 صباحًا. إذا لم تكن أنت من قام بتسجيل الدخول، يرجى تغيير كلمة المرور الخاصة بك على الفور وتفعيل المصادقة الثنائية لحماية حسابك.",
    metadata: {
      device: "iPhone 13",
      location: "الرياض، المملكة العربية السعودية",
      ip: "***********",
      browser: "Safari"
    },
    actions: [
      { label: "تغيير كلمة المرور", url: "/account/profile" },
      { label: "تفعيل المصادقة الثنائية", url: "/settings" }
    ]
  },
  {
    id: "4",
    title: "تحذير استخدام",
    message: "لقد وصلت إلى 90% من حد التوكينز الشهري",
    time: new Date(Date.now() - 1 * 24 * 60 * 60000),
    read: true,
    type: "error",
    details: "لقد وصلت إلى 90% من حد التوكينز الشهري المخصص لك. عند الوصول إلى 100%، سيتم تقييد استخدامك للنظام حتى بداية الشهر القادم أو حتى تقوم بترقية اشتراكك إلى باقة أعلى.",
    metadata: {
      currentUsage: "9,000",
      limit: "10,000",
      resetDate: "2024-07-31"
    },
    actions: [
      { label: "ترقية الاشتراك", url: "/subscription-packages" },
      { label: "عرض الاستخدام", url: "/analytics" }
    ]
  },
];

export const Header = () => {
  const { token } = useToken();
  const { t, language, setLanguage, isRTL } = useTranslation();
  const { actualTheme, toggleTheme } = useTheme();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState(sampleNotifications);
  const [notificationOpen, setNotificationOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [notificationDetailOpen, setNotificationDetailOpen] = useState(false);

  const unreadCount = notifications.filter(n => !n.read).length;

  const languageItems = [
    {
      key: "ar",
      label: "العربية",
      onClick: () => setLanguage("ar"),
    },
    {
      key: "en",
      label: "English",
      onClick: () => setLanguage("en"),
    },
  ];

  const userMenuItems = [
    {
      key: "profile",
      icon: <ProfileOutlined />,
      label: language === "ar" ? "الملف الشخصي" : "Profile",
      onClick: () => navigate('/account/profile'),
    },
    {
      key: "linked-accounts",
      icon: <LinkOutlined />,
      label: language === "ar" ? "الحسابات المرتبطة" : "Linked Accounts",
      onClick: () => navigate('/account/linked-accounts'),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: t("common.settings"),
      onClick: () => navigate('/settings'),
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: t("common.logout"),
      onClick: () => navigate('/login'),
    },
  ];

  const formatNotificationTime = (time) => {
    const now = new Date();
    const diff = now.getTime() - time.getTime();
    
    // Less than a minute
    if (diff < 60000) {
      return language === "ar" ? "الآن" : "Just now";
    }
    
    // Less than an hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return language === "ar" 
        ? `منذ ${minutes} ${minutes === 1 ? "دقيقة" : "دقائق"}`
        : `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
    }
    
    // Less than a day
    if (diff < ********) {
      const hours = Math.floor(diff / 3600000);
      return language === "ar"
        ? `منذ ${hours} ${hours === 1 ? "ساعة" : "ساعات"}`
        : `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
    }
    
    // More than a day
    const days = Math.floor(diff / ********);
    return language === "ar"
      ? `منذ ${days} ${days === 1 ? "يوم" : "أيام"}`
      : `${days} ${days === 1 ? "day" : "days"} ago`;
  };

  const formatFullDateTime = (time) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true
    };
    
    return new Date(time).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', options);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case "info":
        return <InfoCircleOutlined style={{ color: "#1890ff" }} />;
      case "success":
        return <CheckOutlined style={{ color: "#52c41a" }} />;
      case "warning":
        return <WarningOutlined style={{ color: "#faad14" }} />;
      case "error":
        return <ExclamationCircleOutlined style={{ color: "#f5222d" }} />;
      default:
        return <InfoCircleOutlined style={{ color: "#1890ff" }} />;
    }
  };

  const getNotificationTypeText = (type) => {
    switch (type) {
      case "info":
        return language === "ar" ? "معلومات" : "Information";
      case "success":
        return language === "ar" ? "نجاح" : "Success";
      case "warning":
        return language === "ar" ? "تحذير" : "Warning";
      case "error":
        return language === "ar" ? "خطأ" : "Error";
      default:
        return language === "ar" ? "معلومات" : "Information";
    }
  };

  const getNotificationTypeColor = (type) => {
    switch (type) {
      case "info":
        return "blue";
      case "success":
        return "green";
      case "warning":
        return "orange";
      case "error":
        return "red";
      default:
        return "blue";
    }
  };

  const markAsRead = (id) => {
    setNotifications(
      notifications.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map(notification => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id) => {
    setNotifications(
      notifications.filter(notification => notification.id !== id)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const handleNotificationClick = (notification) => {
    // Mark as read
    markAsRead(notification.id);
    
    // Close notification popover
    setNotificationOpen(false);
    
    // Set selected notification and open detail modal
    setSelectedNotification(notification);
    setNotificationDetailOpen(true);
  };

  const handleActionClick = (action, notificationId) => {
    if (action.url) {
      navigate(action.url);
      setNotificationDetailOpen(false);
    } else if (action.action === 'dismiss') {
      setNotificationDetailOpen(false);
    }
  };

  const notificationContent = (
    <div style={{ width: 350, maxHeight: 400 }}>
      <div className="flex justify-between items-center mb-2">
        <Text strong>{language === "ar" ? "الإشعارات" : "Notifications"}</Text>
        <div>
          {unreadCount > 0 && (
            <Button 
              type="link" 
              size="small" 
              onClick={markAllAsRead}
            >
              {language === "ar" ? "تعيين الكل كمقروء" : "Mark all as read"}
            </Button>
          )}
          {notifications.length > 0 && (
            <Button 
              type="link" 
              size="small" 
              danger 
              onClick={clearAllNotifications}
            >
              {language === "ar" ? "مسح الكل" : "Clear all"}
            </Button>
          )}
        </div>
      </div>
      
      <Divider style={{ margin: "8px 0" }} />
      
      {notifications.length === 0 ? (
        <Empty 
          description={language === "ar" ? "لا توجد إشعارات" : "No notifications"} 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      ) : (
        <List
          dataSource={notifications}
          renderItem={item => (
            <List.Item
              className={`cursor-pointer transition-colors ${!item.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
              onClick={() => handleNotificationClick(item)}
              actions={[
                <Button 
                  type="text" 
                  size="small" 
                  danger
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteNotification(item.id);
                  }}
                >
                  <CloseOutlined />
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={getNotificationIcon(item.type)}
                title={
                  <div className="flex justify-between">
                    <Text strong>{item.title}</Text>
                    <Text type="secondary" className="text-xs">
                      {formatNotificationTime(item.time)}
                    </Text>
                  </div>
                }
                description={item.message}
              />
            </List.Item>
          )}
        />
      )}
    </div>
  );

  return (
    <AntdHeader
      style={{
        background: token.colorBgContainer,
        padding: "0 24px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        boxShadow: "none",
      }}
    >
      <div className="flex items-center">
        <Input
          prefix={<SearchOutlined />}
          placeholder={t("common.search")}
          style={{ width: 250, marginRight: isRTL ? 0 : 16, marginLeft: isRTL ? 16 : 0 }}
        />
      </div>
      <div className="flex items-center gap-4">
        <Button
          type="text"
          icon={actualTheme === "dark" ? <SunOutlined /> : <MoonOutlined />}
          onClick={toggleTheme}
          title={actualTheme === "dark" ? t("common.light") : t("common.dark")}
        />
        
        <Dropdown
          menu={{
            items: languageItems,
            selectedKeys: [language],
          }}
          placement="bottomRight"
        >
          <Button type="text" icon={<GlobalOutlined />}>
            {language === "ar" ? "العربية" : "English"}
          </Button>
        </Dropdown>
        
        <Popover
          content={notificationContent}
          trigger="click"
          placement="bottomRight"
          open={notificationOpen}
          onOpenChange={setNotificationOpen}
          overlayClassName="notification-popover"
        >
          <Badge count={unreadCount}>
            <Button
              type="text"
              icon={<BellOutlined />}
            />
          </Badge>
        </Popover>
        
        <Dropdown
          menu={{
            items: userMenuItems,
          }}
          placement="bottomRight"
        >
          <Space className="cursor-pointer">
            <Avatar icon={<UserOutlined />} />
            <span className="hidden md:inline">
              {language === "ar" ? (profile?.full_name || 'مطور النظام') : (profile?.full_name || 'System Developer')}
            </span>
          </Space>
        </Dropdown>
      </div>

      {/* Notification Detail Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            {selectedNotification && getNotificationIcon(selectedNotification.type)}
            <span>{selectedNotification?.title}</span>
            <Tag color={selectedNotification ? getNotificationTypeColor(selectedNotification.type) : 'blue'}>
              {selectedNotification ? getNotificationTypeText(selectedNotification.type) : ''}
            </Tag>
          </div>
        }
        open={notificationDetailOpen}
        onCancel={() => setNotificationDetailOpen(false)}
        footer={
          selectedNotification?.actions ? (
            <div className="flex justify-end gap-2">
              {selectedNotification.actions.map((action, index) => (
                <Button 
                  key={index}
                  type={index === 0 ? "primary" : "default"}
                  onClick={() => handleActionClick(action, selectedNotification.id)}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          ) : (
            <Button onClick={() => setNotificationDetailOpen(false)}>
              {language === "ar" ? "إغلاق" : "Close"}
            </Button>
          )
        }
        width={600}
      >
        {selectedNotification && (
          <div>
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <ClockCircleOutlined />
                <Text type="secondary">
                  {formatFullDateTime(selectedNotification.time)}
                </Text>
              </div>
              
              <Paragraph className="text-lg">
                {selectedNotification.details || selectedNotification.message}
              </Paragraph>
            </div>
            
            {selectedNotification.metadata && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
                <Title level={5}>{language === "ar" ? "معلومات إضافية" : "Additional Information"}</Title>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(selectedNotification.metadata).map(([key, value]) => (
                    <div key={key}>
                      <Text strong>
                        {key === 'device' ? (language === "ar" ? "الجهاز" : "Device") :
                         key === 'location' ? (language === "ar" ? "الموقع" : "Location") :
                         key === 'ip' ? "IP" :
                         key === 'browser' ? (language === "ar" ? "المتصفح" : "Browser") :
                         key === 'currentUsage' ? (language === "ar" ? "الاستخدام الحالي" : "Current Usage") :
                         key === 'limit' ? (language === "ar" ? "الحد الأقصى" : "Limit") :
                         key === 'resetDate' ? (language === "ar" ? "تاريخ إعادة التعيين" : "Reset Date") :
                         key}:
                      </Text>
                      <div>{value}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </AntdHeader>
  );
}
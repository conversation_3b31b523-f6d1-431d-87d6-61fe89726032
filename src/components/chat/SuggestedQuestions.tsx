import React from 'react';
import { useTranslation } from '../../hooks/use-translation';
import { cn } from '../../lib/utils';

interface SuggestedQuestionsProps {
  onQuestionClick: (question: string) => void;
  className?: string;
}

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({ 
  onQuestionClick,
  className
}) => {
  const { t } = useTranslation();
  
  // الأسئلة المقترحة (في التطبيق الحقيقي ستأتي من API أو ملف الترجمة)
  const questions = [
    'ما هي شروط التعويض في حال الإخلال بالعقد؟',
    'ما خطوات تأسيس شركة ذات مسؤولية محدودة؟',
    'ما هي شروط رفع دعوى أمام ديوان المظالم؟',
    'هل يحق للمرأة فسخ النكاح بسبب الضرر؟'
  ];

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-4xl", className)}>
      {questions.map((question, index) => (
        <button
          key={index}
          onClick={() => onQuestionClick(question)}
          className="p-6 text-right bg-card border border-border rounded-2xl hover:border-primary hover:shadow-md transition-all duration-200 text-card-foreground text-base leading-relaxed hover:bg-accent/50"
        >
          {question}
        </button>
      ))}
    </div>
  );
};
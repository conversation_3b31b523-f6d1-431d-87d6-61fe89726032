import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from '../../hooks/use-translation';
import { cn } from '../../lib/utils';
import { Mi<PERSON>, Paperclip, Send, Loader2 } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string, attachments?: File[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
  isError?: boolean;
  placeholder?: string;
  maxLength?: number;
  onAttachFile?: () => void;
  onVoiceRecord?: () => void;
  supportedFileTypes?: string[];
  maxFileSize?: number;
  maxFiles?: number;
}

export const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false,
  isLoading = false,
  isError = false,
  placeholder,
  maxLength = 2000,
  onAttachFile,
  onVoiceRecord,
  supportedFileTypes = ['image/*', 'text/*', 'application/pdf'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 5
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const showCharacterCount = inputValue.length > maxLength * 0.7;
  const canSend = !disabled && !isLoading && (inputValue.trim().length > 0 || attachedFiles.length > 0);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && canSend) {
      e.preventDefault();
      handleSubmit(e);
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canSend) return;
    
    onSendMessage(inputValue, attachedFiles.length > 0 ? attachedFiles : undefined);
    setInputValue('');
    setAttachedFiles([]);
  };
  
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    
    const newFiles = Array.from(files).filter(file => {
      // التحقق من حجم الملف
      if (file.size > maxFileSize) {
        alert(`الملف ${file.name} كبير جدًا. الحد الأقصى هو ${maxFileSize / (1024 * 1024)} ميجابايت.`);
        return false;
      }
      return true;
    });
    
    // التحقق من عدد الملفات
    if (attachedFiles.length + newFiles.length > maxFiles) {
      alert(`يمكنك إرفاق ${maxFiles} ملفات كحد أقصى.`);
      return;
    }
    
    setAttachedFiles(prev => [...prev, ...newFiles]);
    
    // إعادة تعيين حقل الإدخال
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    if (onVoiceRecord) {
      onVoiceRecord();
    }
  };
  
  const handleRemoveFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  // التركيز على حقل الإدخال عند التحميل
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);
  
  return (
    <form onSubmit={handleSubmit} className="relative">
      {/* عرض الملفات المرفقة */}
      {attachedFiles.length > 0 && (
        <div className="mb-6 p-5 bg-muted/50 rounded-2xl border border-border">
          <div className="text-sm font-medium mb-3">الملفات المرفقة:</div>
          <div className="space-y-2">
            {attachedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Paperclip className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{file.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {(file.size / 1024).toFixed(1)} KB
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(index)}
                  className="p-1 text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-full transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* حقل الإدخال الرئيسي */}
      <div className="relative bg-card rounded-3xl shadow-md transition-all duration-300 border border-border hover:shadow-lg min-h-[120px]">
        {/* منطقة النص */}
        <div className="flex-1 px-6 pt-6 pb-4">
          <textarea
            ref={textareaRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder || t('chat.typeMessage')}
            disabled={disabled}
            maxLength={maxLength}
            className="w-full bg-transparent text-lg resize-none border-0 p-0 text-foreground focus:outline-none focus:ring-0 min-h-[80px]"
            style={{ maxHeight: '200px' }}
          />
        </div>
        
        {/* شريط الأدوات */}
        <div className="flex items-center justify-between px-6 py-4">
          {/* أزرار الإجراءات اليسرى */}
          <div className="flex items-center gap-3">
            {/* زر إرفاق الملفات */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled || attachedFiles.length >= maxFiles}
                className="p-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed"
                title={t('chat.file')}
              >
                <Paperclip className="w-5 h-5" />
              </button>
              <span className="text-xs text-muted-foreground">{t('chat.file')}</span>
            </div>
            
            {/* زر التسجيل الصوتي */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="button"
                onClick={handleVoiceRecord}
                disabled={disabled}
                className="p-3 rounded-xl transition-all duration-200 disabled:opacity-40 disabled:cursor-not-allowed"
                title={isRecording ? t('chat.stopRecording') : t('chat.voice')}
              >
                <Mic className="w-5 h-5" />
              </button>
              <span className="text-xs text-muted-foreground">
                {isRecording ? t('chat.stop') : t('chat.voice')}
              </span>
            </div>
          </div>
          
          {/* عداد الأحرف وزر الإرسال */}
          <div className="flex items-center gap-4">
            {/* عداد الأحرف */}
            {showCharacterCount && (
              <div className="text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full">
                {inputValue.length}/{maxLength}
              </div>
            )}
            
            {/* زر الإرسال */}
            <div className="flex flex-col items-center gap-1">
              <button
                type="submit"
                disabled={!canSend}
                className="p-3 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed shadow-sm bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md active:scale-95"
                title={isLoading ? t('chat.sending') : t('chat.send')}
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
              <span className="text-xs text-muted-foreground">
                {isLoading ? t('chat.sending') : t('chat.send')}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* حقل إدخال الملفات المخفي */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={supportedFileTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />
      
      {/* رسائل الحالة */}
      <div className="mt-4 space-y-3">
        {isError && (
          <div className="text-sm text-destructive flex items-start gap-3 bg-destructive/10 p-4 rounded-xl border border-destructive/20">
            <span>{t('chat.error')}</span>
          </div>
        )}
      </div>
    </form>
  );
};
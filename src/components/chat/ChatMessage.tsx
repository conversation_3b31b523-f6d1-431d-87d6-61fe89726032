import React, { useState } from 'react';
import { useTranslation } from '../../hooks/use-translation';
import { cn } from '../../lib/utils';
import { ThumbsUp, ThumbsDown, Copy, Undo, Share } from 'lucide-react';
import { parseTextWithCitations } from './legal-citation';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  tokens?: number;
  has_citations?: boolean;
}

interface ChatMessageProps {
  message: Message;
  isAnimating?: boolean;
  onRegenerate?: (messageId: string) => void;
  onLike?: (messageId: string) => void;
  onDislike?: (messageId: string) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  isAnimating = false,
  onRegenerate,
  onLike,
  onDislike
}) => {
  const { t, language } = useTranslation();
  const [copiedMessage, setCopiedMessage] = useState<string | null>(null);
  const [likedMessages, setLikedMessages] = useState<Set<string>>(new Set());
  const [dislikedMessages, setDislikedMessages] = useState<Set<string>>(new Set());
  const [isHovered, setIsHovered] = useState(false);
  
  const isBot = message.sender === 'bot';
  
  const handleCopy = (id: string, text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedMessage(id);
    
    setTimeout(() => {
      setCopiedMessage(null);
    }, 2000);
  };
  
  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate(message.id);
    }
  };
  
  const handleLike = (id: string) => {
    // إذا كانت الرسالة مُعجبة بالفعل، قم بإلغاء الإعجاب
    if (likedMessages.has(id)) {
      const newLiked = new Set(likedMessages);
      newLiked.delete(id);
      setLikedMessages(newLiked);
    } else {
      // إضافة إعجاب وإزالة عدم الإعجاب إن وجد
      const newLiked = new Set(likedMessages);
      newLiked.add(id);
      setLikedMessages(newLiked);
      
      const newDisliked = new Set(dislikedMessages);
      newDisliked.delete(id);
      setDislikedMessages(newDisliked);
    }
    
    if (onLike) {
      onLike(id);
    }
  };
  
  const handleDislike = (id: string) => {
    // إذا كانت الرسالة غير مُعجبة بالفعل، قم بإلغاء عدم الإعجاب
    if (dislikedMessages.has(id)) {
      const newDisliked = new Set(dislikedMessages);
      newDisliked.delete(id);
      setDislikedMessages(newDisliked);
    } else {
      // إضافة عدم إعجاب وإزالة الإعجاب إن وجد
      const newDisliked = new Set(dislikedMessages);
      newDisliked.add(id);
      setDislikedMessages(newDisliked);
      
      const newLiked = new Set(likedMessages);
      newLiked.delete(id);
      setLikedMessages(newLiked);
    }
    
    if (onDislike) {
      onDislike(id);
    }
  };
  
  const formatTime = (date: Date, lang: string) => {
    return new Intl.DateTimeFormat(lang === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  const renderMessageContent = (text: string, hasCitations?: boolean) => {
    if (hasCitations) {
      return parseTextWithCitations(text);
    }
    return text;
  };
  
  return (
    <div 
      className={`group mb-6 ${isAnimating ? 'animate-fade-in-up' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* حاوية الرسالة */}
      <div className="flex items-start gap-4">
        {/* الصورة الرمزية */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full flex items-center justify-center">
            {isBot ? (
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center shadow-md">
                <img src="/ShuraihAIUI.svg" alt="شُريح" className="w-6 h-6 logo-component" />
              </div>
            ) : (
              <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                <span className="text-muted-foreground text-sm font-medium">U</span>
              </div>
            )}
          </div>
        </div>
        
        {/* محتوى الرسالة */}
        <div className="flex-1 min-w-0">
          {/* الرأس */}
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium text-foreground text-sm">
              {isBot ? 'شُريح' : 'المستخدم'}
            </span>
            <span className="text-xs text-muted-foreground">
              {formatTime(message.timestamp, language)}
            </span>
          </div>
          
          {/* فقاعة الرسالة */}
          <div className="bg-card rounded-2xl p-5 shadow-sm border border-border hover:border-border/60 transition-colors chat-bubble">
            <div className="text-card-foreground text-base leading-relaxed whitespace-pre-line">
              {renderMessageContent(message.text, message.has_citations)}
            </div>
          </div>
          
          {/* شريط الأدوات */}
          <div className="flex items-center justify-between mt-3 px-1">
            {/* أزرار الإجراءات */}
            <div className={`flex items-center gap-1 transition-all duration-300 ease-in-out ${
              isHovered 
                ? 'opacity-100 translate-y-0' 
                : 'opacity-0 translate-y-2 pointer-events-none'
            }`}>
              {/* زر النسخ */}
              <button
                onClick={() => handleCopy(message.id, message.text)}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="نسخ"
              >
                <Copy className="h-4 w-4" />
              </button>
              
              {/* أزرار خاصة برسائل البوت */}
              {isBot && (
                <>
                  {/* إعادة التوليد */}
                  <button
                    onClick={handleRegenerate}
                    className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                    title="إعادة إنشاء"
                  >
                    <Undo className="h-4 w-4" />
                  </button>
                  
                  {/* إعجاب */}
                  <button
                    onClick={() => handleLike(message.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      likedMessages.has(message.id)
                        ? 'text-success bg-success/10 hover:bg-success/20'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                    title="إعجاب"
                  >
                    <ThumbsUp className="h-4 w-4" />
                  </button>
                  
                  {/* عدم إعجاب */}
                  <button
                    onClick={() => handleDislike(message.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      dislikedMessages.has(message.id)
                        ? 'text-destructive bg-destructive/10 hover:bg-destructive/20'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                    title="عدم إعجاب"
                  >
                    <ThumbsDown className="h-4 w-4" />
                  </button>
                </>
              )}
              
              {/* مشاركة */}
              <button
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                title="مشاركة"
              >
                <Share className="h-4 w-4" />
              </button>
            </div>
            
            {/* عداد التوكينز */}
            {isBot && message.tokens && (
              <div className={`text-xs text-muted-foreground font-medium transition-all duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-60'
              }`}>
                {message.tokens} توكين
              </div>
            )}
          </div>
        </div>
      </div>

      {/* إشعار النسخ */}
      {copiedMessage === message.id && (
        <div className="fixed bottom-4 left-4 bg-success text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in">
          <span className="text-sm">تم نسخ الرسالة ✓</span>
        </div>
      )}
    </div>
  );
};
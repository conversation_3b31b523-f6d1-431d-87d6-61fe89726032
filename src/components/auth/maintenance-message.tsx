import React from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Button } from 'antd';
import { ToolOutlined, MailOutlined } from '@ant-design/icons';
import { useTranslation } from '../../hooks/use-translation';

const { Title, Paragraph, Text } = Typography;

interface MaintenanceMessageProps {
  estimatedDuration?: string; // e.g. "2 hours", "30 minutes"
  contactEmail?: string;
  startTime?: string;
  endTime?: string;
}

export const MaintenanceMessage: React.FC<MaintenanceMessageProps> = ({
  estimatedDuration = "ساعتين / 2 hours",
  contactEmail = "<EMAIL>",
  startTime = "10:00 صباحاً",
  endTime = "12:00 ظهراً"
}) => {
  const { language } = useTranslation();
  
  return (
    <Card className="max-w-lg mx-auto shadow-md">
      <div className="text-center mb-6">
        <ToolOutlined className="text-4xl text-primary mb-4" />
        
        {/* Arabic Content */}
        <div className={language !== 'ar' ? 'mb-8' : ''} dir="rtl" lang="ar">
          <Title level={3} className="font-bold">تحديث نظام تسجيل الدخول</Title>
          
          <Paragraph className="text-lg mb-4">
            نعتذر عن الإزعاج، ولكن نظام تسجيل الدخول غير متاح مؤقتاً بسبب تحديثات النظام الجارية.
          </Paragraph>
          
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <Space direction="vertical" className="w-full">
              <Text strong>المدة المتوقعة:</Text>
              <Text>{estimatedDuration}</Text>
              
              <Text strong>وقت البدء:</Text>
              <Text>{startTime}</Text>
              
              <Text strong>وقت الانتهاء المتوقع:</Text>
              <Text>{endTime}</Text>
            </Space>
          </div>
          
          <Paragraph>
            يرجى التحقق مرة أخرى بعد {estimatedDuration}. إذا كنت بحاجة إلى مساعدة عاجلة، يرجى التواصل معنا عبر البريد الإلكتروني.
          </Paragraph>
          
          <Button type="primary" icon={<MailOutlined />} href={`mailto:${contactEmail}`}>
            تواصل معنا
          </Button>
        </div>
        
        {/* English Content */}
        {language !== 'ar' && (
          <div dir="ltr" lang="en" className="mt-8 pt-8 border-t">
            <Title level={3} className="font-bold">Login System Maintenance</Title>
            
            <Paragraph className="text-lg mb-4">
              We apologize for the inconvenience, but the login system is temporarily unavailable due to ongoing system updates.
            </Paragraph>
            
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <Space direction="vertical" className="w-full">
                <Text strong>Estimated Duration:</Text>
                <Text>{estimatedDuration}</Text>
                
                <Text strong>Start Time:</Text>
                <Text>{startTime}</Text>
                
                <Text strong>Expected Completion:</Text>
                <Text>{endTime}</Text>
              </Space>
            </div>
            
            <Paragraph>
              Please check back in {estimatedDuration}. If you need urgent assistance, please contact us via email.
            </Paragraph>
            
            <Button type="primary" icon={<MailOutlined />} href={`mailto:${contactEmail}`}>
              Contact Us
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  MessageSquare, 
  FileText, 
  Users, 
  Settings, 
  CreditCard, 
  FileCheck, 
  Flag, 
  BarChart, 
  Database, 
  X 
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  userRole?: string;
}

const Sidebar = ({ isOpen, onClose, userRole }: SidebarProps) => {
  const location = useLocation();
  
  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: Home, roles: ['admin', 'attorney', 'client'] },
    { path: '/chat', label: 'Chat', icon: MessageSquare, roles: ['admin', 'attorney', 'client'] },
    { path: '/citations', label: 'Citations', icon: FileText, roles: ['admin', 'attorney', 'client'] },
    { path: '/subscription', label: 'Subscription', icon: CreditCard, roles: ['admin', 'attorney', 'client'] },
    { path: '/invoices', label: 'Invoices', icon: FileCheck, roles: ['admin', 'attorney', 'client'] },
    { path: '/profile', label: 'Profile', icon: Users, roles: ['admin', 'attorney', 'client'] },
    { path: '/settings', label: 'Settings', icon: Settings, roles: ['admin', 'attorney', 'client'] },
    
    // Admin routes
    { path: '/admin', label: 'Admin Dashboard', icon: BarChart, roles: ['admin'] },
    { path: '/admin/users', label: 'Manage Users', icon: Users, roles: ['admin'] },
    { path: '/admin/chats', label: 'Manage Chats', icon: MessageSquare, roles: ['admin'] },
    { path: '/admin/citations', label: 'Manage Citations', icon: FileText, roles: ['admin'] },
    { path: '/admin/content-reports', label: 'Content Reports', icon: Flag, roles: ['admin'] },
    { path: '/admin/subscriptions', label: 'Subscriptions', icon: CreditCard, roles: ['admin'] },
    { path: '/admin/audit-log', label: 'Audit Log', icon: Database, roles: ['admin'] },
    { path: '/admin/settings', label: 'System Settings', icon: Settings, roles: ['admin'] },
  ];

  // Filter nav items based on user role
  const filteredNavItems = navItems.filter(item => 
    item.roles.includes(userRole || 'client')
  );

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden"
          onClick={onClose}
        ></div>
      )}

      {/* Sidebar */}
      <aside 
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-auto md:h-screen",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="h-full flex flex-col">
          {/* Sidebar header */}
          <div className="flex items-center justify-between px-4 py-5 border-b border-gray-200 dark:border-gray-700">
            <Link to="/dashboard" className="text-xl font-bold text-gray-800 dark:text-white">
              Legal Chat
            </Link>
            <button 
              onClick={onClose}
              className="md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white"
            >
              <X size={20} />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 overflow-y-auto">
            <ul className="space-y-1">
              {filteredNavItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors",
                      isActive(item.path)
                        ? "bg-primary text-white"
                        : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    )}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
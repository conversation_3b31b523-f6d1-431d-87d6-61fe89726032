export interface ChatSession {
  id: string;
  title: string;
  user_id: string;
  is_archived: boolean;
  is_flagged: boolean;
  created_at: string;
  updated_at: string;
  messages?: ChatMessage[];
  attachments?: ChatAttachment[];
  tags?: Tag[];
}

export interface ChatMessage {
  id: string;
  chat_session_id: string;
  sender_type: 'user' | 'system';
  content: string;
  created_at: string;
  is_flagged: boolean;
  citations?: CitationReference[];
}

export interface ChatAttachment {
  id: string;
  chat_session_id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: string;
  uploaded_at: string;
}

export interface Tag {
  id: string;
  name: string;
}

export interface CitationReference {
  id: string;
  citation_id: string;
  title: string;
  source: string;
  start_index?: number;
  end_index?: number;
}

export interface CreateChatSessionData {
  title: string;
}

export interface UpdateChatSessionData {
  title?: string;
  is_archived?: boolean;
  is_flagged?: boolean;
}

export interface CreateChatMessageData {
  content: string;
}
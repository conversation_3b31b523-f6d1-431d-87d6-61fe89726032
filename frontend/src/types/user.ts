export interface User {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'attorney' | 'client';
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at?: string;
  last_login?: string;
  profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  phone_number?: string;
  address?: string;
  bio?: string;
  avatar_url?: string;
  preferences?: string;
  created_at: string;
  updated_at?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  full_name: string;
}

export interface UpdateProfileData {
  full_name?: string;
  phone_number?: string;
  address?: string;
  bio?: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}
export interface Citation {
  id: string;
  title: string;
  content: string;
  source: string;
  source_url?: string;
  article_number?: string;
  section?: string;
  category?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  tags: string[];
}

export interface CreateCitationData {
  title: string;
  content: string;
  source: string;
  source_url?: string;
  article_number?: string;
  section?: string;
  category?: string;
  tags?: string[];
}

export interface UpdateCitationData {
  title?: string;
  content?: string;
  source?: string;
  source_url?: string;
  article_number?: string;
  section?: string;
  category?: string;
  tags?: string[];
}

export interface CitationImportData {
  citations: CreateCitationData[];
}
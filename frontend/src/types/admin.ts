export interface UsageReport {
  start_date: string;
  end_date: string;
  total_users: number;
  active_users: number;
  new_users: number;
  total_sessions: number;
  new_sessions: number;
  total_messages: number;
  new_messages: number;
  avg_messages_per_session: number;
}

export interface PerformanceReport {
  start_date: string;
  end_date: string;
  avg_response_time_ms: number;
  p95_response_time_ms: number;
  p99_response_time_ms: number;
  error_rate: number;
  uptime_percentage: number;
  api_calls_total: number;
  api_calls_per_day: number;
}

export interface FinancialReport {
  start_date: string;
  end_date: string;
  total_revenue: number;
  subscription_counts: {
    basic: number;
    premium: number;
    enterprise: number;
  };
  new_subscriptions: number;
  canceled_subscriptions: number;
  monthly_recurring_revenue: number;
  average_revenue_per_user: number;
}

export interface ContentReport {
  id: string;
  reporter_id?: string;
  reporter_name?: string;
  resource_type: string;
  resource_id: string;
  reason: string;
  description?: string;
  status: string;
  reviewed_by?: string;
  reviewer_name?: string;
  reviewed_at?: string;
  created_at: string;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  user_name?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address?: string;
  user_agent?: string;
  details?: Record<string, any>;
  created_at: string;
}

export interface SystemSettings {
  general: {
    site_name: string;
    maintenance_mode: boolean;
    registration_enabled: boolean;
  };
  security: {
    max_login_attempts: number;
    session_timeout_minutes: number;
    password_reset_expiry_hours: number;
  };
  chat: {
    max_message_length: number;
    max_file_size_mb: number;
    allowed_file_types: string[];
  };
  ai: {
    model_name: string;
    temperature: number;
    max_tokens: number;
  };
  payment: {
    currency: string;
    tax_percentage: number;
    payment_methods: string[];
  };
}
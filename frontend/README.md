# Legal Chat Platform Frontend

A React TypeScript frontend for a legal chat platform with authentication, real-time chat, legal citation management, and payment integration.

## Features

- User authentication and authorization
- Real-time chat with WebSockets
- Legal citation search and integration
- Payment processing with Moyasar
- E-invoicing with Wafeq
- Responsive design with Tailwind CSS
- Dark mode support
- Role-based access control

## Tech Stack

- React 18
- TypeScript
- Tailwind CSS
- React Query for data fetching
- React Hook Form for form validation
- React Router for routing
- Zod for schema validation
- Axios for API requests
- Jest and React Testing Library for testing

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/legal-chat-frontend.git
   cd legal-chat-frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Create a `.env` file:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file with your configuration.

4. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. The application will be available at http://localhost:5173

## Building for Production

```bash
npm run build
# or
yarn build
```

The build artifacts will be stored in the `dist/` directory.

## Testing

```bash
npm run test
# or
yarn test
```

## Project Structure

```
frontend/
├── public/                # Static assets
├── src/
│   ├── components/        # Reusable components
│   │   ├── auth/          # Authentication components
│   │   ├── chat/          # Chat components
│   │   ├── layouts/       # Layout components
│   │   └── ui/            # UI components
│   ├── contexts/          # React contexts
│   ├── hooks/             # Custom hooks
│   ├── lib/               # Utility functions
│   ├── pages/             # Page components
│   │   ├── admin/         # Admin pages
│   │   ├── auth/          # Auth pages
│   │   ├── chat/          # Chat pages
│   │   ├── dashboard/     # Dashboard pages
│   │   ├── payments/      # Payment pages
│   │   └── profile/       # Profile pages
│   ├── services/          # API services
│   ├── types/             # TypeScript types
│   ├── App.tsx            # Main App component
│   ├── index.css          # Global styles
│   └── main.tsx           # Entry point
├── .env.example           # Environment variables example
├── index.html             # HTML template
├── package.json           # Dependencies and scripts
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── vite.config.ts         # Vite configuration
└── README.md              # Project documentation
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.